package com.aisino.da.api.util;

import cn.hutool.core.util.ObjectUtil;
import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.*;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aosplus.session.AcsHelper;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.da.api.constant.OtherDatum;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.dao.OtherDatumCollectionDao;
import com.aisino.da.core.service.FileService;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName OtherDatumCollectionUtil
 * @Description 其他会计资料工具类
 * <AUTHOR>
 * @Date 2023/7/17 9:41
 */
public class OtherDatumCollectionUtil {

    // private final static OtherDatumCollectionDao otherDatumCollectionDao = new OtherDatumCollectionDaoImpl();

    /**
     * 获取同步归档信息服务
     * @return
     */
    public static Map<String,Object> getSyncArchiveInformationInfo(){
        Map<String,Object> map = new HashMap<>(1);
        List<String> objects = new ArrayList<>();
        objects.add(OtherDatum.QT_02);
        map.put("2",objects);
        Message message = new Message("da.fc.SyncArchiveInformationMs");
        Object publish = message.publish(map);
        if(ObjectUtil.isNotEmpty(publish)){
            return  (Map<String, Object> ) publish;
        } else {
            throw new BusinessException("同步归档设置中[银行对账单]同步归档按钮未启用，请启用后收集");
        }
    }

    /**
     * 赋值基本的字段值
     * @param map
     * @return 返回同步序列号
     */
    public static String makeDefalultValue(Map map){
        String corgnid = SessionHelper.getCurrentOrgnId();

        String corgnid_name = SessionHelper.getCurrentOrgnName();
        String cadminorgnid = SessionHelper.getCurrentAdminOrgnId();
        String cadminorgnid_name = SessionHelper.getCurrentAdminOrgnName();
        String ccreatedate = CollectionStringUtil.getSysDateTime();
        String ccreatorid = SessionHelper.getCurrentUserId();
        String ccreatorid_name = SessionHelper.getCurrentUserRealName();

        map.put("cguid", Guid.g());
        String g = Guid.g();
        map.put("cserialnumber",g);
        map.put("ibatchmnum",g);
        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate",ccreatedate);

        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);

        map.put("csourceorgnid", corgnid);
        map.put("csourceorgnid_name",corgnid_name);

        //取职员
        map.put("ccollector", AcsHelper.getAttribute("cempguid"));
        map.put("ccollector_name",AcsHelper.getAttribute("cempname"));
        return g;
    }

    /**
     * 解析影像文件,获取文件流
     * @param statementInfos
     */
    public static String parseImageFile(List<Map> statementInfos,OtherDatumCollectionDao otherDatumCollectionDao) {
        Params fileParam = new Params();
        for (Map map : statementInfos) {
            // 影像id
            String imageno = map.get("imageno")+"";
            // 根据影像id查询文件信息
            Map<String, Object> fileMap = otherDatumCollectionDao.queryFileInfoById(imageno);
            if(MapUtil.isNotEmpty(fileMap)){
                String cfilename = CollectionUtil.getStringFromMap(fileMap, "cfilename");
                try{
                    Message msg = new Message("aps.logic.getFileInputStream");
                    fileParam.put("cfilename",cfilename);
                    InputStream fileInputStream = (InputStream)msg.publish(fileParam);
                    if(ObjectUtil.isEmpty(fileInputStream)){
                        return null;
                    }
                    map.put("file", fileInputStream);
                    map.put("fileInfo",fileMap);
                }catch (Exception e){
                    map.put("file",null);
                }
            }else {
                map.put("file",null);
            }
        }
        return "111";
    }

    /**
     * 校验元数据内容是否合法
     */
    public static String checkMetaData(Map<String,Object> map) {
        //String accountNo = map.get("accountNo")+"";
        StringBuilder strMsg = new StringBuilder();
        String accountname = CollectionUtil.getStringFromMap(map, "accountname");
        String year = CollectionUtil.getStringFromMap(map, "year").replace("年", "");
        String month = CollectionUtil.getStringFromMap(map, "month").replace("月", "");
        if(OtherDatumCollectionUtil.isNull(accountname)){
            strMsg.append("账户名称为空;");
        }
        if(OtherDatumCollectionUtil.isNull(year)){
            strMsg.append("年份为空;");
        }
        if(OtherDatumCollectionUtil.isNull(month)){
            strMsg.append("月份为空;");
        }
        if(accountname.length()>OtherDatum.ACCOUNT_NAME_LEN){
            strMsg.append("账户名称长度超长;");
        }
        if(year.length()>OtherDatum.YEAR_LEN){
            strMsg.append("年份长度超长");
        }
        if(month.length()>OtherDatum.MONTH_LEN){
            strMsg.append("月份长度超长");
        }
        return strMsg.toString();
    }

    /**
     * 组装其他会计资料的对账单数据
     */
    public static List<Map> assembleStatementData(Map<String, List<Map>> legalMetadataMap,Map<String,Object> logMap,
                                                  OtherDatumCollectionDao otherDatumCollectionDao,List<Map> tableList,
                                                  boolean fign) {
        List<Map> resultList = new ArrayList<>();
        // 来源系统名称
        Object csourcesysname = logMap.get("csourcesysname");
        Object csourcesyscode = logMap.get("csourcesyscode");
        // 来源组织id
        Object csourceorgnid = logMap.get("csourceorgnid");
        // 来源组织名称
        Object csourceorgnidName = logMap.get("csourceorgnid_name");
        // 日志主键id
        Object logGuid = logMap.get("cguid");
        // 收集日志的序列号
        Object cserialnumber = logMap.get("cserialnumber");
        // 获取明细id
        Map<String, Object> qtMap = otherDatumCollectionDao.queryDetailClassByCode(OtherDatum.QT_02);
        Object cdetailclassguid = CollectionUtil.getStringFromMap(qtMap, "cguid");
        legalMetadataMap.forEach((key,legalMetadataList) -> {
            String[] split = key.split("-");
            String year = split[0].replace("年", "");
            String month = split[1].replace("月", "");
            String cperiod = year + month;
            Map<String,Object> dataMap = new HashMap<>(40);
            // 赋值基本的字段
            OtherDatumCollectionUtil.makeDefalultValue(dataMap);
            // 拼接四性检测状态唯一标识
            dataMap.put("cbusinessprimarykeystr",csourceorgnid+"!"+cperiod+"!"+cdetailclassguid+"!"+csourcesysname);
            dataMap.put("cdatasource",csourcesyscode);
            dataMap.put("csourcesysname",csourcesysname);
            dataMap.put("ieastatus","0");
            dataMap.put("ceastatus","收集成功");
            dataMap.put("centityfilestatus_name","收集成功");
            dataMap.put("centityfilestatus","0");
            dataMap.put("isuaistatus","1");
            dataMap.put("csuaistatus","已检测");
            dataMap.put("cperioddate",year+"-"+month);
            dataMap.put("cperioddatelist", cperiod);
            dataMap.put("csourceorganid",csourceorgnid);
            dataMap.put("csourceorganname",csourceorgnidName);
            dataMap.put("ccollectdate",OtherDatumCollectionUtil.getSysDate());
            dataMap.put("ccollecttime",OtherDatumCollectionUtil.getSysTime());
            dataMap.put("creportname",cperiod+"-"+OtherDatum.QT_02_NAME);
            dataMap.put("cdetailclassguid_name",OtherDatum.QT_02_NAME);
            dataMap.put("cdetailclassguid",cdetailclassguid);
            dataMap.put("cdetailclasscode",OtherDatum.QT_02);
            dataMap.put("cdetailclassname",OtherDatum.QT_02_NAME);
            /*dataMap.put("cbustypeguid","cbustypeguid");
            dataMap.put("cbustypecode","cbustypecode");
            dataMap.put("cbustypename","cbustypename");*/
            dataMap.put("ctemplateid","da_fc_otheraccountreport_list_template");
            dataMap.put("cpageid","da_fc_otheraccountreport_list");
            dataMap.put("cyear",year);
            dataMap.put("cmonth",month);
            dataMap.put("cfiles_table_name","da_files_"+year);
            dataMap.put("ccollectorid", AcsHelper.getAttribute("cempguid"));
            dataMap.put("ccollectorid_name",AcsHelper.getAttribute("cempname"));
            dataMap.put("csourcebatchid",logGuid);
            dataMap.put("cserialnumber",cserialnumber);
            boolean isfile = false;
            StringBuilder msg = new StringBuilder();
            // 列表主键
            Object cguid = dataMap.get("cguid");
            Optional<Map> tableMapOpt = tableList.stream().filter(tableMap -> CollectionUtil.getStringFromMap(tableMap, "cperioddatelist").equals(cperiod)).findFirst();
            if(tableMapOpt.isPresent() && fign){
                cguid = tableMapOpt.get().get("cguid");
            }
            // 循环元数据列表，处理一些字段
            for (Map<String,Object> metadata : legalMetadataList) {
                // 处理列表是否存在文件
                if(ObjectUtil.isNotEmpty(metadata.get("file")) && !isfile){
                    isfile = true;
                }
                // 处理错误的对账单元数据校验不通过的消息msg
                Object msg1 = metadata.get("msg");
                if(ObjectUtil.isNotEmpty(msg1)){
                    msg.append(msg1);
                }
                metadata.put("clistguid",cguid);
            }
            if(isfile){
                dataMap.put("isexistfile","1");
            }else {
                dataMap.put("isexistfile","0");
            }
            dataMap.put("msg",msg.toString());
            resultList.add(dataMap);
        });
        return resultList;
    }

    /**
     * 组装其他会计资料的对账单元数据
     */
    public static List<Map> assembleStatementMetadata(List<Map> dataList,Map<String,Object> logMap) {
        List<Map> resultList = new ArrayList<>();
        // 来源组织id
        Object csourceorgnid = logMap.get("csourceorgnid");
        // 来源组织名称
        Object csourceorgnidName = logMap.get("csourceorgnid_name");
        dataList.forEach(map -> {
            // 客户账号
            Object accountNo = map.get("accountNo");
            // 客户名称
            Object accountname = map.get("accountname");
            Object cyear = CollectionUtil.getStringFromMap(map,"year").replace("年","");
            Object cmonth = CollectionUtil.getStringFromMap(map,"month").replace("月","");
            // 文件
            Object file = map.get("file");
            // 文件信息
            Object fileInfo = map.get("fileInfo");
            // 该参数来源于对账单数据组装当中
            Object clistguid = map.get("clistguid");
            // 以下参数对方系统没有传
            Object fbnum = map.get("fbnum");
            Object issuer = map.get("issuer");
            Object currency = map.get("currency");
            Object clientno = map.get("clientno");
            Object printnum = map.get("printnum");
            Object printdate = map.get("printdate");
            Map<String,Object> dataMap = new HashMap<>(20);
            dataMap.put("accountno",accountNo);
            dataMap.put("accountname",accountname);
            dataMap.put("cyear",cyear);
            dataMap.put("cmonth",cmonth);
            dataMap.put("fbnum",fbnum);
            dataMap.put("issuer",issuer);
            dataMap.put("currency",currency);
            dataMap.put("clientno",clientno);
            dataMap.put("printnum", printnum);
            dataMap.put("printdate",printdate);
            dataMap.put("csourceorganname",csourceorgnidName);
            dataMap.put("csourceorganid",csourceorgnid);
            dataMap.put("clistguid",clistguid);
            dataMap.put("file",file);
            dataMap.put("fileInfo",fileInfo);
            // 补充基本数据字段
            makeDefalultValue(dataMap);
            resultList.add(dataMap);
        });
        return resultList;
    }

    /**
     * 组装其他会计资料的原始文件数据
     * @param legalMetadataMap
     * @param logMap
     * @return
     */
    public static Map<String, List<Map>> assembleOriginalFile(Map<String, List<Map>> legalMetadataMap,Map<String,Object> logMap) {
        Map<String, List<Map>> returnMap = new HashMap<>(legalMetadataMap.size());
        // 来源组织id
        Object csourceorgnid = logMap.get("csourceorgnid");
        // 来源组织名称
        Object csourceorgnidName = logMap.get("csourceorgnid_name");
        // 来源系统名称
        Object extcode = logMap.get("extcode");
        // 收集日志的序列号
        Object cserialnumber = logMap.get("cserialnumber");
        legalMetadataMap.forEach((key, metadataList) ->{
            String year = key.replace("年","");
            List<Map> resultList = new ArrayList<>();
            metadataList.forEach(metadata ->{
                // 月
                String cmonth = metadata.get("cmonth") + "";
                // 列表id
                String clistguid = metadata.get("clistguid") + "";
                // 文件
                Object file = metadata.get("file");
                // 获取文件信息，文件名和文件真实名
                Map<String,Object> fileInfo = metadata.get("fileInfo") != null ? (Map<String,Object>)metadata.get("fileInfo"): new HashMap<>(1);
                Object cfilename = fileInfo.get("cfilename");
                Object cfilerealname = fileInfo.get("cfilerealname");
                // 文件MD5码
                Object md5 = fileInfo.get("md5");
                // 月份
                String month = CollectionUtil.getStringFromMap(metadata, "cmonth");
                // 组装新的文件信息
                Map<String,Object> newFile = new HashMap<>(20);
                // 赋值最基本的字段
                makeDefalultValue(newFile);
                newFile.put("storagemethod","aliyunoss");
                newFile.put("file",file);
                newFile.put("dxccname","xmabcbackoss");
                newFile.put("cfiles_table_name","da_files_"+year);
                newFile.put("clistguid",clistguid);
                newFile.put("cbusinesstype",OtherDatum.QT);
                newFile.put("cfilesourcetype","8");
                newFile.put("filename",cfilename);
                // 获取文件类型
                String imageFile = DaApiProFileUtil.isImageFile(cfilerealname + "");
                newFile.put("carriertype",imageFile);
                newFile.put("cfiletype","1");
                newFile.put("cfilerealname",cfilerealname);
                newFile.put("mdnumber",md5);
                newFile.put("datainterval",year+"-"+cmonth);
                // todo 由于银行对账单收集对方系统没传文件信息，由于是内部系统收集，根据影像id查询平台文件表，可以查询到文件信息
                // todo 平台文件加密暂时是md5，后续平台可能会修改加密方式？sm3？
                newFile.put("chxtype","md5");
                // 档案上传对象存储服务器的地址:来源系统编码/组织名称/会计期间/其他会计资料/银行对账单
                String path = extcode+"/"+csourceorgnidName+"/"+key+month+"/"+"其他会计资料"+"/"+"银行对账单";
                newFile.put("dafilepath",path);
                // 流水号
                newFile.put("cserialnumber",cserialnumber);
                resultList.add(newFile);
            });
            returnMap.put(key,resultList);
        });
        return returnMap;
    }

    /**
     * 上传原始文件到对象存储服务器，并再对应的文件分表记录文件数据信息
     * @param originalMetadataMap 文件信息
     * @param otherDatumCollectionDao 数据库调用方法
     * @param daGetBillDetialDao 数据库调用方法
     */
    public static void uploadOriginalFile(Map<String, List<Map>> originalMetadataMap, OtherDatumCollectionDao otherDatumCollectionDao, DaGetBillDetialDao daGetBillDetialDao){
        originalMetadataMap.forEach((key,originalMetadataList) -> {
            originalMetadataList.forEach(originalMetadata ->{
                // 文件路径
                String dafilepath = CollectionUtil.getStringFromMap(originalMetadata, "dafilepath");
                // 文件名称
                String filename = CollectionUtil.getStringFromMap(originalMetadata, "filename");
                InputStream file = (InputStream)originalMetadata.get("file");
                // 计算文件大小
                long size = OtherDatumCollectionUtil.calculateFileSize(file);
                originalMetadata.put("cfilessize", size);
                Map<String,Object> fileMap = FileService.uploadFile(new Params(), dafilepath+"/"+filename, file);
                String cstatus = CollectionUtil.getStringFromMap(fileMap, "cstatus");
                if("1".equals(cstatus)){
                    originalMetadata.put("cstatus", "1");
                    originalMetadata.put("cstatus_name", "收集成功");
                    //map.put("storagemethod", "default");
                }else {
                    originalMetadata.put("cstatus", "2");
                    originalMetadata.put("cstatus_name", "收集失败");
                    String message = CollectionUtil.getStringFromMap(fileMap, "message");
                    originalMetadata.put("msg", message);
                }
            });
            String daFileTableName = daGetBillDetialDao.getDaFileTableName(key);
            otherDatumCollectionDao.batchInsertOriginalFile(daFileTableName,originalMetadataList);
        });
    }


    /**
     * 四性检测校验(针对文件进行检查)
     * @param masterDataList
     * @param fileDataMap
     * @return
     */
    public static Object fourCheck(List<Map> masterDataList, Map<String, List<Map>> fileDataMap,Map<String,Object> logMap){
        // 将多个文件信息合并成一个List<Map> 用于过滤数据，找到与列表对应的文件
        List<Map> fileList = fileDataMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        // 将列表数据与文件数据，建立关系，用于发送请求;
        for (Map<String,Object> masterMap : masterDataList) {
            // 列表主键
            String cguid = CollectionUtil.getStringFromMap(masterMap, "cguid");
            List<Map> clistguid = fileList.stream().filter(fileMap -> cguid.equals(fileMap.get("clistguid"))).collect(Collectors.toList());
            // 文件信息
            masterMap.put("fileList",clistguid);
        }
        // 调用四性检测服务，获取检测结果
        Object obj = OtherDatumCollectionUtil.callFourService(masterDataList, logMap);
        if(obj instanceof List){
            // 解析检测结果
            List<Map> fourResultList = (List) obj;
            // 获取四性检测错误的结果map
            List<Map> fourResultErrorList = fourResultList.stream().filter(fourResult -> !CollectionUtil.getBoolean(fourResult, "pass", true)).collect(Collectors.toList());
            fourResultErrorList.forEach(fourResultError ->{
                // 检测结果
                String msg = CollectionUtil.getStringFromMap(fourResultError, "msg");
                String cbusinessprimarykeystr = CollectionUtil.getStringFromMap(fourResultError, "cbusinessprimarykeystr");
                // 根据四性唯一标识字段找到对应的列表数据，添加四性检测结果字段
                Optional<Map> masterDataOpt = masterDataList.stream().filter(map -> CollectionUtil.getStringFromMap(map, "cbusinessprimarykeystr").equals(cbusinessprimarykeystr)).findFirst();
                if(masterDataOpt.isPresent()) {
                    Map masterDataMap = masterDataOpt.get();
                    masterDataMap.put("cfournessresult",msg);
                }
            });
        }
        return obj;
    }

    /**
     * 调用四性检测服务
     * @param masterData
     * @param logMap
     * @return
     */
    public static Object callFourService(List<Map> masterData,Map<String,Object> logMap){
        Map<String,Object> param = new HashMap<>(10);
        param.put("caccountclass","QT");
        param.put("dsid",CollectionUtil.getStringFromMap(logMap,"dsid"));
        param.put("currentOrgnId",CollectionUtil.getStringFromMap(logMap,"csourceorgnid"));
        param.put("currentOrgnName",CollectionUtil.getStringFromMap(logMap,"orgnName"));
        param.put("currentAdminOrgnId",CollectionUtil.getStringFromMap(logMap,"adminOrgnId"));
        param.put("currentAdminOrgnName",CollectionUtil.getStringFromMap(logMap,"adminOrgnName"));
        param.put("currentUserId",CollectionUtil.getStringFromMap(logMap,"userId"));
        param.put("currentUserName",CollectionUtil.getStringFromMap(logMap,"userName"));
        param.put("list",masterData);
        Message ms = new Message("da.suai.precheck");
        Map rtn = (Map)ms.publish(param);
        if(MapUtil.isNotEmpty(rtn)){
            List<Map> suairesult = (List)rtn.get("suairesult");
            if(CollectionUtil.isNotEmpty(suairesult)){
                return suairesult;
            }else {
                return "调用四性检测服务为空";
            }
        }
        return "调用四性检测服务为空";
    }

    /**
     * 删除四性检测不通过的元数据和文件信息
     * @param errList 列表数据
     * @param metadataList  元数据
     * @param fileDataMap 文件信息
     * @return
     */
    public static void deleteMetadataAndFile(List<Map> errList,List<Map> metadataList,Map<String, List<Map>> fileDataMap){
        if(CollectionUtil.isNotEmpty(errList)){
            errList.forEach(errMap ->{
                // 列表主键
                String cguid = CollectionUtil.getStringFromMap(errMap, "cguid");
                // 元数据删除
                metadataList.removeIf(metaMap -> cguid.equals(CollectionUtil.getStringFromMap(metaMap, "clistguid")));
                // 文件删除
                fileDataMap.forEach((key,values) ->{
                    values.removeIf(metaMap -> cguid.equals(CollectionUtil.getStringFromMap(metaMap, "clistguid")));
                });
            });
        }
    }

    /**
     * 判断一个值是否不为空
     * @param value
     * @return
     */
    public static boolean isNull(String value){
        return StringUtil.isEmpty(value) || "null".equals(value);
    }


    /**
     * 获取系统日期
     * @return
     */
    public static String getSysDate(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd");
        String time =s.format(date);
        return time;
    }

    /**
     * 获取系统日期时间
     * @return
     */
    public static String getSysTime(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time =s.format(date);
        return time;
    }

    /**
     * 返回状态和消息
     * @param code
     * @param msg
     * @return
     */
    public static Map<String,Object> returnStats(String code,String msg){
        Map<String, Object> returnMap = new HashMap<>(2);
        returnMap.put("code", code);
        returnMap.put("msg", msg);
        return returnMap;
    }

    /**
     * 计算文件大小
     * @param inputStream
     * @return
     */
    public static long calculateFileSize(InputStream inputStream){
        try {
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteStream.write(buffer, 0, bytesRead);
            }

            byte[] data = byteStream.toByteArray();
            return data.length;
        } catch (IOException e) {
            e.printStackTrace();
            return 0;
        }
    }


}
