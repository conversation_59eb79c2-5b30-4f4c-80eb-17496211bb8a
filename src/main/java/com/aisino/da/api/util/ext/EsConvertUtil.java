package com.aisino.da.api.util.ext;

import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.da.api.util.ArrayUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EsConvertUtil
{
    static HashMap<String, String> COMMON_MAP = new HashMap<>();
    static HashMap<String, String> BANK_MAP = new HashMap<>();
    static HashMap<String, String> TICKET_HEAD_MAP = new HashMap<>();
    static HashMap<String, String> TICKET_BODY_MAP = new HashMap<>();

    static List<String> BANK_LIST = new ArrayList<>();

    static List<String> TICKET_HEAD_LIST = new ArrayList<>();
    static List<String> TICKET_BODY_LIST = new ArrayList<>();

    static 
    {
        //参与转换的公共字段
        for (int i = 1; i <= 50; i++) {
            COMMON_MAP.put("标准扩展字段" + i, "column" + i);
            BANK_LIST.add("column" + i);
            TICKET_HEAD_LIST.add("column" + i);
            TICKET_BODY_LIST.add("column" + i);
        }

        BANK_MAP.put("回单编号","ctransactionnum");
        BANK_MAP.put("交易时间","ctransactiondate");
        BANK_MAP.put("本方户名","caccountname");
        BANK_MAP.put("本方账户","cbankaccount");
        BANK_MAP.put("交易金额","itransactionamount");
        BANK_MAP.put("币种","curcode");
        BANK_MAP.put("本方银行","cbank");
        BANK_MAP.put("对方户名","ccounteraccname");
        BANK_MAP.put("对方账户","ccounteraccount");
        BANK_MAP.put("对方银行","ccounterbank");
        BANK_MAP.put("摘要","cremark");
        BANK_MAP.put("用途","cpurpose");
        BANK_MAP.put("附言","cpostscript");
        BANK_MAP.put("交易流水号","bankserial");


        TICKET_HEAD_MAP.put("乘车人", "passenger");
        TICKET_HEAD_MAP.put("价税合计", "itotal");
        TICKET_HEAD_MAP.put("总金额", "iamt");
        TICKET_HEAD_MAP.put("开票日期", "ddate");
        TICKET_HEAD_MAP.put("发票代码", "ccode");
        TICKET_HEAD_MAP.put("发票号码", "cnumber");
        TICKET_HEAD_MAP.put("发票类型", "ctype");
        TICKET_HEAD_MAP.put("发票大类", "cclass");
        TICKET_HEAD_MAP.put("备注", "cremark");
        TICKET_HEAD_MAP.put("复核人", "review");
        TICKET_HEAD_MAP.put("收款人", "iclerk");
        TICKET_HEAD_MAP.put("机器码", "machinecode");
        TICKET_HEAD_MAP.put("省份", "province");
        TICKET_HEAD_MAP.put("销售方开户行及账户", "cseller_bank_account");
        TICKET_HEAD_MAP.put("销售方地址电话", "cseller_addr_tel");
        TICKET_HEAD_MAP.put("销售方纳税人识别号", "csellertaxid");
        TICKET_HEAD_MAP.put("销售方名称", "csellername");
        TICKET_HEAD_MAP.put("查验时间", "dcheckdate");
        TICKET_HEAD_MAP.put("购买方开户行及账户", "cbuyer_bank_account");
        TICKET_HEAD_MAP.put("购买方地址电话", "cbuyer_addr_tel");
        TICKET_HEAD_MAP.put("购买方纳税人识别号", "cbuyertaxid");
        TICKET_HEAD_MAP.put("购买方名称", "cbuyername");
        TICKET_HEAD_MAP.put("查验状态", "ccheckresult");


        TICKET_BODY_MAP.put("开票明细","goodsname");
        TICKET_BODY_MAP.put("规格型号","specifications");
        TICKET_BODY_MAP.put("单位","measureunits");
        TICKET_BODY_MAP.put("数量","numbers");
        TICKET_BODY_MAP.put("单价","notaxprice");
        TICKET_BODY_MAP.put("不含税金额","goodsamount");
        TICKET_BODY_MAP.put("税率","taxrate");
        TICKET_BODY_MAP.put("税额","goodstaxamount");


        BANK_LIST.add("cremark");
        BANK_LIST.add("ccounterbank");
        BANK_LIST.add("ccounteraccname");
        BANK_LIST.add("ccounteraccount");
        BANK_LIST.add("ctransactionnum");
        BANK_LIST.add("caccountname");
        BANK_LIST.add("cbankaccount");
        BANK_LIST.add("cbank");
        BANK_LIST.add("ctransactiondate");
        BANK_LIST.add("datasource");
        BANK_LIST.add("itransactionamount");
        BANK_LIST.add("bankserial");
        BANK_LIST.add("csztype");
        BANK_LIST.add("cpurpose");
        BANK_LIST.add("curcode");
        BANK_LIST.add("cbankac");
        BANK_LIST.add("cbankac_name");
        BANK_LIST.add("cbankaccount_name");
        BANK_LIST.add("cpostscript");
        BANK_LIST.add("amount_words");


        TICKET_HEAD_LIST.add("passenger");
        TICKET_HEAD_LIST.add("itotal");
        TICKET_HEAD_LIST.add("iamt");
        TICKET_HEAD_LIST.add("ddate");
        TICKET_HEAD_LIST.add("ccode");
        TICKET_HEAD_LIST.add("cnumber");
        TICKET_HEAD_LIST.add("ctype");
        TICKET_HEAD_LIST.add("cclass");
        TICKET_HEAD_LIST.add("cremark");
        TICKET_HEAD_LIST.add("review");
        TICKET_HEAD_LIST.add("iclerk");
        TICKET_HEAD_LIST.add("machinecode");
        TICKET_HEAD_LIST.add("province");
        TICKET_HEAD_LIST.add("cseller_bank_account");
        TICKET_HEAD_LIST.add("cseller_addr_tel");
        TICKET_HEAD_LIST.add("csellertaxid");
        TICKET_HEAD_LIST.add("csellername");
        TICKET_HEAD_LIST.add("dcheckdate");
        TICKET_HEAD_LIST.add("cbuyer_bank_account");
        TICKET_HEAD_LIST.add("cbuyer_addr_tel");
        TICKET_HEAD_LIST.add("cbuyertaxid");
        TICKET_HEAD_LIST.add("cbuyername");
        TICKET_HEAD_LIST.add("checkresult");
        TICKET_HEAD_LIST.add("ext_column1");
        TICKET_HEAD_LIST.add("ext_column2");
        TICKET_HEAD_LIST.add("ext_column3");
        TICKET_HEAD_LIST.add("ext_column4");
        TICKET_HEAD_LIST.add("ext_column5");

        TICKET_BODY_LIST.add("taxrate");
        TICKET_BODY_LIST.add("vehicletype");
        TICKET_BODY_LIST.add("transitdatestart");
        TICKET_BODY_LIST.add("transitdateend");
        TICKET_BODY_LIST.add("licenseplateno");
        TICKET_BODY_LIST.add("goodstaxamount");
        TICKET_BODY_LIST.add("goodsname");
        TICKET_BODY_LIST.add("notaxprice");
        TICKET_BODY_LIST.add("numbers");
        TICKET_BODY_LIST.add("measureunits");
        TICKET_BODY_LIST.add("specifications");
        TICKET_BODY_LIST.add("goodsamount");
    }

    public static StringBuffer getBankCfilecontentsBySJ(Map dataMap)
    {
        if (dataMap == null || dataMap.isEmpty()) {
            return new StringBuffer();
        }

        Map<String, String> nbankMap = new HashMap<>(COMMON_MAP);
        nbankMap.putAll(BANK_MAP);

        return buildContentString(dataMap, nbankMap);
    }


    public static StringBuffer getTicketCfilecontentsBySJ(Map headMap, List<Map> bodyList)
    {
        StringBuffer sb = new StringBuffer();
        if (headMap != null && !headMap.isEmpty()) {
            Map<String, String> combinedHeadMap = new HashMap<>(COMMON_MAP);
            combinedHeadMap.putAll(TICKET_HEAD_MAP);
            sb.append(buildContentString(headMap, combinedHeadMap));
        }

        if (CollectionUtil.isNotEmpty(bodyList)) {
            Map<String, String> combinedBodyMap = new HashMap<>(COMMON_MAP);
            combinedBodyMap.putAll(TICKET_BODY_MAP);

            for (Map bodyMap : bodyList) {
                sb.append(buildContentString(bodyMap, combinedBodyMap));
            }
        }
        return sb;
    }


    private static StringBuffer buildContentString(Map<String, Object> dataMap, Map<String, String> fieldMap) {
        StringBuffer sb = new StringBuffer();

        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
            String key = entry.getKey();
            Object value = dataMap.get(key);

            if (!ArrayUtil.isObjectEmpty(value)) {
                sb.append(value).append(" ");
            }
        }
        return sb;
    }

    public static String getBankContents(Map datamap)
    {
        if (datamap == null ) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        appendValues(sb, datamap, BANK_LIST);

        return sb.toString();
    }

    public static void appendValues(StringBuilder sb, Map datamap, List<String> columns) {
        if (datamap == null || columns == null || columns.isEmpty()) {
            return;
        }

        for (String column : columns) {
            Object value = datamap.get(column);
            if (value != null && StringUtils.isNotEmpty(value.toString())) {
                sb.append(value).append(" ");
            }
        }
    }

    public static String getTicketContents(Map datamap, List<Map> bodylist)
    {
        if (datamap == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        appendValues(sb, datamap, TICKET_HEAD_LIST);

        //查询发票明细
        if (CollectionUtil.isNotEmpty(bodylist))
        {
            for (Map bodymap : bodylist)
            {
                appendValues(sb, bodymap, TICKET_BODY_LIST);
            }
        }
        return sb.toString();
    }
}
