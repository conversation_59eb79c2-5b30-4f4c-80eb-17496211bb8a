package com.aisino.da.api.util.ext;

import com.aisino.aosplus.core.ConfigHelper;
import com.aisino.da.api.entity.EsAddViewVO;
import com.aisino.da.api.entity.EsDeleteViewVO;
import com.aisino.da.api.entity.EsResultViewVO;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.common.service.mq.EsMQProducer;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Es处理工具类
 */
public class ESUtil
{

    private static final boolean  ENABLEFTR = ConfigHelper.getBoolean("enableFTR", false);

    /**
     * 删除历史的se信息
     * @param filecguidlist
     * @param type 1:单据 2:发票 3:回单
     */
    public static void deleteSeDetial(List<String> filecguidlist, String type)
    {
        if(ENABLEFTR)
        {
            Map esdeletmap = new HashMap<>();
            esdeletmap.put("operate", "del");
            esdeletmap.put("filetype", type);
            esdeletmap.put("data", filecguidlist);
            EsMQProducer.sendMsg(esdeletmap);
        }
    }

    /**
     * 增加新的se信息
     * @param ofileList
     * @param dsid
     * @param type 1:单据 2:发票 3:回单
     */
    public static void addSeDetial(List<Map> ofileList, String dsid, String type)
    {
        if(ENABLEFTR)
        {
            List<Map> filelist = new ArrayList<>();
            ofileList.forEach(filemap -> {
                Map nmap = new HashMap<>();
                nmap.put("cfileid", filemap.get("cguid"));
                nmap.put("corgnid", filemap.get("corgnid"));
                nmap.put("corgnid_name", filemap.get("corgnid_name"));
                nmap.put("cfilenum", filemap.get("cfilenum"));
                nmap.put("cqznum", filemap.get("cqznum"));
                nmap.put("caccountclassname", filemap.get("caccountclassname"));
                nmap.put("caccountclasscode", filemap.get("caccountclasscode"));
                nmap.put("cdetailclassname", filemap.get("cdetailclassname"));
                nmap.put("cdetailclasscode", filemap.get("cdetailclasscode"));
                nmap.put("ccustody_year_name", filemap.get("ccustody_year_name"));
                nmap.put("cfilecontents", filemap.get("cfilecontents"));
                nmap.put("cfilerealname", filemap.get("cfilerealname"));
                nmap.put("cfiles_table_name", filemap.get("cfiles_table_name"));
                nmap.put("dafilepath", filemap.get("dafilepath"));
                nmap.put("filename", filemap.get("filename"));
                filelist.add(nmap);
            });
            Map esdeletmap = new HashMap<>();
            esdeletmap.put("operate", "add");
            esdeletmap.put("filetype", type);
            esdeletmap.put("data", filelist);
            esdeletmap.put("dsid", dsid);
            EsMQProducer.sendMsg(esdeletmap);
        }
    }

    /**
     * 凭证+单据情况 或者多批次单据 需要由主单据或者。。凭证调用处理单据部分es
     * @param esResultViewVO
     * @param dsid
     */
    public static void addBillEs(EsResultViewVO esResultViewVO, String dsid)
    {
        List<EsAddViewVO> esAddViewVOList = esResultViewVO.getEsAddViewVOList();
        List<EsDeleteViewVO> esDeleteViewVOList = esResultViewVO.getEsDeleteViewVOList();
        String msg = esResultViewVO.getMsg();
        if (StringUtils.isBlank(msg))
        {
            if (!ArrayUtil.isObjectEmpty(esAddViewVOList) && esAddViewVOList.size() > 0)
            {
                for (EsAddViewVO esAddViewVO : esAddViewVOList)
                {
                    List<Map> addfilelist = esAddViewVO.getAddfilelist();
                    if(!ArrayUtil.isObjectEmpty(esAddViewVO) && addfilelist.size() > 0)
                    {
                        String type = esAddViewVO.getType();
                        ESUtil.addSeDetial(addfilelist, dsid, type);
                    }

                }
            }

            if (!ArrayUtil.isObjectEmpty(esDeleteViewVOList) && esDeleteViewVOList.size() > 0)
            {
                for (EsDeleteViewVO esDeleteViewVO : esDeleteViewVOList)
                {
                    if (!ArrayUtil.isObjectEmpty(esDeleteViewVO) && esDeleteViewVO.getFileidlist().size() > 0)
                    {
                        String type = esDeleteViewVO.getType();
                        ESUtil.deleteSeDetial(esDeleteViewVO.getFileidlist(), type);
                    }
                }
            }
        }
    }
}
