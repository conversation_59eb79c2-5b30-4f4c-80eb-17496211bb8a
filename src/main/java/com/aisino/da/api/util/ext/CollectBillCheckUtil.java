package com.aisino.da.api.util.ext;

import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.da.api.handler.enums.DaResult;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class CollectBillCheckUtil
{

    //公共字段
    static HashMap<String, String> CommonMap = new HashMap<>();
    //公共字段必填
    static HashMap<String, String> CommonRequiredMap = new HashMap<>();
    //字段长度
    static HashMap<String, String> FieldLengthMap = new HashMap<>();

    //发票主表
    static HashMap<String, String> invoiceHeadMap = new HashMap<>();
    //发票子表
    static HashMap<String, String> invoiceBodyMap = new HashMap<>();
    //发票主表必填
    static HashMap<String, String> invoiceHeadRequiredMap = new HashMap<>();


    //银行回单表
    static HashMap<String, String> bankReceiptMap = new HashMap<>();

    //银行回单表必填
    static HashMap<String, String> bankReceiptRequiredMap = new HashMap<>();

    static
    {
        //公共字段校验相关
        //公共入库字段
        CommonMap.put("主单据日期","sybillcode");
        CommonMap.put("凭证编号","cvoucherno");
        CommonMap.put("凭证id","cvoucherid");
        CommonMap.put("主单据编码","sybillcode");
        CommonMap.put("主单据id","sybillid");
        CommonMap.put("凭证日期","cvoucherdate");
        CommonMap.put("文件总数","ifileqty");
        CommonMap.put("影像数","iimgqty");
        CommonMap.put("附件数","iappqty");
        CommonMap.put("组织编码","cexpensecompanyguid_code");
        CommonMap.put("组织名称","cexpensecompanyguid_name");
        CommonMap.put("单据id","billid");
        CommonMap.put("单据编号","cbillcode");
        CommonMap.put("职员","cempguid_name");
        CommonMap.put("部门","cdeptguid_name");
        CommonMap.put("金额","iamt");
        CommonMap.put("制单人","ccreatorname");
        CommonMap.put("审核人","cauditusername");
        CommonMap.put("单据日期","ddate");
        CommonMap.put("币种","curcode");

        //公共校验字段
/*
        CommonRequiredMap.put("主单据日期","sybilldate");
*/
        CommonRequiredMap.put("凭证编号","cvoucherno");
        CommonRequiredMap.put("凭证id","cvoucherid");
/*        CommonRequiredMap.put("主单据编码","sybillcode");
        CommonRequiredMap.put("主单据id","sybillid");*/
        CommonRequiredMap.put("凭证日期","cvoucherdate");
        CommonRequiredMap.put("文件总数","ifileqty");
        CommonRequiredMap.put("组织编码","cexpensecompanyguid_code");
        CommonRequiredMap.put("组织名称","cexpensecompanyguid_name");
        CommonRequiredMap.put("单据id","billid");
        CommonRequiredMap.put("单据编号","cbillcode");
        CommonRequiredMap.put("单据日期","ddate");
/*        CommonRequiredMap.put("职员","cempguid_name");
        CommonRequiredMap.put("部门","cdeptguid_name");
        CommonRequiredMap.put("金额","iamt");*/
        //长度校验字段
        FieldLengthMap.put("id","100");
        FieldLengthMap.put("其它","4000");
        FieldLengthMap.put("摘要","5000");
        FieldLengthMap.put("事由","5000");

        //票据校验相关
        //发票主表字段
        invoiceHeadMap.put("影像表id","oldcguid");
        invoiceHeadMap.put("单据id","cbillguid");
        invoiceHeadMap.put("单据子表id","cbilllineguid");
        invoiceHeadMap.put("发票大类","cclass");
        invoiceHeadMap.put("发票类型","ctype");
        invoiceHeadMap.put("发票大类编码","cclassid");
        invoiceHeadMap.put("发票类型编码","ctypeid");
        invoiceHeadMap.put("发票号码","cnumber");
        invoiceHeadMap.put("发票代码","ccode");
        invoiceHeadMap.put("开票日期","ddate");
        invoiceHeadMap.put("总金额","iamt");
        invoiceHeadMap.put("价税合计","itotal");
        invoiceHeadMap.put("乘车人","passenger");
        invoiceHeadMap.put("销售方名称","csellername");
        invoiceHeadMap.put("销售方纳税人识别号","csellertaxid");
        invoiceHeadMap.put("销售方地址电话","cseller_addr_tel");
        invoiceHeadMap.put("销售方开户行及账户","cseller_bank_account");
        invoiceHeadMap.put("购买方名称","cbuyername");
        invoiceHeadMap.put("购买方纳税人识别号","cbuyertaxid");
        invoiceHeadMap.put("购买方地址电话","cbuyer_addr_tel");
        invoiceHeadMap.put("购买方开户行及账户","cbuyer_bank_account");
        invoiceHeadMap.put("省份","province");
        invoiceHeadMap.put("机器码","machinecode");
        invoiceHeadMap.put("收款人","iclerk");
        invoiceHeadMap.put("复核人","review");
        invoiceHeadMap.put("备注","cremark");
        invoiceHeadMap.put("查验状态","ccheckresult");
        invoiceHeadMap.put("查验时间","dcheckdate");
        invoiceHeadMap.put("核对结果","checkresult");
        invoiceHeadMap.put("核对方式","checkmode");
        invoiceHeadMap.put("来源","cfrom");
        invoiceHeadMap.put("是否OCR识别","isocr");
        //发票子表字段
        invoiceBodyMap.put("开票明细","goodsname");
        invoiceBodyMap.put("规格型号","specifications");
        invoiceBodyMap.put("单位","measureunits");
        invoiceBodyMap.put("数量","numbers");
        invoiceBodyMap.put("单价","notaxprice");
        invoiceBodyMap.put("不含税金额","goodsamount");
        invoiceBodyMap.put("税率","taxrate");
        invoiceBodyMap.put("税额","goodstaxamount");
        //发票必填字段
        invoiceHeadRequiredMap.put("单据id","单据id");
        //invoiceHeadRequiredMap.put("单据子表id","单据子表id");

        //银行回单
        bankReceiptMap.put("主表id","cheadid");
        bankReceiptMap.put("关联单据id","cbillguid");
        bankReceiptMap.put("关联单据页面id","cbillpageid");
        bankReceiptMap.put("交易金额","itransactionamount");
        bankReceiptMap.put("银行流水号","bankserial");
        bankReceiptMap.put("摘要","cremark");
        bankReceiptMap.put("对方户名","ccounteraccname");
        bankReceiptMap.put("对方账户","ccounteraccount");
        bankReceiptMap.put("对方银行","ccounterbank");
        bankReceiptMap.put("电子回单号","ctransactionnum");
        bankReceiptMap.put("本方户名","caccountname");
        bankReceiptMap.put("本方账户","cbankaccount");
        bankReceiptMap.put("本方银行","cbank");
        bankReceiptMap.put("交易时间","ctransactiondate");
        bankReceiptMap.put("系统收集","datasource");

        bankReceiptRequiredMap.put("电子回单号","");
        bankReceiptRequiredMap.put("交易时间","");
        bankReceiptRequiredMap.put("交易金额","");
        bankReceiptRequiredMap.put("本方户名","");
        bankReceiptRequiredMap.put("本方账户","");
        bankReceiptRequiredMap.put("本方银行","");
        bankReceiptRequiredMap.put("对方户名","");
        bankReceiptRequiredMap.put("对方账户","");
        bankReceiptRequiredMap.put("对方银行","");

    }

    String billUrl = "page/commonvchr/";
    String billUrlName = "?origin=1&singleTab=1&cstate=view&name=";


    public static DaResult checkCommon(Map commonmap)
    {
        StringBuffer stringBuffer = new StringBuffer();
        valueVoucherValidate(commonmap, CommonRequiredMap, stringBuffer);
        if (StringUtils.isBlank(stringBuffer.toString()))
        {
            return  new DaResult().sucess(stringBuffer.toString());
        }
        return  new DaResult().error("common【"  + stringBuffer.toString() + "】");
    }

    private static void valueVoucherValidate(Map datamap, HashMap<String, String> RequiredMap,StringBuffer stringBuffer)
    {
        for (String key : RequiredMap.keySet())
        {   //必填项
            Object value = datamap.get(key);
            if(isObjectEmpty(value))
            {
                stringBuffer.append(key+"必填;");
            }
        }

        for (Object o : datamap.keySet()) {
            Object value = datamap.get(o);
            if(isObjectNotEmpty(value))
            {
                valueLengthValidate(o.toString(),value,FieldLengthMap,stringBuffer);
            }
        }
    }

    private static Boolean isObjectNotEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isNotEmpty(s);
    }

    private static void valueLengthValidate(String key, Object value, HashMap<String, String> LengthMap, StringBuffer stringBuffer){
        //数值类型不参数长度判断
        if (!(value instanceof Number) && !(value instanceof List)&& !(value instanceof Map))
        {
            Integer length = 0;
            if (key.contains("id"))
            {
                length = CollectionUtil.getIntFromMap(LengthMap, "id");
            }else if (key.contains("事由") || key.contains("摘要"))
            {
                length = CollectionUtil.getIntFromMap(LengthMap, "事由");
            }else
            {
                length =  CollectionUtil.getIntFromMap(LengthMap, "其它");
            }

            if(value.toString().length()>length){//长度报错
                stringBuffer.append(key+"大于最大长度【" + length + "】;");
            }
        }

    }

    public static DaResult checkInvcoice(List<Map> invoiceList)
    {
        StringBuffer stringBuffer = new StringBuffer();
        for (Map map : invoiceList)
        {
            valueVoucherValidate(map, invoiceHeadRequiredMap, stringBuffer);
        }
        if (StringUtils.isBlank(stringBuffer.toString()))
        {
            return  new DaResult().sucess(stringBuffer.toString());
        }
        return  new DaResult().error("票据【"  + stringBuffer.toString() + "】");
    }

    public static DaResult checkBank(List<Map> bankList)
    {
        StringBuffer stringBuffer = new StringBuffer();
        for (Map map : bankList)
        {
            valueVoucherValidate(map, bankReceiptRequiredMap, stringBuffer);
        }
        if (StringUtils.isBlank(stringBuffer.toString()))
        {
            return  new DaResult().sucess(stringBuffer.toString());
        }
        return  new DaResult().error("银行回单【"  + stringBuffer.toString() + "】");
    }

    public static DaResult checkHead(Map headMap, List<Map> headConfiglist)
    {
        StringBuffer stringBuffer = new StringBuffer();
        for (Map map : headConfiglist)
        {

            String isrequired = CollectionUtil.getStringFromMap(map, "isrequired");
            String cfildname = CollectionUtil.getStringFromMap(map, "cfield");
            String value = CollectionUtil.getStringFromMap(headMap, cfildname);
            //等于1为笔录
            if ("1".equalsIgnoreCase(isrequired))
            {
                if(isObjectNotEmpty(value))
                {
                    valueLengthValidate(cfildname,value,FieldLengthMap,stringBuffer);
                }
                else
                {//必填报错
                    stringBuffer.append(cfildname+"必填;");
                }
            }else
            {
                if(isObjectNotEmpty(value))
                {
                    valueLengthValidate(cfildname, value,FieldLengthMap,stringBuffer);
                }
            }
        }

        if (StringUtils.isBlank(stringBuffer.toString()))
        {
            return  new DaResult().sucess(stringBuffer.toString());
        }
        return  new DaResult().error("单据信息【"  + stringBuffer.toString() + "】");
    }

    public static DaResult checkBody(List<Map> bodyList, List<Map> bodyConfigList)
    {
        for (Map map : bodyList)
        {
            DaResult daResult = checkHead(map, bodyConfigList);
            if ("0100".equalsIgnoreCase(daResult.code))
            {
               return daResult;
            }
        }
        return  new DaResult().sucess("");
    }

    public static DaResult checkInvcoiceBody(List<Map> invoicebodylist)
    {
        StringBuffer stringBuffer = new StringBuffer();
        for (Map map : invoicebodylist)
        {
            valueVoucherValidate(map, new HashMap<>(), stringBuffer);
        }

        if (StringUtils.isBlank(stringBuffer.toString()))
        {
            return  new DaResult().sucess(stringBuffer.toString());
        }
        return  new DaResult().error("票据明细【"  + stringBuffer.toString() + "】");
    }

    private static Boolean isObjectEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isEmpty(s);
    }
}
