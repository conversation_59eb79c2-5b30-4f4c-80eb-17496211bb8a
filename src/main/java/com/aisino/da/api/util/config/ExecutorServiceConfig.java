package com.aisino.da.api.util.config;

import java.util.concurrent.*;

/**
 * @description:
 * @date: 2023/5/23 14:57
 * @author: wanglk
 */
public class ExecutorServiceConfig {

    private static ExecutorService executorService;

    static {
        init();
    }
    private ExecutorServiceConfig(){}

    private static void init() {
        executorService = new ThreadPoolExecutor(4, 10, 5, TimeUnit.SECONDS, new LinkedBlockingDeque<Runnable>(20), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     *@description：默认执行器
     *@param：
     *@return：java.util.concurrent.ExecutorService
     *@date：2023/5/26 8:56
     *@author：wanglk
     */
    public static ExecutorService getDefaultExecutor() {
        return executorService;
    }

    /**
     *@description：固定线程执行器
     *@param：
     *@return：java.util.concurrent.ExecutorService
     *@date：2023/5/26 8:56
     *@author：wanglk
     */
    public static ExecutorService getFixedExecutor() {
        return Executors.newFixedThreadPool(10);
    }
}
