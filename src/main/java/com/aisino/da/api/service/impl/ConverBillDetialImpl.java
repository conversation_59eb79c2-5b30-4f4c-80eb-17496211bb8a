package com.aisino.da.api.service.impl;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.BeanHelper;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.dao.ExtDataHandDao;
import com.aisino.da.api.entity.EsAddViewVO;
import com.aisino.da.api.entity.EsDeleteViewVO;
import com.aisino.da.api.entity.EsResultViewVO;
import com.aisino.da.api.handler.abstracts.AbstractApiHandler;
import com.aisino.da.api.handler.enums.DaDataResult;
import com.aisino.da.api.handler.factory.ApiHandlerFactory;
import com.aisino.da.api.service.ConverBillDetialService;
import com.aisino.da.api.service.VerifyJsonService;
import com.aisino.da.api.service.ext.ExtDataHandleService;
import com.aisino.da.api.service.ext.ExtPublicDataHandleService;
import com.aisino.da.api.service.fkbill.BillVoucherCollectDataService;
import com.aisino.da.api.service.fkbill.SaveAndCheckDataDetialService;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.api.util.ext.BillFileUtil;
import com.aisino.da.common.constant.SystermTagConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Savepoint;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class ConverBillDetialImpl implements ConverBillDetialService
{
    private static final Logger logger = LoggerFactory.getLogger(ConverBillDetialImpl.class);

    @Inject
    private ExtDataHandDao extDataHandDao;
    @Inject
    private DaGetBillDetialDao daGetBillDetialDao;

    @Inject
    private ExtDataHandleService extDataHandleService;

    @Inject
    private ExtPublicDataHandleService extPublicDataHandleService;
    @Inject
    private VerifyJsonService verifyJsonService;

    @Override
    public DaDataResult conerBillDetial(Map batchMap, String extcode, List<Map> billdetiallist, String dsid, String cvoucherid, boolean isRelated)
    {
        if (StringUtils.isBlank(extcode))
        {
            return new DaDataResult().error("处理单据相关报文时获取外系统编码异常：【" + extcode + "】");
        }
        boolean icover = CollectionUtil.getBoolean(batchMap, "icover", false);
        try
        {
            //获取收集系统名称
            String extname = CollectionUtil.getStringFromMap(batchMap, "csourcesysname");
            if (StringUtils.isBlank(extname))
            {
                return new DaDataResult().error("处理单据相关报文时获取外系名称异常：【" + extcode + "】");
            }
            //判断是否存在已收集单据
            String corgnid = CollectionUtil.getStringFromMap(batchMap, "corgnid");

            Map billCintegrityMap = checkBillCintegrity(cvoucherid, icover, dsid, extname, extcode, corgnid);
            if (!CollectionUtil.isBlankMap(billCintegrityMap))
            {
                return new DaDataResult().error("凭证下下包含已人工核对完整的单据,请取消人工核对后重新收集");
            }
            Map datamap = queryBillCollectionHeadIsExist(cvoucherid, icover, dsid, extname, extcode, corgnid);
            if (!CollectionUtil.isBlankMap(datamap))
            {
                return new DaDataResult().error("凭证下下包含已收集单据,请检查处理后重新收集");
            }

            //根据第三方系统编码获取系统配置
            Map extSysConfig = daGetBillDetialDao.getExtSysConfigByDsid(extcode, dsid);
            String csystag = CollectionUtil.getStringFromMap(extSysConfig, "csystag");
            if (CollectionUtil.isBlankMap(extSysConfig))
            {
                return new DaDataResult().error("收集文件时,根据第三方系统编码【" + extcode + "】获取配置为空");
            }else
            {
                boolean istatus = CollectionUtil.getBoolean(extSysConfig, "istatus", true);
                if (!istatus && !(SystermTagConstant.CZYFK_CODE.equalsIgnoreCase(csystag) || SystermTagConstant.CZYCW_CODE.equalsIgnoreCase(csystag)))
                {
                    return new DaDataResult().error("收集文件时,根据第三方系统编码【" + extcode + "】为禁用，请启用后收集");
                }
            }

            //判断所属类别分支处理0 财智云财务 1 财智云费控 2 其他产品 3 A8（本分支目前只处理0 1 2） 3 为单独分支
            boolean igsppz = true;
            if (SystermTagConstant.CZYFK_CODE.equalsIgnoreCase(csystag))
            {
                //财智云费控分支数据处理
                BillVoucherCollectDataService bean = BeanHelper.getBean(BillVoucherCollectDataService.class);

                DaDataResult daDataResult = fkDataCheck(billdetiallist, batchMap, dsid, bean,icover);
                if (daDataResult.cstatus)
                {
                    DaDataResult dataResult = dataSave(billdetiallist, batchMap, dsid, bean, icover, igsppz, isRelated);
                    dataResult.addEsResultViewVO(daDataResult);
                    return dataResult;
                }
                else
                {
                    return daDataResult;
                }
            }
            else if (SystermTagConstant.CZYCW_CODE.equalsIgnoreCase(csystag))
            {
                //财智云财务分支数据处理
                Map map = billdetiallist.get(0);
                return A6DataCheck(map, batchMap, dsid,icover,extcode,igsppz,isRelated);

            }
            else
            {
                //默认其它系统处理为标准接口处理。。。。。根据实际情况调整,因为收集单据跟随凭证,默认包含关联关系，不需要特殊处理,
                //
                Map billdetialMap = billdetiallist.get(0);
                return extPublicDataHandleService.ExtDataCheck(billdetialMap, batchMap, dsid,icover,extcode,igsppz, isRelated);
            }
        }catch (Exception e)
        {
            logger.error("处理单据相关报文时数据入库异常：=====>【" + e + "】");
            return new DaDataResult().error("处理单据相关报文时数据入库异常：【" + e.getMessage() + "】");
        }
    }

    /**
     *
     * @param batchMap
     * @param billdetialMap
     * @param dsid
     * @param cvoucherid
     * @return
     */
    @Override
    public DaDataResult conerBillDetial2(Map batchMap, Map billdetialMap, String dsid, String cvoucherid)
    {

        DbService db = new ApsContextDb().getDb(dsid);
        try
        {
            Savepoint aThis = db.savePoint("Bill" + System.currentTimeMillis());
            for (Object extcode : billdetialMap.keySet())
            {
                Object billmap = billdetialMap.get(extcode);

                if (isObjectNotEmpty(billmap))
                {
                    if (billmap instanceof Map)
                    {
                        Map billDeatilMap = (Map) billmap;
                        List listmap = new ArrayList<>();
                        listmap.add(billDeatilMap);
                        DaDataResult daDataResult =  conerBillDetial(batchMap,extcode.toString(),listmap,dsid,cvoucherid,true);
                        if (!daDataResult.cstatus)
                        {
                            if (aThis == null)
                            {
                                db.rollback();
                            }else
                            {
                                db.rollback(aThis);
                            }
                        }
                        return daDataResult;
                    }else if (billmap instanceof List)
                    {
                        List listmap = (List) billmap;
                        DaDataResult daDataResult = conerBillDetial(batchMap, extcode.toString(), listmap, dsid, cvoucherid, true);
                        if (!daDataResult.cstatus)
                        {
                            if (aThis == null)
                            {
                                db.rollback();
                            }else
                            {
                                db.rollback(aThis);
                            }
                        }
                        return daDataResult;
                    }
                }else
                {
                    if (aThis == null)
                    {
                        db.rollback();
                    }else
                    {
                        db.rollback(aThis);
                    }
                    return new DaDataResult().error("收集单据时获取【"+ extcode +"】,单据报文为【" + billmap + "】");
                }

            }

        }
        catch (SQLException e)
        {
            return new DaDataResult().error("单据数据处理异常" + e.getMessage());
        }

        return new DaDataResult().error("收集单据时获取时，数据异常" + billdetialMap.toString());
    }

    private DaDataResult A6DataCheck(Map billdetialMap, Map batchMap, String dsid, boolean icover, String extcode, boolean igsppz, boolean isRelated)
    {
        DaDataResult daDataResult = new DaDataResult();
        AbstractApiHandler apiTemplate = ApiHandlerFactory.getApiTemplate(CollectConstant.BILL);
        if (CollectionUtil.isBlankMap(billdetialMap))
        {
            return daDataResult.error("单据数据为空无需入库");
        }
        else
        {
            try
            {
                EsResultViewVO esResultViewVO = new EsResultViewVO();

                //校验数据可否入库
                String csourceorgnid = CollectionUtil.getStringFromMap(batchMap, "csourceorgnid");
                String csourceorgncode = CollectionUtil.getStringFromMap(batchMap, "csourceorgncode");
                Map billDefine = new HashMap();
                for (Object billtype : billdetialMap.keySet())
                {
                    //获取报文定义
                    billDefine = verifyJsonService.getBillDefine(extcode, billtype.toString(), csourceorgnid, csourceorgncode);
                    if (CollectionUtils.isEmpty(billDefine))
                    {
                        daDataResult.setEsResultViewVO(new EsResultViewVO());
                        return daDataResult.error("获取【" + billtype + "】报文定义为空");
                    }

                    Object billdetilo = billdetialMap.get(billtype);
                    if (isObjectNotEmpty(billdetilo))
                    {
                        if (billdetilo instanceof List )
                        {
                            List<Map> details = (List)billdetilo;
                            if (CollectionUtil.isNotEmpty(details))
                            {
                                String msg = extDataHandleService.checkStandardData(details, billDefine, apiTemplate);
                                if (StringUtils.isNotBlank(msg))
                                {
                                    daDataResult.setEsResultViewVO(new EsResultViewVO());
                                    return daDataResult.error(msg);
                                }
                            }
                        }
                    }
                    else
                    {
                        daDataResult.setEsResultViewVO(new EsResultViewVO());
                        return daDataResult.error("收集单据时获取【"+ billtype +"】,单据报文为【" + billdetilo + "】");
                    }
                }
                //校验数据通过数据入库
                for (Object billtype : billdetialMap.keySet())
                {
                    Object billdetilo = billdetialMap.get(billtype);
                    if (isObjectNotEmpty(billdetilo))
                    {
                        if (billdetilo instanceof List )
                        {
                            List<Map> details = (List)billdetilo;
                            //保存数据
                            billDefine = verifyJsonService.getBillDefine(extcode, billtype.toString(), csourceorgnid, csourceorgncode);
                            DaDataResult daResult = bzSaveData(details, billDefine, dsid, batchMap, icover, apiTemplate, extcode,isRelated);
                            if (!daResult.getCstatus())
                            {
                                daDataResult.setEsResultViewVO(new EsResultViewVO());
                                return daResult;
                            }
                        }
                    }
                }
                //校验通过保存数据
                daDataResult.setEsResultViewVO(esResultViewVO);
            }catch (Exception e)
            {
                logger.error("============>数据处理发生错误" + e);
                e.printStackTrace();
                daDataResult.setEsResultViewVO(new EsResultViewVO());
                return daDataResult.error("============>数据处理发生错误" + e.getMessage());
            }

        }
        return daDataResult.sucess("数据保存成功");
    }

    /**
     * @param billdetiallist 单据详情list
     * @param billDefine     完整报文定义
     * @param dsid           数据源id
     * @param batchMap       批次相关
     * @param iscover        是否覆盖
     * @param apiTemplate
     * @param extcode
     * @param isRelated
     * @return
     */
    private DaDataResult bzSaveData(List<Map> billdetiallist, Map billDefine, String dsid, Map batchMap, boolean iscover, AbstractApiHandler apiTemplate, String extcode, boolean isRelated)
    {
        DaDataResult daDataResult = new DaDataResult();
        if (CollectionUtil.isEmpty(billdetiallist))
        {
            return new DaDataResult().sucess("单据数据为空无需入库");
        }
        else
        {
            for (Map billdetial : billdetiallist)
            {
                EsResultViewVO esResultViewVO = saveBZData(billdetial, billDefine, dsid, batchMap, iscover, apiTemplate, extcode, isRelated);

                String code = esResultViewVO.getCode();
                if (CollectConstant.ERROR_CODE.equals(code))
                {
                    String msg = esResultViewVO.getMsg();
                    return new DaDataResult().sucess("数据入库异常：" + msg);
                }else
                {
                    daDataResult.addEsResultViewVO(esResultViewVO);
                }
            }
        }
        return daDataResult.sucess("数据入库完成");
    }

    private EsResultViewVO saveBZData(Map oldbilldetial, Map billDefine, String dsid, Map batchMap, boolean iscover, AbstractApiHandler apiTemplate, String extcode, boolean isRelated)
    {
        EsResultViewVO esResultViewVO = new EsResultViewVO();

        DbService db = new ApsContextDb().getDb(dsid);
        try
        {
            Map commonDetial = apiTemplate.getCommonDetial(oldbilldetial);
            if (CollectionUtils.isEmpty(commonDetial))
            {
                esResultViewVO.setCode(CollectConstant.ERROR_CODE);
                esResultViewVO.setMsg("转换出的common信息为空");
                return esResultViewVO;
            }else
            {
                commonDetial.put("cbillid",commonDetial.get("billid"));
            }

            String cloglistflag = CollectionUtil.getStringFromMap(batchMap, "cloglistflag");
            if (CollectConstant.VOUCHER.equalsIgnoreCase(cloglistflag))
            {
                commonDetial.put("biscollectwithvou","1");
            }else
            {
                commonDetial.put("biscollectwithvou","0");
            }
            Map defalutmap = makeDefalultValue(batchMap);

            //凭证id
            String cvoucherid = CollectionUtil.getStringFromMap(commonDetial, "cvoucherid");
            //主单据id
            String sybillid = CollectionUtil.getStringFromMap(commonDetial, "sybillid");

            //单前单据id
            String billid = CollectionUtil.getStringFromMap(commonDetial, "billid");
            //当前单据编号
            String cbillcode = CollectionUtil.getStringFromMap(commonDetial, "cbillcode");

            //ddate 通过单据日期获取资料区间
            String billdate = CollectionUtil.getStringFromMap(commonDetial, "String");
            String billInterval = ArrayUtil.getDataInterval(billdate);

            String cfiles_table_name = CollectionUtil.getStringFromMap(commonDetial, "cfiles_table_name");
            if (StringUtils.isBlank(sybillid))
            {
                sybillid = "";
            }

            Object head = billDefine.get("head");
            //单据信息入库表
            String tablename = "";
            //入库json长度
            int ijsonmaxlength = 0;
            if (head instanceof Map)
            {
                Map definhead = (Map)head;
                tablename = CollectionUtil.getStringFromMap(definhead, "cbilltype_name");
                ijsonmaxlength = CollectionUtil.getIntFromMap(definhead, "ijsonmaxlength");
                commonDetial.put("cdabilltypename", definhead.get("cbwnameguid_name"));
                commonDetial.put("cdabilltypeid", definhead.get("cbwnameguid"));
                commonDetial.put("exttablename", tablename);
            }
            //通过，删除该凭证下当前单据的历史数据所有历史数据删除历史数据
            extDataHandDao.getDaFileTableName(cfiles_table_name);
            String oldcserialnumber = CollectionUtil.getStringFromMap(batchMap, "oldcserialnumber");
            String corgnid = CollectionUtil.getStringFromMap(batchMap, "corgnid");

            //增加返回要删除的凭证下文件合集;
            EsResultViewVO esdeletvo = extDataHandDao.deleteCollectionDetialByA8(cvoucherid, billid, sybillid, tablename, cfiles_table_name, db, oldcserialnumber, corgnid);

            //获取单据拼接json
            Object billhead = oldbilldetial.get("表头");
            String billjson = JsonUtil.toJSON(billhead);

            if (ijsonmaxlength > 0 && billjson.length() > ijsonmaxlength)
            {
                //0100 代表基础校验未通过
                esResultViewVO.setCode(CollectConstant.ERROR_CODE);
                esResultViewVO.setMsg("需要存储的json数据超长");
                return esResultViewVO;
            }

            List<Map> daheadlist      = new ArrayList<>(); //默认表存储公共字段

            List<Map> invoicelist     = new ArrayList<>(); //发票
            List<Map> invoicebodylist = new ArrayList<>(); //发票
            List<Map> billlist        = new ArrayList<>(); //单据list
            List<Map> billAndVoucherRellist = new ArrayList<>(); //单据凭证关系

            extDataHandleService.converA8CommDetial(commonDetial, daheadlist, batchMap, cbillcode,tablename,isRelated);
            //单据map
            Map billmap = extDataHandleService.converA8BillDetial(batchMap, billjson ,commonDetial);
            billlist.add(billmap);
            //存储发票

            //存储单据凭证关系
            extDataHandleService.converbillAndVoucher(commonDetial, billAndVoucherRellist,batchMap);
            //构建树表信息  cfiles_table_name, 构建filest;
            List<Map> treelist = new ArrayList<>(); //树 //默认表存储公共字段
            List<Map> filelist = new ArrayList<>(); //文件记录
            String dabillcguid = CollectionUtil.getStringFromMap(billmap, "cguid");

            String oldbillid = CollectionUtil.getStringFromMap(oldbilldetial, "单据id");
            String ishead = "0";
            if (sybillid.equalsIgnoreCase(oldbillid))
            {
                ishead = "1";
            }

            //构建文件相关
            Object fileo = oldbilldetial.get("文件详情");
            if (fileo != null && fileo instanceof List)
            {
                for (Map filemap : (List<Map>) fileo)
                {
                    BillFileUtil.converfile(filemap,filelist,"","",batchMap, oldbillid, ishead,extcode,commonDetial, billInterval);
                }
            }
            //构建文件树
            Map headMap = (Map)oldbilldetial.get("表头");
            BillFileUtil.convertree(headMap, commonDetial, treelist, "1", "", dabillcguid, extcode);

            if(CollectionUtil.isNotEmpty(filelist))
            {
                extDataHandDao.inserDetialByTableName(cfiles_table_name, filelist, db);
            }

            if (CollectionUtil.isNotEmpty(billlist))
            {
                extDataHandDao.inserDetialByTableName(tablename, billlist, db);
            }

            if (CollectionUtil.isNotEmpty(invoicelist))
            {
                extDataHandDao.inserDetialByTableName("da_invoice", invoicelist, db);
            }

            if (CollectionUtil.isNotEmpty(invoicebodylist))
            {
                extDataHandDao.inserDetialByTableName("da_invoiceline", invoicebodylist, db);
            }

            if (CollectionUtil.isNotEmpty(treelist))
            {
                extDataHandDao.inserDetialByTableName("da_fview_vouandbillrela", treelist, db);
            }

            if (CollectionUtil.isNotEmpty(daheadlist))
            {
                for (Map map : daheadlist)
                {
                    if (CollectionUtil.isNotEmpty(filelist) && filelist.size() > 0 )
                    {
                        map.put("isexistfile", "1");
                        //int ifileqty = CollectionUtil.getIntFromMap(commonDetial, "ifileqty");
                        BigDecimal dfileqty = CollectionUtil.getBigDecimal(commonDetial, "ifileqty");
                        int ifileqty = dfileqty.intValue();
                        if (filelist.size() == ifileqty)
                        {
                            map.put("fileistruename","一致");
                            map.put("fileistrue","0");

                        }
                        else
                        {
                            map.put("fileistruename","不一致");
                            map.put("fileistrue","1");
                        }
                    }else
                    {
                        map.put("isexistfile", "0");
                    }

                }
                try
                {
                    extDataHandDao.inserDetialByTableName("da_api_fk_file_collection", daheadlist, db);

                }catch (Exception e)
                {
                    esResultViewVO.setCode(CollectConstant.ERROR_CODE);
                    esResultViewVO.setMsg("数据入库失败" + e.getMessage());
                    return esResultViewVO;
                }
            }
            if (CollectionUtil.isNotEmpty(billAndVoucherRellist))
            {
                extDataHandDao.inserDetialByTableName("da_fc_billAndVoucherRel", billAndVoucherRellist, db);
            }

            if (CollectionUtil.isNotEmpty(daheadlist))
            {
                for (Map map : daheadlist) {
                    String cvoucher = CollectionUtil.getStringFromMap(map, "cvoucherid");
                    updateSUAIStatusMs(cvoucher);
                    updateIntegrityStatusUpdateMs(map);
                    //da.bbs.IntegrityStatusUpdateMs
                }
            }

            esResultViewVO.setCode(CollectConstant.SUCCESS_CODE);
            esResultViewVO.setMsg("处理数据成功");
            List<EsAddViewVO> addViewVOList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(filelist) && filelist.size() > 0)
            {
                Map<String, List<Map>> groupedData = filelist.stream()
                        .collect(Collectors.groupingBy(map -> (String) map.get("cbusinesstype")));
                if (!CollectionUtil.isBlankMap(groupedData))
                {
                    for (String key : groupedData.keySet())
                    {
                        List<Map> nfilelsit = groupedData.get(key);
                        EsAddViewVO esAddViewVO = new EsAddViewVO();
                        esAddViewVO.setType(key);
                        esAddViewVO.setAddfilelist(nfilelsit);
                        addViewVOList.add(esAddViewVO);
                    }
                }
            }

            esResultViewVO.addAllEsAddViewVO(addViewVOList);

            esResultViewVO.addEsResultViewVO(esdeletvo);
            return esResultViewVO;
        }catch (Exception e)
        {
            db.rollback();
            e.printStackTrace();
            logger.error( "处理数据失败=================" + e);
            esResultViewVO.setCode(CollectConstant.ERROR_CODE);
            esResultViewVO.setMsg("处理数据失败" + e.getMessage());
            return esResultViewVO;
        }
    }

    private void updateIntegrityStatusUpdateMs(Map map)
    {
        Message ms = new Message("da.bbs.IntegrityStatusUpdateMs");
        List<Map> objects = new ArrayList<>();
        objects.add(map);
        Object rtn = ms.publish(objects);
    }

    /**
     * 更新凭证关联关系
     * @param cguid
     */
    public static void updateSUAIStatusMs(String cguid)
    {
        if (StringUtils.isNotEmpty(cguid))
        {
            List<String> list = new ArrayList();
            list.add(cguid);
            publishSUAIStatusMs(list);
        }
    }

    private static void publishSUAIStatusMs(List<String> list){
        String ctablename = "da_api_fk_file_collection";
        Message ms = new Message("da.suai.updaterelstatusrrecollectms");
        Map par=new HashMap();
        par.put("ctablename", ctablename);
        par.put("cvoucherids", list);
        Object rtn = ms.publish(par);
    }

    /**
     * 凭证加单据入口
     * @param billdetiallist
     * @param batchMap
     * @param dsid
     * @param bean
     * @param icover
     * @param isGsppz
     * @param isRelated
     * @return
     */
    private DaDataResult dataSave(List<Map> billdetiallist, Map batchMap, String dsid, BillVoucherCollectDataService bean, boolean icover, boolean isGsppz, boolean isRelated)
    {
        DaDataResult daDataResult = new DaDataResult();

        batchMap.put("biscollectwithvou", isGsppz ? "1" : "0");

        if (CollectionUtil.isEmpty(billdetiallist))
        {
            return daDataResult.sucess("单据数据为空无需入库");
        }
        else
        {
            for (Map billdetialMap : billdetiallist)
            {
                for (Object pageid : billdetialMap.keySet())
                {
                    Object billlist = billdetialMap.get(pageid);
                    if (billlist instanceof List )
                    {
                        List<Map> details = (List)billlist;
                        if (CollectionUtil.isNotEmpty(details))
                        {
                            EsDeleteViewVO esDeleteViewVO = new EsDeleteViewVO();

                            SaveAndCheckDataDetialService adapt = bean.getAdapt(pageid.toString());
                            EsAddViewVO esAddViewVO = adapt.CheckAndSaveBillDetial(details, batchMap, dsid, isRelated, isRelated);
                            String checkmsg = esAddViewVO.getMsg();
                            if (StringUtils.isNotEmpty(checkmsg))
                            {
                                daDataResult.addEsResultViewVO(esDeleteViewVO, new EsAddViewVO());
                                return daDataResult.sxerror(checkmsg);
                            }else
                            {
                                List<Map> addfilelist = esAddViewVO.getAddfilelist();
                                if (CollectionUtil.isNotEmpty(addfilelist))
                                {
                                    EsResultViewVO esResultViewVO = new EsResultViewVO();
                                    List<EsAddViewVO> addlist = new ArrayList<>();
                                    //分组

                                    Map<String, List<Map>> groupedData = addfilelist.stream()
                                            .collect(Collectors.groupingBy(map -> (String) map.get("cbusinesstype")));
                                    if (!CollectionUtil.isBlankMap(groupedData))
                                    {
                                        for (String key : groupedData.keySet())
                                        {
                                            List<Map> filelist = groupedData.get(key);
                                            EsAddViewVO esAddVievo = new EsAddViewVO();
                                            esAddVievo.setType(key);
                                            esAddVievo.setAddfilelist(filelist);
                                            addlist.add(esAddVievo);
                                        }
                                        esResultViewVO.addAllEsAddViewVO(addlist);
                                    }

                                    daDataResult.addEsResultViewVO(esResultViewVO);

                                }

                            }
                        }
                    }
                }
            }

        }
        return daDataResult.sucess("数据保存成功");
    }

    private DaDataResult fkDataCheck(List<Map> billdetiallist, Map batchMap, String dsid, BillVoucherCollectDataService bean, boolean icover)
    {
        DaDataResult daDataResult = new DaDataResult();

        if (CollectionUtil.isEmpty(billdetiallist))
        {
            return daDataResult.sucess("单据数据为空无需入库");
        }
        else
        {
            for (Map billdetialMap : billdetiallist)
            {
                for (Object pageid : billdetialMap.keySet())
                {
                    Object billlist = billdetialMap.get(pageid);
                    if (billlist instanceof List )
                    {
                        List<Map> details = (List)billlist;
                        if (CollectionUtil.isNotEmpty(details))
                        {
                            SaveAndCheckDataDetialService adapt = bean.getAdapt(pageid.toString());

                            for (Map billd : details)
                            {
                                EsAddViewVO esAddViewVO = new EsAddViewVO();

                                EsResultViewVO besResultVO = adapt.dataCheck(billd, icover, dsid, batchMap);
                                String code = besResultVO.getCode();
                                if ("0000".equalsIgnoreCase(code))
                                {
                                    String msg = besResultVO.getMsg();
                                    daDataResult.addEsResultViewVO(new EsDeleteViewVO(), esAddViewVO);
                                    return daDataResult.error("单据字段校验时候发生异常====>" + msg);
                                }else
                                {
                                    daDataResult.addEsResultViewVO(besResultVO);
                                }
                            }
                        }
                    }
                }
            }

        }
        return daDataResult.sucess("数据校验通过");
    }
    public Map checkBillCintegrity(String cvoucherid, boolean iscover, String dsid, String extname, String extcode, String corgnid)
    {
        DbService dbService = new ApsContextDb().getDb(dsid);
        String billsql = "select * from da_api_fk_file_collection where datasource=? and cintegritystatus='6' and cvoucherid = ?  and corgnid=?";
        Map billmap = dbService.queryMap(billsql, extname, cvoucherid, corgnid);
        return billmap;
    }
    public Map queryBillCollectionHeadIsExist(String cvoucherid, boolean iscover, String dsid, String extname, String extcode, String corgnid)
    {
        DbService dbService = new ApsContextDb().getDb(dsid);
        if (iscover)
        {
            //覆盖时校验单据是否存在
            String billsql = "select * from da_api_fk_file_collection where  datasource=? and (centityfilestatus<>'0' or ceastatus <>'0' or ifileborrowstatus is not null or ifileborrowstatus<>'') and cvoucherid = ? and corgnid=?";
            Map billmap = dbService.queryMap(billsql, extname, cvoucherid, corgnid);
            //覆盖时校验票据是否存在
            String invoicesql = "select * from da_invoice where  datasource=? and (centityfilestatus<>'0' or ceastatus <>'0' or cbilleastatus<>'0' or ifileborrowstatus is not null or ifileborrowstatus<>'')  and cvoucherid = ? and corgnid=?";
            List<Map> invoicemap = dbService.queryMapList(invoicesql, extname, cvoucherid, corgnid);
            //覆盖时校验银行回单是否存在
            String banksql = "select * from da_fc_banksreceipt where  datasource=? and (centityfilestatus<>'0' or ceastatus <>'0' or ifileborrowstatus is not null or ifileborrowstatus<>'')  and cvoucherid = ? and corgnid=?";
            List<Map> bankmap = dbService.queryMapList(banksql, extname, cvoucherid, corgnid);
            if (invoicemap.size() + bankmap.size() > 0)
            {
                //返回单据信息
                String getbillsql = "select * from da_api_fk_file_collection where datasource=? and (1=1) and cvoucherid = ?  and corgnid=?";
                return  dbService.queryMap(getbillsql, extname, cvoucherid, corgnid);
            }
            else
            {
                return billmap;
            }
        }
        else
        {
            //不覆盖的验重sql
            String billsql = "select * from da_api_fk_file_collection where datasource=? and collstatus<>'0' and cvoucherid = ?  and corgnid=?";
            Map billmap = dbService.queryMap(billsql, extname, cvoucherid, corgnid);
            return billmap;
        }
    }
    @Override
    public Map getCZCSyncArchiveInformationInfo()
    {
        Map<String, String> extcodemap = new HashMap<>();
        extcodemap.put(CollectConstant.FK_DEFAULT_CODE, CollectConstant.FK_DEFAULT_CODE_VALUE);
        extcodemap.put(CollectConstant.A6_DEFAULT_CODE, CollectConstant.A6_DEFAULT_CODE_VALUE);
        Map gzmp= new HashMap<>();
        for (String extcode : extcodemap.keySet())
        {
            String ywcode = extcodemap.get(extcode);
            Map pmap = new HashMap<>();
            pmap.put("3",ywcode);
            Map syncArchiveInformationInfo = getSyncArchiveInformationInfo(pmap);
            //获取extcode同步归档规则
            if (!CollectionUtil.isBlankMap(syncArchiveInformationInfo))
            {
                gzmp.put(extcode, syncArchiveInformationInfo.get(ywcode));

            }else
            {
                gzmp.put(extcode,new ArrayList<>());
            }
        }
        /*
        List<String> extcodelist = new ArrayList<>();
        extcodelist.add(CollectConstant.FK_DEFAULT_CODE);
        extcodelist.add(CollectConstant.A6_DEFAULT_CODE);
        if (CollectionUtil.isNotEmpty(extcodelist))
        {
            for (String extcode : extcodelist)
            {
                Map pmap = new HashMap<>();
                pmap.put("3",extcode);
                Map syncArchiveInformationInfo = getSyncArchiveInformationInfo(pmap);
                //获取extcode同步归档规则
                if (!CollectionUtil.isBlankMap(syncArchiveInformationInfo))
                {
                    gzmp.putAll(syncArchiveInformationInfo);

                }else
                {
                    gzmp.put(extcode,new ArrayList<>());
                }
            }
        }*/

        return gzmp;
    }
    private Map getSyncArchiveInformationInfo(Map params){
        //归档设置ms
        /**
         为1+会计账簿、报告、其他，返回会计账簿、报告、其他模板配置
         为2+记账凭证，返回凭证模板配置
         为3+固定资产，返回固定资产模板配置
         为3+费控，返回费控模板配置
         * */
        Message msg = new Message("da.fc.SyncArchiveInformationMs");
        Object value = msg.publish(params);
        if(value!=null&&value instanceof Map){
            Map rtn = (Map) value;
            return rtn;
        }else {
            return null;
        }
    }

    private static Boolean isObjectNotEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isNotEmpty(s);
    }

    private static Map makeDefalultValue(Map batchmap)
    {
        Map map = new HashMap<>();
        String corgnid           = CollectionUtil.getStringFromMap(batchmap,"corgnid");
        String corgnid_name      = CollectionUtil.getStringFromMap(batchmap,"corgnid_name");
        String cadminorgnid      = CollectionUtil.getStringFromMap(batchmap,"cadminorgnid");
        String cadminorgnid_name = CollectionUtil.getStringFromMap(batchmap,"cadminorgnid_name");
        String ccreatorid        = CollectionUtil.getStringFromMap(batchmap,"ccreatorid");
        String ccreatorid_name   = CollectionUtil.getStringFromMap(batchmap,"ccreatorid_name");
        String ccreatedate       = CollectionStringUtil.getSysDateTime();
        String csourcesysname   = CollectionUtil.getStringFromMap(batchmap,"csourcesysname");

        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate", ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);

        map.put("ccollectorid",ccreatorid);
        map.put("ccollectorid_name",ccreatorid_name);
        map.put("csourcesysname",csourcesysname);
        return map;
    }
}
