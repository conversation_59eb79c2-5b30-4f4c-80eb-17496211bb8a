package com.aisino.da.api.service.publicother.impl;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.handler.enums.DaDataResult;
import com.aisino.da.api.service.fkbill.DataConveService;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.api.util.ExtFileUploadUtil;
import com.aisino.da.api.util.ext.ESUtil;
import com.aisino.da.api.util.ext.EsConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 行回单数据处理类
 */
@Service
public class BankReceiptConveService
{
    @Inject
    private static DaGetBillDetialDao daGetBillDetialDao;
    static String billUrl = "page/commonvchr/";
    static String billUrlName = "?origin=1&singleTab=1&cstate=view&name=";
    static HashMap<String, String> bankReceiptMap = new HashMap<>();
    static HashMap<String, String> bankCommonMap = new HashMap<>();
    static HashMap<String, String> nbankReceiptMap = new HashMap<>();
    static HashMap<String, String> treePublicMap = new HashMap<>();

    //文件表
    static HashMap<String, String> daFileMap = new HashMap<>();

    //银行树
    static HashMap<String, String> treeBankMap = new HashMap<>();
    static
    {
        bankReceiptMap.put("回单编号","ctransactionnum");
        bankReceiptMap.put("交易时间","ctransactiondate");
        bankReceiptMap.put("本方户名","caccountname");
        bankReceiptMap.put("本方账户","cbankaccount");
        bankReceiptMap.put("交易金额","itransactionamount");
        bankReceiptMap.put("币种","curcode");

        nbankReceiptMap.put("本方银行","cbank");
        nbankReceiptMap.put("对方户名","ccounteraccname");
        nbankReceiptMap.put("对方账户","ccounteraccount");
        nbankReceiptMap.put("对方银行","ccounterbank");
        nbankReceiptMap.put("摘要","cremark");
        nbankReceiptMap.put("用途","cpurpose");
        nbankReceiptMap.put("附言","cpostscript");
        nbankReceiptMap.put("交易流水号","bankserial");
        nbankReceiptMap.put("扩展字段1","ext_column1");
        nbankReceiptMap.put("扩展字段2","ext_column2");
        nbankReceiptMap.put("扩展字段3","ext_column3");
        nbankReceiptMap.put("扩展字段4","ext_column4");
        nbankReceiptMap.put("扩展字段5","ext_column5");

        nbankReceiptMap.put("标准扩展字段1","column1");
        nbankReceiptMap.put("标准扩展字段2","column2");
        nbankReceiptMap.put("标准扩展字段3","column3");
        nbankReceiptMap.put("标准扩展字段4","column4");
        nbankReceiptMap.put("标准扩展字段5","column5");
        nbankReceiptMap.put("标准扩展字段6","column6");
        nbankReceiptMap.put("标准扩展字段7","column7");
        nbankReceiptMap.put("标准扩展字段8","column8");
        nbankReceiptMap.put("标准扩展字段9","column9");
        nbankReceiptMap.put("标准扩展字段10","column10");
        nbankReceiptMap.put("标准扩展字段11","column11");
        nbankReceiptMap.put("标准扩展字段12","column12");
        nbankReceiptMap.put("标准扩展字段13","column13");
        nbankReceiptMap.put("标准扩展字段14","column14");
        nbankReceiptMap.put("标准扩展字段15","column15");
        nbankReceiptMap.put("标准扩展字段16","column16");
        nbankReceiptMap.put("标准扩展字段17","column17");
        nbankReceiptMap.put("标准扩展字段18","column18");
        nbankReceiptMap.put("标准扩展字段19","column19");
        nbankReceiptMap.put("标准扩展字段20","column20");
        nbankReceiptMap.put("标准扩展字段21","column21");
        nbankReceiptMap.put("标准扩展字段22","column22");
        nbankReceiptMap.put("标准扩展字段23","column23");
        nbankReceiptMap.put("标准扩展字段24","column24");
        nbankReceiptMap.put("标准扩展字段25","column25");
        nbankReceiptMap.put("标准扩展字段26","column26");
        nbankReceiptMap.put("标准扩展字段27","column27");
        nbankReceiptMap.put("标准扩展字段28","column28");
        nbankReceiptMap.put("标准扩展字段29","column29");
        nbankReceiptMap.put("标准扩展字段30","column30");
        nbankReceiptMap.put("标准扩展字段31","column31");
        nbankReceiptMap.put("标准扩展字段32","column32");
        nbankReceiptMap.put("标准扩展字段33","column33");
        nbankReceiptMap.put("标准扩展字段34","column34");
        nbankReceiptMap.put("标准扩展字段35","column35");
        nbankReceiptMap.put("标准扩展字段36","column36");
        nbankReceiptMap.put("标准扩展字段37","column37");
        nbankReceiptMap.put("标准扩展字段38","column38");
        nbankReceiptMap.put("标准扩展字段39","column39");
        nbankReceiptMap.put("标准扩展字段40","column40");
        nbankReceiptMap.put("标准扩展字段41","column41");
        nbankReceiptMap.put("标准扩展字段42","column42");
        nbankReceiptMap.put("标准扩展字段43","column43");
        nbankReceiptMap.put("标准扩展字段44","column44");
        nbankReceiptMap.put("标准扩展字段45","column45");
        nbankReceiptMap.put("标准扩展字段46","column46");
        nbankReceiptMap.put("标准扩展字段47","column47");
        nbankReceiptMap.put("标准扩展字段48","column48");
        nbankReceiptMap.put("标准扩展字段49","column49");
        nbankReceiptMap.put("标准扩展字段50","column50");

        nbankReceiptMap.put("是否自动关联", "cautoass");

        bankCommonMap.put("主单据日期","sybilldate");
        bankCommonMap.put("凭证id","cvoucherid");
        bankCommonMap.put("凭证编号","cvoucherno");
        bankCommonMap.put("主单据编码","sybillcode");
        bankCommonMap.put("主单据id","sybillid");
        bankCommonMap.put("凭证日期","cvoucherdate");
        bankCommonMap.put("文件总数","ifileqty");
        bankCommonMap.put("组织编码","cOrgnCode");
        bankCommonMap.put("组织名称","cOrgnName");
        bankCommonMap.put("单据id","cbillguid");
        bankCommonMap.put("单据编号","cbillcode");
        bankCommonMap.put("单据日期","cbilldate");
        //bankCommonMap.put("分表字段","cfiles_table_name");

        treePublicMap.put("cfiletablename","cfiles_table_name");
        treePublicMap.put("cparent","凭证id");
        treePublicMap.put("cvoucherid","凭证id");
        treePublicMap.put("sybillid","主单据id");

        treeBankMap.put("cmeanscode","ctransactionnum");
        treeBankMap.put("cmeansname","银行回单");

        daFileMap.put("存储文件名","filename");
        daFileMap.put("文件类型","cfiletype");
        daFileMap.put("文件哈希码","mdnumber");
        daFileMap.put("文件哈希类型","chxtype");
        daFileMap.put("单据id","cbillid");
        daFileMap.put("文件实际名称","cfilerealname");
        daFileMap.put("银行回单id","oldbankid");
        daFileMap.put("文件来源类型","cfilesourcetype");
        daFileMap.put("文件访问地址","fileurl");
        daFileMap.put("桶名","dxccname");
        daFileMap.put("文件收集方式","filecollmethod");
        daFileMap.put("存储方式","storagemethod");
        daFileMap.put("路径","filepath");
        daFileMap.put("载体类型","carriertype");
        daFileMap.put("排序字段","csortindex");

    }

    private static final Log log = LogFactory.getLog(BankReceiptConveService.class);

    public static String CheckDataByBankReceipt(Map bankmap, Map sourceBatch, String dsid, List<String> bankAccount, boolean iscover)
    {
        Map logMap = new HashMap<>();
        StringBuffer sb = new StringBuffer();
        //因银行回单公共字段不需要校验，固只检验实际字段
        String ctransactionnum = CollectionUtil.getStringFromMap(bankmap,"回单编号");
        String cvoucherid= CollectionUtil.getStringFromMap(bankmap,"凭证id");
        boolean ishavcbankaccount = bankAccount.contains(bankmap.get("本方账户"));
        if (!ishavcbankaccount)
        {
            sb.append( "系统中没有本方账户" + bankmap.get("本方账户")) ;
        }

        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");

        //判断是否含有 完整性状态增加:人工核对完整 6,人工核对不完整 7
        boolean iscintegritystatus = daGetBillDetialDao.queryBankCintegrit(ctransactionnum, dsid, corgnid, cvoucherid);
        if (iscintegritystatus) {
            sb.append("已人工核对完整的银行回单,不允许重复收集");
        }
        //查询数据是否已存在
        if (iscover)
        {
            //判断有无手工导入记录datasource=手工
            boolean b = daGetBillDetialDao.queryBankReceiptsg(ctransactionnum, dsid, corgnid,cvoucherid);
            if (b)
            {
                sb.append( "已在系统中存在手工导入记录,请删除后重新收集") ;
            }else
            {
                //判断是否收集过重复记录
                b = daGetBillDetialDao.queryBankReceiptAndIscover(ctransactionnum, dsid, corgnid,cvoucherid);
                if (b)
                {
                    sb.append("已匹配银行流水或已发生后续操作，不允许重复收集") ;
                }
            }
        }else
        {
            boolean b = daGetBillDetialDao.queryBankReceipt(ctransactionnum, dsid, corgnid,cvoucherid);
            if (b)
            {
                sb.append("已存在，不允许重复收集") ;
            }
        }


        CheckReceiptMap(bankmap,sb);
        CheckFileName(bankmap,sb);
        if (sb.length() > 0)
        {
            logMap.putAll(sourceBatch);
            logMap.put("ctransactionnum", ctransactionnum);
            logMap.put("csyncexceptionmsg", "银行回单【" + ctransactionnum + "】" + sb.toString());
            logMap.put("cguid", Guid.g());
            String ccreatedate = CollectionStringUtil.getSysDateTime();
            logMap.put("ccreatedate", ccreatedate);
            String date = CollectionUtil.getStringFromMap(bankmap, "交易时间");
            if (StringUtils.isNotBlank(date)){
                String ctransactiondate = strToDate(date);
                logMap.put("ctransactiondate",ctransactiondate);
            }
            List<Map> loglist = new ArrayList<>();
            loglist.add(logMap);
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_banksreceipt_collect_log", loglist,dsid);
            return "银行回单【" + ctransactionnum + "】" + sb.toString();
        }

        return sb.toString();
    }

    private static void CheckFileName(Map bankmap, StringBuffer sb)
    {
        Object filesobj = bankmap.get("文件详情");
        //构建文件路径
                if (filesobj != null && filesobj instanceof List) {
            List<Map> oldfilelist = (ArrayList<Map>) filesobj;
            for (Map filemap : oldfilelist)
            {
                String cfilerealname = CollectionUtil.getStringFromMap(filemap, "文件实际名称");
                String filename = CollectionUtil.getStringFromMap(filemap, "存储文件名");
                if (!ArrayUtil.isValidFileName(cfilerealname))
                {
                    sb.append("文件实际名称").append(cfilerealname).append("缺少文件名后缀或文件格式错误!");
                }
                if (!ArrayUtil.isValidFileName(filename))
                {
                    sb.append("存储文件名").append(filename).append("缺少文件名后缀或文件格式错误!");
                }
            }
        }
    }

    private static String CheckData(List<Map> banklist, StringBuffer sb)
    {
        for (Map oldbankmap : banklist)
        {
            CheckReceiptMap(oldbankmap,sb);
        }
        return sb.toString();
    }

    private static void CheckReceiptMap(Map oldbankmap, StringBuffer sb)
    {
        for (String key : bankReceiptMap.keySet())
        {
            if (oldbankmap.containsKey(key))
            {
                if (isObjectEmpty(oldbankmap.get(key)))
                {
                    sb.append("必填字段【" + key + "】值为空");
                }else
                {
                    if ("交易时间".equalsIgnoreCase(key))
                    {
                        String s = strToDate(oldbankmap.get(key).toString());
                        if (StringUtils.isBlank(s))
                        {
                            sb.append("必填字段【" + key + "】,转换异常");
                        }
                    }
                }

            }else
            {
                sb.append("缺少必填字段" + key);
            }
        }
    }

    private static Boolean isObjectEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isEmpty(s);
    }

    public static DaDataResult SaveDataByBankReceipt(Map bankcommmap, Map sourceBatch, String dsid, boolean canSXcheck, boolean sXcheck, Map stomedMapByExtCode)
    {

        //构建公共字段
        List<Map> bankreceiptlist = new ArrayList<>();//银行回单数据
        List<Map> bankAndVoucherRellist = new ArrayList<>(); //凭证回单关系
        List<Map> treelist = new ArrayList<>(); //树 //默认表存储公共字段

        //判断是否存在重复数据

        Map commonMap = getCommonMap(bankcommmap, sourceBatch);
        Map errormap = getBanklist(bankreceiptlist, bankcommmap, commonMap, sourceBatch, bankAndVoucherRellist, treelist, dsid, sXcheck, stomedMapByExtCode);
        boolean status = CollectionUtil.getBoolean(errormap, "status", false);
        try
        {
            if (!status)
            {
                Map logMap = new HashMap<>();
                logMap.putAll(sourceBatch);
                logMap.put("ctransactionnum", errormap.get("ctransactionnum"));
                logMap.put("csyncexceptionmsg", errormap.get("msg"));
                logMap.put("cguid", Guid.g());

                String ccreatedate = CollectionStringUtil.getSysDateTime();
                logMap.put("ccreatedate", ccreatedate);
                String date = CollectionUtil.getStringFromMap(bankcommmap, "交易时间");
                if (StringUtils.isNotBlank(date)){
                    String ctransactiondate = strToDate(date);
                    logMap.put("ctransactiondate",ctransactiondate);
                }
                List<Map> loglist = new ArrayList<>();
                loglist.add(logMap);
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_banksreceipt_collect_log", loglist,dsid);
                return new DaDataResult().error("").sxerror(errormap.get("msg").toString());
            }

            if (CollectionUtil.isNotEmpty(bankreceiptlist))
            {
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_banksreceipt", bankreceiptlist,dsid);
            }

            if (CollectionUtil.isNotEmpty(bankAndVoucherRellist))
            {
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_VoucherAndBankRel", bankAndVoucherRellist,dsid);
            }

            if (CollectionUtil.isNotEmpty(treelist))
            {
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fview_vouandbillrela", treelist,dsid);
            }

            if (CollectionUtil.isNotEmpty(bankreceiptlist))
            {
                for (Map map : bankreceiptlist) {
                    String cvoucherid = CollectionUtil.getStringFromMap(map, "cvoucherid");
                    updateSUAIStatusMs(cvoucherid);
                    updateIntegrityStatusUpdateMs(map);

                }
            }
        }catch (Exception e)
        {
            Map logMap = new HashMap<>();
            logMap.putAll(sourceBatch);
            logMap.put("ctransactionnum", errormap.get("ctransactionnum"));
            logMap.put("csyncexceptionmsg", errormap.get("msg"));
            logMap.put("cguid", Guid.g());
            String ccreatedate = CollectionStringUtil.getSysDateTime();
            logMap.put("ccreatedate", ccreatedate);
            String date = CollectionUtil.getStringFromMap(bankcommmap, "交易时间");
            if (StringUtils.isNotBlank(date)){
                String ctransactiondate = strToDate(date);
                logMap.put("ctransactiondate",ctransactiondate);
            }
            List<Map> loglist = new ArrayList<>();
            loglist.add(logMap);
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_banksreceipt_collect_log", loglist,dsid);
            return new DaDataResult().error("回单数据入库异常：【" + e.getMessage() + "】");
        }finally {
            ApsContextDb apsContext = new ApsContextDb();
            DbService db = apsContext.getDb(dsid);
            db.commit();
        }


        return new DaDataResult().sucess("回单数据存储完成");
    }


    /**
     * 更新凭证关联关系
     * @param cguid
     */
    public static void updateSUAIStatusMs(String cguid)
    {
        if (StringUtils.isNotEmpty(cguid))
        {
            List<String> list = new ArrayList();
            list.add(cguid);
            publishSUAIStatusMs(list);
        }
    }

    private static void updateIntegrityStatusUpdateMs(Map map)
    {
        Message ms = new Message("da.bbs.IntegrityStatusUpdateMs");
        List<Map> objects = new ArrayList<>();
        objects.add(map);
        Object rtn = ms.publish(objects);
    }

    private static void publishSUAIStatusMs(List<String> list){
        String ctablename = "da_fc_banksreceipt";
        Message ms = new Message("da.suai.updaterelstatusrrecollectms");
        Map par=new HashMap();
        par.put("ctablename", ctablename);
        par.put("cvoucherids", list);
        Object rtn = ms.publish(par);
    }
    private static Map getBanklist(List<Map> bankreceiptlist, Map bankcommmap, Map commonMap, Map sourceBatch, List<Map> bankAndVoucherRellist, List<Map> treelist, String dsid, boolean sXcheck, Map stomedMapByExtCode)
    {
        Map rtnmap = new HashMap<>();


        List<Map> filelist = new ArrayList<>(); //文件合集
        //生成文件存储表，
        String ctransactiondate = CollectionUtil.getStringFromMap(bankcommmap, "交易时间");
        String ctransactionnum = CollectionUtil.getStringFromMap(bankcommmap, "回单编号");

        String cfiles_table_name = "";
        if (StringUtils.isNotBlank(ctransactiondate) && ctransactiondate.length() > 4) {
            cfiles_table_name = "da_files_" + ctransactiondate.substring(0, 4);
        } else {

            cfiles_table_name = "da_files_" + getSysYear();
        }
        commonMap.put("cfiles_table_name", cfiles_table_name);
        commonMap.put("cfiletablename", cfiles_table_name);
        Map Bankmap = new HashMap<>();
        Bankmap.put("datasource",CollectionUtil.getStringFromMap(sourceBatch,"csourcesysname"));

        String backguid = Guid.g();

        StringBuffer stringBuffer = EsConvertUtil.getBankCfilecontentsBySJ(bankcommmap);
        //填充必填字段
        setBankReceiptMap(Bankmap, bankcommmap);
        //填充非必填字段
        setBankNotReceiptMap(Bankmap, bankcommmap);

        //放入状态
        makestatusValue(Bankmap);

        //放入公共字段
        Bankmap.putAll(commonMap);

        Bankmap.put("biscollectwithvou", "0");
        //文件处理,生成文件原始记录
        Object filesobj = bankcommmap.get("文件详情");
        //构建文件路径
        String extcode = CollectionUtil.getStringFromMap(sourceBatch, "extcode");
        String cserialnumber = CollectionUtil.getStringFromMap(sourceBatch, "cserialnumber");
        String bankInterval = ArrayUtil.getDataInterval(ctransactiondate);

        String dafilepath = extcode + "/" +  ctransactionnum + "/" + CollectConstant.BANKRECEIPT_NAME + "/";
        if (filesobj != null && filesobj instanceof List) {
            List<Map> oldfilelist = (ArrayList<Map>) filesobj;
            for (Map filemap : oldfilelist)
            {
                DataConveService.publicextconverfile(filemap, filelist, daFileMap, backguid, "", commonMap, "", "0", dafilepath, sourceBatch, bankInterval, "");
            }
        }

        //刷新id
        Bankmap.put("cguid", backguid);
        //放入银行回单相关默认字段
        makeDefaultBillDetial(Bankmap);
        Bankmap.put("datainterval", bankInterval);

        //树表数据
        convertree(Bankmap,sourceBatch , treelist, treePublicMap, treeBankMap, "3", "", backguid, billUrl + "da_bank_ysj/da_bank_ysj/" + backguid + billUrlName + "银行回单", "0");
        //关联表数据
        databankAndVoucher(commonMap, bankAndVoucherRellist, Bankmap);

        String daFileTableName = daGetBillDetialDao.getDaFileTableName(cfiles_table_name);
        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");


        if (CollectionUtil.isNotEmpty(filelist))
        {

            List nfilelist = new ArrayList<>();
            for (Map filemap : filelist)
            {
                Map map = ExtFileUploadUtil.dowloadFile(filemap, stomedMapByExtCode, sXcheck);
                boolean status = CollectionUtil.getBoolean(map, "status", false);
                //若为true代表文件下载成功若四性检测可以进行检测
                if (!status)
                {
                    map.put("ctransactionnum", ctransactionnum);
                    return map;
                }

                //将下载文件成功的map 放入新的list以便数据入库
                filemap.put("cstatus", "1");
                filemap.put("cstatus_name", "收集成功");
                filemap.put("storagemethod", "default");
                filemap.put("cfilessize", map.get("cfilessize"));

                //增加ES字段
                filemap.put("cfileid", filemap.get("cguid"));
                filemap.put("cfilenum", "");
                filemap.put("cqznum","");
                filemap.put("caccountclassname","");
                filemap.put("caccountclasscode","");
                filemap.put("cdetailclassname","");
                filemap.put("cdetailclasscode","");
                filemap.put("cperioddate","");
                filemap.put("ccustody_year_name","");
                filemap.put("cfilecontents", stringBuffer);

                nfilelist.add(filemap);
            }

            //都下载完成且四性检测通过认为文件下载完成拆入下载完成字段
            int sjnum = nfilelist.size();
            int ifileqty = CollectionUtil.getIntFromMap(Bankmap, "ifileqty");
            if (sXcheck && ifileqty > 0 && ifileqty != sjnum)
            {
                Map map = new HashMap<>();
                map.put("ctransactionnum", ctransactionnum);
                map.put("msg", "四性检测不通过:实际文件数【" + sjnum + "】与传递过来要收集的文件数【" + ifileqty +"】不一致");
                return map;
            }

            Bankmap.put("downloadfilenum", sjnum);

            String cvoucherid = CollectionUtil.getStringFromMap(bankcommmap, "凭证id");
            Map bankReceiptmap = daGetBillDetialDao.queryBankReceiptMap(ctransactionnum,dsid, corgnid,cvoucherid);
            List<String> deletefilecguidlist = new ArrayList<>();

            if(!CollectionUtil.isBlankMap(bankReceiptmap))
            {
                deletefilecguidlist = daGetBillDetialDao.queryBankReceiptFile(bankReceiptmap, dsid);
                daGetBillDetialDao.deleteBankReceipt(bankReceiptmap, ctransactionnum,dsid);
            }
            daGetBillDetialDao.inserDetialByTableNameByCZY(daFileTableName, nfilelist, dsid);
            try
            {
                if (CollectionUtil.isNotEmpty(nfilelist))
                {
                    ESUtil.addSeDetial(nfilelist, dsid,"3");
                }

                if (CollectionUtil.isNotEmpty(deletefilecguidlist))
                {
                    ESUtil.deleteSeDetial(deletefilecguidlist, "3");
                }

            }catch (Exception e)
            {
                log.error("银行回单调用ES发生错误：" + e);
                e.printStackTrace();
                log.error("银行回单调用ES发生错误：" + e);
            }
        }else
        {
            String cvoucherid = CollectionUtil.getStringFromMap(bankcommmap, "凭证id");

            Map bankReceiptmap = daGetBillDetialDao.queryBankReceiptMap(ctransactionnum,dsid, corgnid, cvoucherid);

            List<String> deletefilecguidlist = new ArrayList<>();
            if(!CollectionUtil.isBlankMap(bankReceiptmap))
            {
                deletefilecguidlist = daGetBillDetialDao.queryBankReceiptFile(bankReceiptmap, dsid);
                daGetBillDetialDao.deleteBankReceipt(bankReceiptmap, ctransactionnum,dsid);
            }
            Bankmap.put("downloadfilenum", 0);

            try
            {
                if (CollectionUtil.isNotEmpty(deletefilecguidlist))
                {
                    ESUtil.deleteSeDetial(deletefilecguidlist, "3");
                }

            }catch (Exception e)
            {
                log.error("银行回单调用ES发生错误：" + e);
                e.printStackTrace();
                log.error("银行回单调用ES发生错误：" + e);
            }
        }
        bankreceiptlist.add(Bankmap);

        rtnmap.put("status",true);
        return rtnmap;
    }

    private static void makeDefaultBillDetial(Map bankmap)
    {
        bankmap.put("ctemplateid_name", "系统收集-银行回单列表");
        bankmap.put("ctemplateid", "da_fc_banksreceipt_list");
        bankmap.put("cpageid", "da_fc_banksreceipt_list");
        bankmap.put("cpageid_name", "系统收集-银行回单列表");
        bankmap.put("cflowstatus", "0");
        bankmap.put("cflowstatus_name", "未匹配");
        bankmap.put("cvoucherstatus", "0");
        bankmap.put("cvoucherstatus_name", "未匹配");

/*        bankmap.put("creceiptstatus", "0");
        bankmap.put("creceiptstatus_name", "未匹配");*/
    }

    public static void databankAndVoucher(Map commonmap, List<Map> bankAndVoucherRellist, Map Bankmap)
    {
        Map bMap = new HashMap<>();

        bMap.put("ctransactionnum", Bankmap.get("ctransactionnum"));
        bMap.put("cvoucherid", commonmap.get("cvoucherid"));
        bMap.put("cvoucode", commonmap.get("cvoucherno"));
        bMap.put("cguid",Guid.g());
        bMap.putAll(makeBatchDefalultValue(commonmap));
        bankAndVoucherRellist.add(bMap);
    }
    public static void  convertree(Map headMap, Map cvmap, List<Map> treelist, HashMap<String, String> treePublicMap, HashMap<String, String> treePrivateMap, String ctype, String invoiceheadid, String bankid, String url, String ishead)
    {
        Map tMap = new HashMap<>();

        for (String key : treePublicMap.keySet())
        {
            if (key == "cparent")
            {
                String cparent = CollectionUtil.getStringFromMap(headMap, CollectionUtil.getStringFromMap(bankCommonMap,"单据id"));
                if (StringUtils.isBlank(cparent))
                {
                    cparent = CollectionUtil.getStringFromMap(headMap, CollectionUtil.getStringFromMap(bankCommonMap,"凭证id"));
                }
                if (StringUtils.isBlank(cparent))
                {
                    tMap.put(key, "000000");
                }else
                {
                    tMap.put(key, cparent);
                }
            }
            else
            {
                tMap.put(key, headMap.get(key));
            }
        }

        for (String key : treePrivateMap.keySet())
        {
            if (!"1".equalsIgnoreCase(ctype)  && ! "cparent".contains(key))
            {
                if ("cmeansname".equalsIgnoreCase(key))
                {
                    tMap.put(key, treePrivateMap.get(key));
                }else
                {
                    tMap.put(key, headMap.get(treePrivateMap.get(key)));
                }
            }else if ("2".equalsIgnoreCase(ctype))
            {
                Object o = headMap.get(treePrivateMap.get(key));
                if (isObjectNotEmpty(o))
                {
                    tMap.put(key, o);

                }else
                {
                    tMap.put(key, headMap.get(treePrivateMap.get(key)));

                    //  tMap.put(key, CollectionUtil.getStringFromMap(headMap, "凭证id"));
                }
            }
            else if ("3".equalsIgnoreCase(ctype))
            {
                tMap.put(key, headMap.get(treePrivateMap.get(key)));
            }
        }

        if ("2".equalsIgnoreCase(ctype))
        {
            tMap.put("cmeansid", invoiceheadid);
            String cmeansname = CollectionUtil.getStringFromMap(headMap, "发票类型");
            if (StringUtils.isNotBlank(cmeansname))
            {
                tMap.put("cmeansname", cmeansname);
            }
        }
        else if ("3".equalsIgnoreCase(ctype))
        {
            tMap.put("cmeansid", bankid);
        }


        tMap.put("ctype", ctype);
        tMap.put("cguid",Guid.g());
        tMap.put("pageurl",url);
        tMap.put("ishead",ishead);
        tMap.putAll(makeBatchDefalultValue(cvmap));
        treelist.add(tMap);
    }

    private static Map makeBatchDefalultValue(Map batchmap)
    {
        Map map = new HashMap<>();
        String corgnid           = CollectionUtil.getStringFromMap(batchmap,"corgnid");
        String corgnid_name      = CollectionUtil.getStringFromMap(batchmap,"corgnid_name");
        String cadminorgnid      = CollectionUtil.getStringFromMap(batchmap,"cadminorgnid");
        String cadminorgnid_name = CollectionUtil.getStringFromMap(batchmap,"cadminorgnid_name");
        String ccreatorid        = CollectionUtil.getStringFromMap(batchmap,"ccreatorid");
        String ccreatorid_name   = CollectionUtil.getStringFromMap(batchmap,"ccreatorid_name");
        String ccreatedate       = CollectionStringUtil.getSysDateTime();
        String csourcesysname   = CollectionUtil.getStringFromMap(batchmap,"csourcesysname");

        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate", ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);

        map.put("ccollectorid",ccreatorid);
        map.put("ccollectorid_name",ccreatorid_name);
        map.put("csourcesysname",csourcesysname);
        return map;
    }
    public static String setBankNotReceiptMap(Map bankmap, Map datamap)
    {
        StringBuffer sb = new StringBuffer();

        for (String key : nbankReceiptMap.keySet())
        {
            String value = nbankReceiptMap.get(key);
            addMap(datamap, key, value, bankmap);
        }
        return sb.toString();
    }

    public static String setBankReceiptMap(Map bankmap, Map datamap)
    {
        StringBuffer sb = new StringBuffer();

        for (String key : bankReceiptMap.keySet())
        {
            String value = bankReceiptMap.get(key);
            if ("本方账户".equalsIgnoreCase(key))
            {
                addMap(datamap, key, value + "_name", bankmap);
            }
            addMap(datamap, key, value, bankmap);
        }

        return sb.toString();
    }

    public static Map getCommonMap(Map commonmap,Map sourceBatch)
    {
        Map newcommonmap = new HashMap<>();
        newcommonmap.putAll(sourceBatch);
        for (String key : bankCommonMap.keySet())
        {
            String value = bankCommonMap.get(key);
            addMap(commonmap,key,value,newcommonmap);
        }
        return newcommonmap;
    }

    private static String addMap(Map commonmap,String key,String value, Map newmap)
    {
        if (commonmap.containsKey(key))
        {
            Object o = commonmap.get(key);
            if ("交易时间".equalsIgnoreCase(key)&&!isObjectEmpty(o))
            {
                //格式化日期。。。。
                newmap.put(value,strToDate(o.toString()));
                return strToDate(o.toString());
            }
            else if ("是否自动关联".equalsIgnoreCase(key))
            {
                if (!isObjectEmpty(o) && "1".equalsIgnoreCase(o.toString()))
                {
                    newmap.put(value, "1");
                    return "1";
                }
            }
            else
            {
                if (!isObjectEmpty(o))
                {
                    if (o instanceof Number)
                    {

                        if (o instanceof Integer)
                        {
                            o = (Integer) o;
                        }else
                        {
                            Double aDouble = (Double) o;
                            o = aDouble.doubleValue();
                        }
                        newmap.put(value,o);
                    }else
                    {
                        newmap.put(value,o);
                    }
                    return String.valueOf(o);
                }
            }
        }
        return "";
    }


    private static String getSysDate(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM");
        String time =s.format(date);
        return time;
    }

    private static String getSysYear(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy");
        String time =s.format(date);
        return time;
    }

    private static Map makestatusValue(Map dMap)
    {
        //组卷状态
        dMap.put("centityfilestatus", "0");
        dMap.put("centityfilestatus_name", "收集成功");
        //归档状态
        dMap.put("ceastatus", "0");
        dMap.put("ceastatus_name", "收集成功");
        //四性检测状态
   /*     dMap.put("isuaistatus", "0");
        dMap.put("csuaistatus", "未检测");*/
        //手工关联状态
        dMap.put("imanualrelstatus", "0");
        dMap.put("cmanualrelstatus", "未关联");

        dMap.put("isuaistatus","1");
        dMap.put("csuaistatus","已检测");

        return dMap;
    }

    public static Map getSXMap(Map datamap, Map batchmap, String caccountclass, List<Map> filelist, String dsid)
    {
        Map sxmap = new HashMap<>();
        sxmap.put("caccountclass", caccountclass);
        sxmap.put("dsid", dsid);
        sxmap.put("currentOrgnId", CollectionUtil.getStringFromMap(batchmap, "corgnid"));
        sxmap.put("currentOrgnName", CollectionUtil.getStringFromMap(batchmap, "corgnid_name"));
        sxmap.put("currentAdminOrgnId", CollectionUtil.getStringFromMap(batchmap, "cadminorgnid"));
        sxmap.put("currentAdminOrgnName", CollectionUtil.getStringFromMap(batchmap, "cadminorgnid_name"));
        sxmap.put("currentUserId", CollectionUtil.getStringFromMap(batchmap, "ccollector"));
        sxmap.put("currentUserName", CollectionUtil.getStringFromMap(batchmap, "ccollector_name"));

        List filel = new ArrayList<>();
        Map filemap = new HashMap<>();
        filemap.put("ctransactionnum", datamap.get("ctransactionnum"));

        filemap.put("fileList", filelist);
        filel.add(filemap);
        sxmap.put("list", filel);
        return sxmap;
    }

    public static Map makeBillErrorInsertSourceBatchDetail(String msg, String ccalljsondetail, Map sourceBatch) {
        String cguid = CollectionUtil.getStringFromMap(sourceBatch, "cguid");
        Map insertSourceBatchDetail = new HashMap<>();
        insertSourceBatchDetail.put("csourcebatchid",cguid);
        insertSourceBatchDetail.put("cflag","0");
        insertSourceBatchDetail.put("ccallcreatedate",getSysTime());
        insertSourceBatchDetail.put("ccalljsondetail", ccalljsondetail);
        insertSourceBatchDetail.put("cmsg",msg);
        makeDefalultValueForInterface(insertSourceBatchDetail,sourceBatch);
        return insertSourceBatchDetail;
    }
    public static Map makeErrorInsertSourceBatchDetail(String msg, Params params, Map sourceBatch) {
        String cguid = CollectionUtil.getStringFromMap(sourceBatch, "cguid");
        Map insertSourceBatchDetail = new HashMap<>();
        insertSourceBatchDetail.put("csourcebatchid",cguid);
        insertSourceBatchDetail.put("cflag","0");
        insertSourceBatchDetail.put("ccallcreatedate",getSysTime());
        insertSourceBatchDetail.put("ccalljsondetail", JsonUtil.toJSON(params.getFieldMap()));
        insertSourceBatchDetail.put("cmsg",msg);
        makeDefalultValueForInterface(insertSourceBatchDetail,sourceBatch);
        return insertSourceBatchDetail;
    }

    public static String makeDefalultValueForInterface(Map map,Map sourceBatch){
        Object corgnid = sourceBatch.get("corgnid");
        Object corgnid_name = sourceBatch.get("corgnid_name");
        Object cadminorgnid = sourceBatch.get("cadminorgnid");
        Object cadminorgnid_name = sourceBatch.get("cadminorgnid_name");
        Object ccreatedate = getSysTime();
        Object ccreatorid = sourceBatch.get("ccreatorid");
        Object ccreatorid_name = sourceBatch.get("ccreatorid_name");
        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate",ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);
        String g = Guid.g();
        map.put("cguid", g);
        return g;
    }

    public static String getSysTime(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time =s.format(date);
        return time;
    }

    private static String strToDate(String datestr){
        Date date = null;
        SimpleDateFormat sdf = new SimpleDateFormat();
        sdf.applyPattern("yyyy-MM-dd");
        try {
            date = sdf.parse(datestr);
        }catch (Exception e){
            e.printStackTrace();
        }

        return sdf.format(date);
    }

    public static void getbanklog(Map bankmap, Map sourceBatch, String dsid, String savemsg)
    {
        Map logMap = new HashMap<>();
        //因银行回单公共字段不需要校验，固只检验实际字段
        Object ctransactionnum = bankmap.get("回单编号");
        logMap.putAll(sourceBatch);
        logMap.put("ctransactionnum", ctransactionnum);
        logMap.put("csyncexceptionmsg", "银行回单【" + ctransactionnum + "】" + savemsg);
        logMap.put("cguid", Guid.g());
        String ccreatedate = CollectionStringUtil.getSysDateTime();
        logMap.put("ccreatedate", ccreatedate);
        String date = CollectionUtil.getStringFromMap(bankmap, "交易时间");
        if (StringUtils.isNotBlank(date)){
            String ctransactiondate = strToDate(date);
            logMap.put("ctransactiondate",ctransactiondate);
        }
        List<Map> loglist = new ArrayList<>();
        loglist.add(logMap);
        daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_banksreceipt_collect_log", loglist,dsid);
    }

    private static Boolean isObjectNotEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isNotEmpty(s);
    }
}
