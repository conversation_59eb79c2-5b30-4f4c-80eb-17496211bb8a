package com.aisino.da.api.service.publicother.impl;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.CollectLogDAOInterface;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.handler.enums.DaDataResult;
import com.aisino.da.api.service.DaGetOtherDetialService;
import com.aisino.da.api.service.publicother.SaveAndCheckOtherDataDetialService;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.api.util.ExtFileUploadUtil;
import com.aisino.da.api.util.MsUtils;
import com.aisino.da.fc.bean.CollectParamsBean;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 银行回单处理
 */
@Service
public class BankReceiptHandler implements SaveAndCheckOtherDataDetialService
{

    @Inject
    private DaGetBillDetialDao daGetBillDetialDao;

    @Inject
    private CollectLogDAOInterface collectLogDAOInterface;

    @Inject
    private DaGetOtherDetialService daGetOtherDetialService;

    /**
     * 银行回单处理入口
     * @param params
     * @return
     */
    @Override
    public Map collectOtherDetial(Params params)
    {
        CollectParamsBean collectParamsBean = new CollectParamsBean();
        String startTime = BankReceiptConveService.getSysTime();
        collectParamsBean.setStartTime(startTime);

        String ccallcreatedate = CollectionStringUtil.getSysDateTime();
        String code = params.getString("编码");
        String serialnumber = params.getString("同步序列号");
        String extcode = params.getString("来源系统编码");
        String msg = params.getString("业务异常信息");

        int totalNumTemp = params.getInt("总批次");
        if (totalNumTemp <= 0)
        {
            //当未告知批次时默认只需要入库一批
            totalNumTemp = 1;
        }

        Map msgmap = new HashMap<>();
        //code 为000000 默认为正常返回数据
        String dsid = params.getString("数据源");
        Map sourceBatch = collectLogDAOInterface.getSourceBatch(serialnumber, extcode, dsid);
        if(CollectionUtil.isBlankMap(sourceBatch))
        {
            msg = "未获取到" + serialnumber + "批次收集发起记录收集";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            return msgmap;
        }
        int ibatchtotalnum = CollectionUtil.getIntFromMap(sourceBatch, "ibatchtotalnum");
        if (ibatchtotalnum < 1)
        {
            sourceBatch.put("ibatchtotalnum",totalNumTemp);
        }

        String csynstatus = CollectionUtil.getStringFromMap(sourceBatch, "csynstatus");
        String sourceBatchId = CollectionUtil.getStringFromMap(sourceBatch, "cguid");
        if (CollectConstant.COLLECTION_COMPLETED_CODE.equalsIgnoreCase(csynstatus))
        {
            msg = "该批次数据已收集完成请勿重复提交";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            return msgmap;
        }

        ApsContextDb apsContext = new ApsContextDb();
        DbService db = apsContext.getDb(dsid);

        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
        List<String> bankAccount = daGetOtherDetialService.getBankAccountByDsid(corgnid, dsid);
        if(CollectionUtil.isEmpty(bankAccount))
        {
            msg = "收集该批次数据内系统内未配置当前公司银行账户无法完成收集";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            //更新为收集完成
            Map updateMap = new HashMap<>();
            String sysDateTime = CollectionStringUtil.getSysDateTime();
            updateMap.put("cendtime", sysDateTime);

            //生成日志明细
            updateMap.put("csynstatus", CollectConstant.COLLECTION_COMPLETED_CODE);
            updateMap.put("csynstatus_name", CollectConstant.COLLECTION_COMPLETED);
            updateMap.put("msg",msg);
            updateMap.put("ccallcreatedate",ccallcreatedate);
            String billdetialjson = JsonUtil.toJSON(params);
            updateMap.put("ccalljsondetail",billdetialjson);
            updateExtSourceBatch(updateMap, sourceBatchId,dsid);
            db.commit();
            return msgmap;
        }
        //若总批次大于一需要判断数据已入库数量,超量数据不予入库;
        int SourceBatchDetailcount = collectLogDAOInterface.querySourceBatchDetail(sourceBatchId, dsid);
        if (totalNumTemp >= 1)
        {
            //查询已入库数据数
            if (SourceBatchDetailcount >= totalNumTemp)
            {
                msg = "已收集" + totalNumTemp + "批数据,请勿重复请求";
                msgmap.put("code","1000");
                msgmap.put("msg",msg);
                Map insertSourceBatchDetail = BankReceiptConveService.makeErrorInsertSourceBatchDetail(msg, params, sourceBatch);
                collectLogDAOInterface.insertSourceBatchDetail(insertSourceBatchDetail, collectParamsBean);
                db.commit();
                return msgmap;
            }
        }

        int cout = 0;
        int failcount = 0;
        int successcount = 0;

        //更新状态数据入库
        updateExtSourceBatchStatus(CollectConstant.COLLECTING,CollectConstant.COLLECTING_CODE, totalNumTemp ,sourceBatchId, dsid);
        Map<String, String> updateMap = new HashMap<>();
        String billdetialjson = JsonUtil.toJSON(params);
        updateMap.put("ccalljsondetail",billdetialjson);

        if (StringUtils.isNotBlank(code) && "000000".equalsIgnoreCase(code))
        {
            //开始回单数据入库
            List<Map> banklist = params.getListMap("银行详情列表");
            if (CollectionUtil.isEmpty(banklist))
            {
                msg = "获取【银行详情列表】数据未获取到" + params.getFieldMap().toString();
            }else
            {
                //通过组织id判断当前组织数据是否需要四性检测;
                boolean canSXcheck = ExtFileUploadUtil.getCanSXcheck(corgnid, new ApsContextDb(dsid));
                //获取配置的文件存储方式
                Map stomedMapByExtCode = daGetBillDetialDao.getStomedMapByExtCode(extcode, dsid);
                String param = CollectionUtil.getStringFromMap(sourceBatch, "param");
                boolean iscover = false;
                if (StringUtils.isNotBlank(param))
                {
                    Map paramMap = JsonUtil.fromJSON(param, Map.class);
                    iscover = CollectionUtil.getBoolean(paramMap, "iscover", false);
                }else
                {
                    iscover = CollectionUtil.getBoolean(sourceBatch, "icover", false);
                }
                DaDataResult daDataResult = datacheckAndSaveByBankReceipt(banklist, sourceBatch, dsid, canSXcheck,stomedMapByExtCode, bankAccount,iscover);
                //取出本批数据处理结果
                cout = daDataResult.count;
                failcount = daDataResult.failcount;
                successcount = daDataResult.successcount;

                if (daDataResult.cstatus)
                {
                    String collmsg = daDataResult.getCollmsg("银行回单");
                    msg = daDataResult.getMsg() + daDataResult.getSxmsg();
                    msg = collmsg + "详情:" + msg;
                }
                else
                {
                    msg = daDataResult.getMsg() + daDataResult.getSxmsg();
                }
            }
        }else
        {
            msg = params.getString("业务异常信息");;
        }
        //返回之前更新回调信息
        String sysDateTime = CollectionStringUtil.getSysDateTime();
        updateMap.put("cendtime", sysDateTime);
        //明细入库时,计算数据总数，跟成功的数据数。。。。。。。。
        Map<String, Integer> numbermap = daGetBillDetialDao.getNumber(sourceBatchId,dsid);
        //查询总数..
        Integer icollectnum = CollectionUtil.getIntFromMap(numbermap,"icollectnum");//记录的数据总数
        Integer icollectsuccessnum = CollectionUtil.getIntFromMap(numbermap,"icollectsuccessnum");//记录的成功数据总数

        //生成日志明细
        Map insertSourceBatchDetail = BankReceiptConveService.makeErrorInsertSourceBatchDetail(msg, params, sourceBatch);
        //明细存储总数
        insertSourceBatchDetail.put("icollectnum",cout);
        insertSourceBatchDetail.put("icollectsuccessnum",successcount);
        insertSourceBatchDetail.put("syserrormsg", params.getString("系统错误信息", ""));
        insertSourceBatchDetail.put("businesserrormsg", params.getString("业务异常信息", ""));

        collectLogDAOInterface.insertSourceBatchDetail(insertSourceBatchDetail, collectParamsBean);

        //数据入库之前判断数据库批次
        SourceBatchDetailcount = collectLogDAOInterface.querySourceBatchDetail(sourceBatchId, dsid);

        if (!CollectConstant.CANCELLATION_CODE.equalsIgnoreCase(csynstatus) && (totalNumTemp == 1 || SourceBatchDetailcount >= totalNumTemp))
        {
            //单批次数据处理完成认为数据入库处理完成不允许
            updateMap.put("csynstatus", CollectConstant.COLLECTION_COMPLETED_CODE);
            updateMap.put("csynstatus_name", CollectConstant.COLLECTION_COMPLETED);

        }else
        {
            updateMap.put("csynstatus", CollectConstant.COLLECTING_CODE);
            updateMap.put("csynstatus_name",CollectConstant.COLLECTING);
            //多批次情况
        }

        //放入总的提示信息
        int ctotal = cout +icollectnum;
        int succestotal = successcount + icollectsuccessnum;
        String countmsg = "银行回单共收集" + ctotal + "条,\n\r成功" + succestotal + "条,\n\r失败" + (ctotal - succestotal)  + "条";

        updateMap.put("msg",countmsg);
        updateMap.put("ccallcreatedate",ccallcreatedate);

        updateExtSourceBatch(updateMap, sourceBatchId,dsid);
        msgmap.put("code","1000");
        msgmap.put("msg","数据处理完成：" + msg);
        db.commit();
        return msgmap;
    }

    public void updateExtSourceBatchStatus(String name, String code, int ibatchtotalnum, String cguid, String dsid) {
        daGetBillDetialDao.updateExtSourceBatchStatus(name, code, ibatchtotalnum ,cguid,dsid);
    }

    public void updateExtSourceBatch(Map updateMap, String cguid,String dsid)
    {
        daGetBillDetialDao.upadteExtSourceBatchByCZY(updateMap, cguid, dsid);
    }

    private DaDataResult datacheckAndSaveByBankReceipt(List<Map> banklist, Map sourceBatch, String dsid, boolean canSXcheck, Map stomedMapByExtCode, List<String> bankAccount, boolean iscover)
    {
        //数据处理之前查询匹配规则调用ms
        Map relatedRelationships = MsUtils.getRelatedRelationships("银行回单", dsid);
        List<Map> billRelated = (List<Map>)relatedRelationships.get("单据资料");
        List<Map> vouRelated = (List<Map>)relatedRelationships.get("记账凭证");

        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
        StringBuffer stringBuffer = new StringBuffer();
        int count = banklist.size();
        int failcount = 0;
        int successcount = 0;
        for (Map bigbankmap : banklist)
        {
            //提取凭证id，凭证id为空认为需要填充数据   bankCommonMap.put("凭证id","cvoucherid");
            String cvoucherid = CollectionUtil.getStringFromMap(bigbankmap, "凭证id");
            String ctransactionnum = CollectionUtil.getStringFromMap(bigbankmap, "回单编号");
            String code = "";

            List<Map> voucherZlDetial =daGetBillDetialDao.getVoucherZlDetial(CollectConstant.BANKRECEIPT_NAME, ctransactionnum, code, corgnid, dsid, false);

            List<Map> nRelatedLsist = new ArrayList<>();
           // nRelatedLsist.addAll(voucherZlDetial);
            if (StringUtils.isBlank(cvoucherid))
            {
                Map bankDetial = getBankDetial(bigbankmap);
                //查询关联单据,
                List<Map> relatedBill = daGetBillDetialDao.getRelatedBill(billRelated, corgnid, dsid, bankDetial);
                //查询关联凭证
                List<Map> relatedVo = daGetBillDetialDao.getRelatedVou(vouRelated, corgnid, dsid, bankDetial);
                ArrayUtil.dRemoval(relatedBill, relatedVo, nRelatedLsist);
                if (CollectionUtil.isNotEmpty(nRelatedLsist) && nRelatedLsist.size() > 0)
                {
                    nRelatedLsist.stream().forEach(map -> map.put("是否自动关联", "0"));
                }
            }

             if (CollectionUtil.isNotEmpty(nRelatedLsist) && CollectionUtil.isNotEmpty(voucherZlDetial))
             {
                 List<Map> nnRelatedLsist = new ArrayList<>();
                 ArrayUtil.dRemoval(voucherZlDetial, nRelatedLsist , nnRelatedLsist);
                 nRelatedLsist = nnRelatedLsist;
             }else if (CollectionUtil.isNotEmpty(voucherZlDetial))
             {
                 nRelatedLsist.addAll(voucherZlDetial);
             }

            //判断查询到关联关系是否有原有凭证关联关系，如果有不再放入。
            boolean isDuplicate = false;
            if (CollectionUtil.isNotEmpty(nRelatedLsist) )
            {
                isDuplicate = nRelatedLsist.stream()
                        .anyMatch(e -> e.get("凭证id").equals(bigbankmap.get("凭证id")));
            }

            if (!isDuplicate && ((CollectionUtil.isNotEmpty(nRelatedLsist) && StringUtils.isNotBlank(cvoucherid)) || (CollectionUtil.isEmpty(nRelatedLsist))))
            {
                //中放入原有凭证信息。。
                Map vomap = new HashMap<>();
                vomap.put("凭证id", bigbankmap.get("凭证id"));
                vomap.put("凭证编号", bigbankmap.get("凭证编号"));
                vomap.put("凭证日期", bigbankmap.get("凭证日期"));
                vomap.put("是否自动关联", "0");
                nRelatedLsist.add(vomap);
            }
            if (CollectionUtil.isNotEmpty(nRelatedLsist) && nRelatedLsist.size() > 0)
            {
                count = count + nRelatedLsist.size() - 1;
                for (Map relatedMap : nRelatedLsist)
                {
                    bigbankmap.putAll(relatedMap);
                    //校验数据，通过的可以入库,若校验未通过增加日志
                    String checkmsg = BankReceiptConveService.CheckDataByBankReceipt(bigbankmap, sourceBatch, dsid, bankAccount,iscover);

                    if (checkmsg.length() > 0)
                    {
                        stringBuffer.append(checkmsg);
                        failcount++;

                    }else
                    {
                        //数据入库
                        DaDataResult daDataResult = BankReceiptConveService.SaveDataByBankReceipt(bigbankmap, sourceBatch, dsid,canSXcheck, canSXcheck, stomedMapByExtCode);
                        if (!daDataResult.getCstatus())
                        {
                            String msg = daDataResult.getMsg();
                            if ("成功".equals(msg) || StringUtils.isBlank(msg))
                            {
                                stringBuffer.append("四性检测异常").append(daDataResult.getSxmsg());

                            }
                            stringBuffer.append("数据入库异常，").append(msg);
                            failcount++;

                        }else
                        {
                            successcount++;
                        }
                    }
                }
            }else
            {
                //校验数据，通过的可以入库,若校验未通过增加日志
                String checkmsg = BankReceiptConveService.CheckDataByBankReceipt(bigbankmap, sourceBatch, dsid, bankAccount,iscover);

                if (checkmsg.length() > 0)
                {
                    stringBuffer.append(checkmsg);
                    failcount++;

                }else
                {
                    //数据入库
                    DaDataResult daDataResult = BankReceiptConveService.SaveDataByBankReceipt(bigbankmap, sourceBatch, dsid,canSXcheck, canSXcheck, stomedMapByExtCode);
                    if (!daDataResult.getCstatus())
                    {
                        String msg = daDataResult.getMsg();
                        if ("成功".equals(msg) || StringUtils.isBlank(msg))
                        {
                            stringBuffer.append("四性检测异常").append(daDataResult.getSxmsg());

                        }
                        stringBuffer.append("数据入库异常，").append(msg);
                        failcount++;

                    }else
                    {
                        successcount++;
                    }
                }
            }
        }
        return new DaDataResult().sucess("银行回单收集完成"  + stringBuffer).addcount(count).addsuccesscount(successcount).addfailcount(failcount);
    }

    private Map getBankDetial(Map bigbankmap)
    {
        Map bankmap = new HashMap<>();
        BankReceiptConveService.setBankReceiptMap(bankmap, bigbankmap);
        //填充非必填字段
        BankReceiptConveService.setBankNotReceiptMap(bankmap, bigbankmap);

        BankReceiptConveService.getCommonMap(bigbankmap, bankmap);

        return BankReceiptConveService.getCommonMap(bigbankmap, bankmap);
    }

}
