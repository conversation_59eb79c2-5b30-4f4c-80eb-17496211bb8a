package com.aisino.da.api.service.fkbill.impl;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.entity.EsAddViewVO;
import com.aisino.da.api.entity.EsDeleteViewVO;
import com.aisino.da.api.entity.EsResultViewVO;
import com.aisino.da.api.service.fkbill.DataConveService;
import com.aisino.da.api.service.fkbill.DataHandlerTicketFileUtil;
import com.aisino.da.api.service.fkbill.SaveAndCheckDataDetialService;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.api.util.ExtFileUploadUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class JYExpenseBillHandler implements SaveAndCheckDataDetialService
{

    @Inject
    private DaGetBillDetialDao daGetBillDetialDao;

    //单据主表
    static HashMap<String, String> billHeadMap = new HashMap<>();
    //单据子表
    static HashMap<String, String> billBodyMap = new HashMap<>();
    //发票主表
    static HashMap<String, String> invoiceHeadMap = new HashMap<>();
    //发票子表
    static HashMap<String, String> invoiceBodyMap = new HashMap<>();
    //银行回单表
    static HashMap<String, String> bankReceiptMap = new HashMap<>();
    //凭证必填
    static HashMap<String, String> voucherRequiredMap = new HashMap<>();
    //单据主表必填
    static HashMap<String, String> billHeadRequiredMap = new HashMap<>();
    //单据子表必填
    static HashMap<String, String> billBodyRequiredMap = new HashMap<>();
    //发票主表必填
    static HashMap<String, String> invoiceHeadRequiredMap = new HashMap<>();
    //发票子表必填
    static HashMap<String, String> invoiceBodyRequiredMap = new HashMap<>();
    //银行回单表必填
    static HashMap<String, String> bankReceiptRequiredMap = new HashMap<>();

    //单据主表必填长度
    static HashMap<String, String> billHeadLengthMap = new HashMap<>();
    //单据子表必填长度
    static HashMap<String, String> billBodyLengthMap = new HashMap<>();
    //发票主表必填长度
    static HashMap<String, String> invoiceHeadLengthMap = new HashMap<>();
    //发票子表必填长度
    static HashMap<String, String> invoiceBodyLengthMap = new HashMap<>();
    //银行回单表必填长度
    static HashMap<String, String> bankReceiptLengthMap = new HashMap<>();
    //树公共字段
    static HashMap<String, String> treePublicMap = new HashMap<>();
    //树单据字段
    static HashMap<String, String> treeBillMap = new HashMap<>();
    //银行树
    static HashMap<String, String> treeBankMap = new HashMap<>();
    //发票树
    static HashMap<String, String> treeInvoiceMap = new HashMap<>();
    //整体公共字段
    static HashMap<String, String> billPublicMap = new HashMap<>();
    //文件表
    static HashMap<String, String> daFileMap = new HashMap<>();
    //公共字段
    static HashMap<String, String> publicCommonMap = new HashMap<>();
    //日志字段
    static HashMap<String, String> logMap = new HashMap<>();

    static
    {
        billHeadMap.put("单据id","oldcguid");
        billHeadMap.put("页面id","oldpageid");
        billHeadMap.put("费用组织id","cexpensecompanyguid");
        billHeadMap.put("费用组织名称","cexpensecompanyguid_name");
        billHeadMap.put("单据模板名称","cbillformwork_name");
        billHeadMap.put("单据编号","cbillcode");
        billHeadMap.put("单据日期","ddate");
        billHeadMap.put("事由","creason_name");

        billHeadMap.put("经办公司","ccompanyguid_name");
        billHeadMap.put("经办人部门","cdeptguid_name");
        billHeadMap.put("供应商","csupplierguid_name");
        billHeadMap.put("单据影像数","iimgqty");
        billHeadMap.put("单据附件个数","iticketqty");
        billHeadMap.put("银行回单数","ibankqty");
        billHeadMap.put("支付单号","cpaybillcode");
        billHeadMap.put("支付公司","cpaycompanyguid_name");
        billHeadMap.put("支付人","cpayerguid_name");
        billHeadMap.put("制单人","ccreatorname");

        billHeadMap.put("报销金额","itotalamt");
        billHeadMap.put("制单人部门","ccreatordeptname");
        billHeadMap.put("制单人部门id","ccreatordeptid");
        billHeadMap.put("制单人id","ccreatornameid");
        billHeadMap.put("上游单据页面id","sybillpageid");
        billHeadMap.put("报销人id","cempguid");
        billHeadMap.put("经办人","cempguid_name");
        billHeadMap.put("审核人","cauditusername");

        billHeadMap.put("附件个数","ifjnum");

        billBodyMap.put("主表id","oldheadid");
        billBodyMap.put("子表id","oldcguid");
        billBodyMap.put("费用类型","cexpenseclass");
        billBodyMap.put("项目","cproject");
        billBodyMap.put("费用承担部门","caccdept");
        billBodyMap.put("报销金额","iamt");
        billBodyMap.put("税率","itax");
        billBodyMap.put("税额","itaxamt");


        billHeadRequiredMap.put("费用组织id","费用组织id");
        billHeadRequiredMap.put("费用组织名称","费用组织名称");
        billHeadRequiredMap.put("单据id","单据id");
        billHeadRequiredMap.put("单据模板名称","单据模板名称");

        voucherRequiredMap.put("凭证id","凭证id");
        voucherRequiredMap.put("凭证日期","凭证日期");
        voucherRequiredMap.put("凭证编号","凭证编号");

        billBodyRequiredMap.put("主表id","主表id");
        billBodyRequiredMap.put("子表id","子表id");

        billHeadLengthMap.put("id","30");
        billHeadLengthMap.put("其它","500");
        billHeadLengthMap.put("事由","5000");

        billBodyLengthMap.put("id","30");
        billBodyLengthMap.put("其它","500");
        billBodyLengthMap.put("事由","5000");


        billPublicMap.put("凭证id","cvoucherid");
        billPublicMap.put("凭证日期","cvoucherdate");
        billPublicMap.put("凭证编号","cvoucherno");
        billPublicMap.put("外系统凭证号","coutvounumber");
        billPublicMap.put("上游单据日期","sybilldate");
        billPublicMap.put("上游单据id","sybillid");
        billPublicMap.put("分表字段","cfiles_table_name");
        billPublicMap.put("文件数","ifileqty");
        billPublicMap.put("上游单据编号","sybillcode");
        billPublicMap.put("会计年","iyear");
        billPublicMap.put("会计月","imonth");
        billPublicMap.put("会计年月","iyearandmonth");

        daFileMap.put("文件对象存储文件名","filename");
        daFileMap.put("文件类型","cfiletype");
        daFileMap.put("文件哈希码","mdnumber");
        daFileMap.put("文件哈希类型","chxtype");
        daFileMap.put("单据id","cbillid");
        daFileMap.put("文件实际名称","cfilerealname");
        daFileMap.put("银行回单id","oldbankid");
        daFileMap.put("文件来源类型","cfilesourcetype");
        daFileMap.put("文件访问地址","fileurl");

        daFileMap.put("桶名","dxccname");
        daFileMap.put("文件收集方式","filecollmethod");
        daFileMap.put("存储方式","storagemethod");
        daFileMap.put("路径","filepath");

        daFileMap.put("载体类型","carriertype");
        daFileMap.put("排序字段","csortindex");

        publicCommonMap.put("单据id","billid");
        publicCommonMap.put("单据模板名称","cbillformwork_name");
        publicCommonMap.put("单据编号","cbillcode");
        publicCommonMap.put("单据日期","ddate");
        publicCommonMap.put("事由","creason_name");
        publicCommonMap.put("单据影像数","iimgqty");
        publicCommonMap.put("附件数","iappqty");
        publicCommonMap.put("文件数","ifileqty");
        publicCommonMap.put("制单人","ccreatorname");
        publicCommonMap.put("报销人id","cempguid");
        publicCommonMap.put("经办人","cempguid_name");
        publicCommonMap.put("报销金额","iamt");
        publicCommonMap.put("页面id","oldpageid");
        publicCommonMap.put("经办人部门","cdeptguid_name");

        bankReceiptMap.put("主表id","cheadid");
        bankReceiptMap.put("关联单据id","cbillguid");
        bankReceiptMap.put("关联单据页面id","cbillpageid");
        bankReceiptMap.put("交易金额","itransactionamount");
        bankReceiptMap.put("银行流水号","bankserial");
        bankReceiptMap.put("摘要","cremark");
        bankReceiptMap.put("对方户名","ccounteraccname");
        bankReceiptMap.put("对方账户","ccounteraccount");
        bankReceiptMap.put("对方银行","ccounterbank");
        bankReceiptMap.put("电子回单号","ctransactionnum");
        bankReceiptMap.put("本方户名","caccountname");
        bankReceiptMap.put("本方账户","cbankaccount");
        bankReceiptMap.put("本方银行","cbank");
        bankReceiptMap.put("交易时间","ctransactiondate");
        bankReceiptMap.put("系统收集","datasource");

        //bankReceiptRequiredMap.put("电子回单号","");
        bankReceiptRequiredMap.put("交易时间","");
        //bankReceiptRequiredMap.put("摘要","");
        bankReceiptRequiredMap.put("交易金额","");
        bankReceiptRequiredMap.put("本方户名","");
        bankReceiptRequiredMap.put("本方账户","");
        bankReceiptRequiredMap.put("本方银行","");
        bankReceiptRequiredMap.put("对方户名","");
        bankReceiptRequiredMap.put("对方账户","");
        bankReceiptRequiredMap.put("对方银行","");


        invoiceHeadLengthMap.put("id","30");
        invoiceHeadLengthMap.put("其它","500");
        invoiceHeadLengthMap.put("备注","5000");

        invoiceBodyLengthMap.put("id","30");
        invoiceBodyLengthMap.put("其它","500");
        invoiceBodyLengthMap.put("备注","5000");

        bankReceiptLengthMap.put("id","30");
        bankReceiptLengthMap.put("其它","500");
        bankReceiptLengthMap.put("摘要","5000");
        bankReceiptLengthMap.put("事由","5000");

        treePublicMap.put("cfiletablename","cfiles_table_name");
        treePublicMap.put("cparent","cvoucherid");
        treePublicMap.put("cvoucherid","cvoucherid");
        treePublicMap.put("sybillid","sybillid");

        treeBillMap.put("cmeansid","单据id");
        treeBillMap.put("cmeanscode","单据编号");
        treeBillMap.put("cbillpageid","页面id");

        treeBankMap.put("cmeanscode","电子回单号");
        treeBankMap.put("cmeansname","银行回单");
        treeBankMap.put("cparent","关联单据id");

        treeInvoiceMap.put("cmeanscode","发票号码");
        treeInvoiceMap.put("cmeansname","票据");
        treeInvoiceMap.put("cparent","单据id");

        invoiceHeadMap.put("影像表id","oldcguid");
        invoiceHeadMap.put("主表id","cbillguid");
        invoiceHeadMap.put("子表id","cbilllineguid");
        invoiceHeadMap.put("发票大类","cclass");
        invoiceHeadMap.put("发票类型","ctype");

        invoiceHeadMap.put("发票大类编码","cclassid");
        invoiceHeadMap.put("发票类型编码","ctypeid");

        invoiceHeadMap.put("发票号码","cnumber");
        invoiceHeadMap.put("发票代码","ccode");
        invoiceHeadMap.put("开票日期","ddate");
        invoiceHeadMap.put("总金额","iamt");
        invoiceHeadMap.put("价税合计","itotal");

        invoiceHeadMap.put("乘车人","passenger");
        invoiceHeadMap.put("销售方名称","csellername");
        invoiceHeadMap.put("销售方纳税人识别号","csellertaxid");
        invoiceHeadMap.put("销售方地址电话","cseller_addr_tel");
        invoiceHeadMap.put("销售方开户行及账户","cseller_bank_account");

        invoiceHeadMap.put("购买方名称","cbuyername");
        invoiceHeadMap.put("购买方纳税人识别号","cbuyertaxid");
        invoiceHeadMap.put("购买方地址电话","cbuyer_addr_tel");
        invoiceHeadMap.put("购买方开户行及账户","cbuyer_bank_account");
        invoiceHeadMap.put("省份","province");

        invoiceHeadMap.put("机器码","machinecode");
        invoiceHeadMap.put("收款人","iclerk");
        invoiceHeadMap.put("复核人","review");
        invoiceHeadMap.put("备注","cremark");
        invoiceHeadMap.put("查验状态","ccheckresult");

        invoiceHeadMap.put("查验时间","dcheckdate");
        invoiceHeadMap.put("核对结果","checkresult");
        invoiceHeadMap.put("核对方式","checkmode");
        invoiceHeadMap.put("来源","cfrom");
        invoiceHeadMap.put("是否OCR识别","isocr");

        invoiceBodyMap.put("开票明细","goodsname");
        invoiceBodyMap.put("规格型号","specifications");
        invoiceBodyMap.put("单位","measureunits");
        invoiceBodyMap.put("数量","numbers");
        invoiceBodyMap.put("单价","notaxprice");
        invoiceBodyMap.put("不含税金额","goodsamount");
        invoiceBodyMap.put("税率","taxrate");
        invoiceBodyMap.put("税额","goodstaxamount");

        invoiceHeadRequiredMap.put("主表id","主表id");
/*
        invoiceHeadRequiredMap.put("子表id","子表id");
*/

        logMap.put("制单人","ccreatorname");
        logMap.put("报销金额","iamt");
        logMap.put("事由","creason_name");
        logMap.put("单据日期","ddate");
        logMap.put("单据id","billid");
        //logMap.put("制单人部门id","cdeptguid");
        logMap.put("单据编号","cbillcode");
        logMap.put("报销人id","cempguid");
        logMap.put("经办人","cempguid_name");
        logMap.put("经办人部门","cdeptguid_name");
        logMap.put("单据模板名称","cbillformwork_name");

    }
    @Override
    public EsAddViewVO CheckAndSaveBillDetial(List<Map> details, Map logmap, String dsid, boolean isRelated, boolean related)
    {
        EsAddViewVO esAddViewVO = new EsAddViewVO();

        List<Map> daheadlist      = new ArrayList<>(); //默认表存储公共字段
        List<Map> treelist = new ArrayList<>(); //树 //默认表存储公共字段
        List<Map> billheadlist    = new ArrayList<>(); //单据主表
        List<Map> billbodylist    = new ArrayList<>(); //单据子表

        List<Map> bankreceiptlist = new ArrayList<>(); //银行回单

        List<Map> invoicelist     = new ArrayList<>(); //发票
        List<Map> invoicebodylist = new ArrayList<>(); //发票

        List<Map> bankAndVoucherRellist = new ArrayList<>(); //凭证回单关系


        List<Map> billAndVoucherRellist = new ArrayList<>(); //单据凭证关系

        List<Map> ticketAndVoucherRellist = new ArrayList<>(); //凭证票据关系


        String sourceBatchCguid = CollectionUtil.getStringFromMap(logmap, "cguid");
        Map<String, List> allfilemap = new HashMap<>();
        List<Map> errormap = new ArrayList<>();

        for (Map billdetial: details)
        {
            List<Map> filelist = new ArrayList<>(); //文件

            Map cvmap = DataConveService.getDetialMap(billdetial,billPublicMap, logmap);
            cvmap.putAll(logmap);
            cvmap.remove("cguid");
            cvmap.put("csourcesysname", CollectionUtil.getStringFromMap(logmap, "csourcesysname"));
             //存储数据发票信息
            List<Map> invoicefilelist = dataInvoiceSave(billdetial, invoicelist, treelist, invoicebodylist, cvmap, dsid, filelist, ticketAndVoucherRellist, isRelated);

            int sum = 0;
            if (CollectionUtil.isNotEmpty(invoicefilelist) && invoicefilelist.size() > 0)
            {
                for (Map filemap : invoicefilelist) {
                    Map rtnmap = ExtFileUploadUtil.dowloadFile(filemap, new HashMap(), false);
                    boolean status = CollectionUtil.getBoolean(rtnmap, "status", false);
                    //若为true代表文件下载成功若四性检测可以进行检测
                    if (!status)
                    {
                        errormap.add(filemap);
                        filelist.add(filemap);
                    }else
                    {
                        //将下载文件成功的map 放入新的list以便数据入库
                        filemap.put("cstatus", "1");
                        filemap.put("cstatus_name", "收集成功");
                        filemap.put("storagemethod", "default");
                        filemap.put("cfilessize", rtnmap.get("cfilessize"));
                        //增加ES字段
                        filemap.put("cfileid", filemap.get("cguid"));
                        filemap.put("cfilenum", "");
                        filemap.put("cqznum","");
                        filemap.put("caccountclassname","");
                        filemap.put("caccountclasscode","");
                        filemap.put("cdetailclassname","");
                        filemap.put("cdetailclasscode","");
                        filemap.put("cperioddate","");
                        filemap.put("ccustody_year_name","");
                        filemap.put("cfilecontents", filemap.get("cfilecontents"));
                        filemap.put("cbusinesstype", filemap.get("cbusinesstype"));

                        filelist.add(filemap);
                        sum++;
                    }
                }
            }

            //存储数据单据信息
            Map map = dataSave(billdetial, daheadlist, treelist, billheadlist, billbodylist, bankreceiptlist, cvmap, sourceBatchCguid, dsid, filelist, bankAndVoucherRellist,isRelated, sum);

            DataConveService.databillAndVoucher(cvmap,billAndVoucherRellist, billdetial);

            if (CollectionUtil.isNotEmpty(filelist))
            {
                String daFileTableName = daGetBillDetialDao.getDaFileTableName(CollectionUtil.getStringFromMap(cvmap,"cfiles_table_name"));
                //构建文件相关校验
                Map sxcheckmap = DataConveService.getSXMap(map, logmap ,"BILL", filelist,dsid);
                //调用四性检测;
                String check = DataConveService.SXCheck(sxcheckmap);
                if (StringUtils.isNotEmpty(check))
                {
                    if (StringUtils.isNotEmpty(check))
                    {
                        esAddViewVO.setMsg(check);
                        return esAddViewVO;
                    }
                }
                if (allfilemap.containsKey(daFileTableName))
                {
                    List list = allfilemap.get(daFileTableName);
                    list.addAll(filelist);
                    allfilemap.put(daFileTableName, list);
                }else {
                    //
                    allfilemap.put(daFileTableName, filelist);
                }
            }
        }

        if (CollectionUtil.isNotEmpty(errormap) && errormap.size() > 0)
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_file_error_log", errormap,dsid);
        }

        if (!CollectionUtil.isBlankMap(allfilemap))
        {
            for (String tablename : allfilemap.keySet())
            {
                List list = allfilemap.get(tablename);
                esAddViewVO.add(list);
                daGetBillDetialDao.inserDetialByTableNameByCZY(tablename, list, dsid);
            }
        }

        if (CollectionUtil.isNotEmpty(invoicelist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_invoice", invoicelist,dsid);
        }

        if (CollectionUtil.isNotEmpty(invoicebodylist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_invoiceline", invoicebodylist,dsid);
        }

        if (CollectionUtil.isNotEmpty(billheadlist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fk_expensebill", billheadlist,dsid);
        }

        if (CollectionUtil.isNotEmpty(billbodylist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fk_expensebill_detail", billbodylist,dsid);
        }

        if (CollectionUtil.isNotEmpty(bankreceiptlist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_banksreceipt", bankreceiptlist,dsid);
        }

        if (CollectionUtil.isNotEmpty(treelist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fview_vouandbillrela", treelist,dsid);
        }
        if (CollectionUtil.isNotEmpty(daheadlist))
        {

            daGetBillDetialDao.inserDetialByTableNameByCZY("da_api_fk_file_collection", daheadlist,dsid);

        }
        if (CollectionUtil.isNotEmpty(billAndVoucherRellist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_billAndVoucherRel", billAndVoucherRellist,dsid);
        }

        if (CollectionUtil.isNotEmpty(ticketAndVoucherRellist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_voucherandticketrel", ticketAndVoucherRellist,dsid);
        }

        if (CollectionUtil.isNotEmpty(daheadlist))
        {
            for (Map map : daheadlist) {
                String cvoucherid = CollectionUtil.getStringFromMap(map, "cvoucherid");
                DataConveService.updateSUAIStatusMs(cvoucherid);
                DataConveService.updateIntegrityStatusUpdateMs(map);

            }
        }
        return esAddViewVO;
    }

    private List<Map> dataInvoiceSave(Map billdetial, List<Map> invoicelist, List<Map> treelist, List<Map> invoicebodylist, Map cvmap, String dsid, List<Map> filelist, List<Map> ticketAndVoucherRellist, boolean isRelated)
    {
        Object invoicedetiallist = billdetial.get("invoicelist");
        Object headlist = billdetial.get("headlist");
        Map headMap = (Map)headlist;
        String oldbillid = CollectionUtil.getStringFromMap(headMap, "单据id");
        //获取主单据日期
        String billdate = CollectionUtil.getStringFromMap(headMap, "单据日期");
        String billInterval = ArrayUtil.getDataInterval(billdate);

        String cempguidname = CollectionUtil.getStringFromMap(headMap, "报销人");
        List<Map> invoicefilelist = new ArrayList<>();
        if (invoicedetiallist != null && invoicedetiallist instanceof List)
        {
            List<Map> oinvoicelist= (ArrayList<Map>)invoicedetiallist;

            for (Map invoicemap : oinvoicelist)
            {
                StringBuffer invoicesb = new StringBuffer();
                String invoiceheadid = Guid.g();
                String headstr = DataConveService.converInvoiceHead(invoicemap, invoiceHeadMap, cvmap, invoiceheadid, invoicelist, cempguidname, headMap, ticketAndVoucherRellist, isRelated);
                invoicesb.append(headstr);
                Object bodylist = invoicemap.get("invoicedetiallist");
                StringBuffer stringBuffer = new StringBuffer();
                if (bodylist != null && bodylist instanceof  List)
                {
                    List<Map> invoiceblist= (ArrayList<Map>)bodylist;

                    for (Map bodymap : invoiceblist)
                    {
                        String bodystr = DataConveService.converInvoiceBody(bodymap, invoiceBodyMap, invoiceheadid, invoicebodylist, cvmap);
                        invoicesb.append(bodystr);

                    }
                }

               // DataConveService.converfile(invoicemap, filelist, daFileMap ,"", invoiceheadid, cvmap, oldbillid, "0", billInterval);
                Object bwfilelist = invoicemap.get("filelist");
                if (bwfilelist != null && bwfilelist instanceof List)
                {
                    List<Map> ofilelist= (ArrayList<Map>)bwfilelist;
                    for (Map map : ofilelist) {
                        DataHandlerTicketFileUtil.publicextconverfile(map, invoicefilelist, "", invoiceheadid, oldbillid,"0", cvmap, billInterval,invoicesb);
                    }
                }

                DataConveService.convertree(invoicemap, cvmap, treelist,treePublicMap,treeInvoiceMap,"2",invoiceheadid,"", BILL_URL + "da_ticket_ysj/da_ticket_ysj/" +  invoiceheadid  + BILL_URL_NAME + "票据", "0");
            }
        }

       return invoicefilelist;
    }

    private Map dataSave(Map billdetial, List<Map> daheadlist, List<Map> treelist, List<Map> billheadlist, List<Map> billbodylist, List<Map> bankreceiptlist, Map cvmap, String sourceBatchCguid, String dsid, List<Map> filelist, List<Map> bankAndVoucherRellist, boolean isRelated, int sum)
    {
        Map rtnmap = new HashMap<>();

        Object banklist = billdetial.get("bankreceiptlist");
        Object headlist = billdetial.get("headlist");
        String billInterval = "";

        if (headlist != null && headlist instanceof Map)
        {
            Map headMap = (Map)headlist;
            //获取主单据日期
            String billdate = CollectionUtil.getStringFromMap(headMap, "单据日期");
            billInterval = ArrayUtil.getDataInterval(billdate);
        }

        if (banklist != null && banklist instanceof List)
        {
            List<Map> oldbanklist= (ArrayList<Map>)banklist;
            Map headMap = (Map)headlist;
            String cempguidname = CollectionUtil.getStringFromMap(headMap, "报销人");
            String oldbillid = CollectionUtil.getStringFromMap(headMap, "单据id");

            for (Map bankmap : oldbanklist)
            {
                bankmap.put("关联单据id", oldbillid);
                String blankid = Guid.g();

                String bankstr = DataConveService.converBankDetial(bankmap, bankReceiptMap, cvmap, blankid, bankreceiptlist, cempguidname, bankAndVoucherRellist, isRelated);

                DataConveService.convertree(bankmap, cvmap, treelist,treePublicMap,treeBankMap,"3", "", blankid, BILL_URL + "da_bank_ysj/da_bank_ysj/" +  blankid  + BILL_URL_NAME + "银行回单", "0");

                Object filesobj = bankmap.get("filelist");
                if (filesobj != null && filesobj instanceof List)
                {
                    List<Map> oldfilelist= (ArrayList<Map>)filesobj;

                    for (Map filemap : oldfilelist)
                    {
                        DataConveService.converfile(filemap, filelist, daFileMap ,blankid, "", cvmap, oldbillid, "0", billInterval, bankstr);
                    }
                }
            }
        }

        if (headlist != null && headlist instanceof Map)
        {
            //单据主表信息
            Map headMap = (Map)headlist;

            String billheadid = Guid.g();
            rtnmap = DataConveService.converBillHead(headMap, billHeadMap, cvmap, billheadid, billheadlist);
            //单据子表信息
            Object bodylist = headMap.get("bodylist");
            if (bodylist != null && bodylist instanceof  List)
            {
                List<Map> blist= (ArrayList<Map>)bodylist;

                for (Map bodymap : blist)
                {
                    DataConveService.converBillBody(bodymap, billBodyMap,billheadid,billbodylist, cvmap);
                }
            }
            String oldbillid = CollectionUtil.getStringFromMap(headMap, "单据id");
            String sybillid = CollectionUtil.getStringFromMap(billdetial, "上游单据id");
            String ishead = "0";
            if (sybillid.equalsIgnoreCase(oldbillid))
            {
                ishead = "1";
            }
            //处理文件信息
            Object filesobj = billdetial.get("filelist");
            if (filesobj != null && filesobj instanceof List)
            {
                List<Map> oldfilelist= (ArrayList<Map>)filesobj;

                for (Map filemap : oldfilelist)
                {
                    DataConveService.converfile(filemap, filelist, daFileMap ,"", "", cvmap, oldbillid, ishead, billInterval, "");
                }
            }
            //树表
            DataConveService.convertree2(headMap, cvmap, treelist,treePublicMap,treeBillMap,"1", BILL_URL + "da_fk_expensebill_jy/da_fk_expensebill_jy/"  + billheadid  + BILL_URL_NAME + "简易报销单");
            //公共主表
            String isexistfile = "1";
            if (CollectionUtil.isEmpty(filelist))
            {
                isexistfile = "0";
            }
            int ifileqty = 0;
            if (cvmap.get("ifileqty") instanceof Number)
            {
                Object yifileqty = cvmap.get("ifileqty");
                if (yifileqty instanceof Integer)
                {
                    ifileqty = (Integer) yifileqty;
                }else
                {
                    Double aDouble = (Double) cvmap.get("ifileqty");
                    ifileqty = aDouble.intValue();
                }
                
            }
            Map fileistruemap = new HashMap<>();
            if (filelist.size() == ifileqty)
            {
                fileistruemap.put("fileistruename","一致");
                fileistruemap.put("fileistrue","0");

            }
            else
            {
                fileistruemap.put("fileistruename","不一致");
                fileistruemap.put("fileistrue","1");
            }
            DataConveService.converCommDetial(headMap,publicCommonMap,cvmap,daheadlist, sourceBatchCguid, isexistfile, fileistruemap, isRelated, sum);
        }
        return rtnmap;
    }

    @Override
    public EsResultViewVO dataCheck(Map billdetial, Boolean iscover, String dsid, Map batchMap)
    {
        EsResultViewVO esResultViewVO = new EsResultViewVO();

        Map checkDetial = new HashMap<>();
        String cbillcode = "";
        StringBuffer stringBuffer = new StringBuffer();
        try
        {
            //校验单据信息
            DataConveService.valueValiBillDate(voucherRequiredMap, billHeadRequiredMap,billBodyRequiredMap,billHeadLengthMap,billBodyLengthMap,billdetial,stringBuffer);
            //校验银行回单信息
            DataConveService.valueValiBankDate(bankReceiptRequiredMap, bankReceiptLengthMap, billdetial, stringBuffer);
            //校验票据信息
            DataConveService.valueValiInvoiceDate(invoiceHeadRequiredMap, invoiceHeadLengthMap, invoiceBodyRequiredMap, invoiceBodyLengthMap,billdetial, stringBuffer);
            //校验数据是否已经录入
            String cvoucherid = CollectionUtil.getStringFromMap(billdetial, "凭证id");
            String sybillid = CollectionUtil.getStringFromMap(billdetial, "上游单据id");
            Object headlist = billdetial.get("headlist");

            String billid = "";
            if (headlist != null && headlist instanceof Map)
            {
                billid = CollectionUtil.getStringFromMap((Map) headlist, "单据id");
                cbillcode = CollectionUtil.getStringFromMap((Map) headlist, "单据编号");
            }

            if (StringUtils.isBlank(billid))
            {
                stringBuffer.append("获取单据历史id为空");
                esResultViewVO.setCode("0000");
                esResultViewVO.setMsg( stringBuffer.toString());
                return esResultViewVO;
            }

            List<Map> deletetablelist =  daGetBillDetialDao.queryBillCollectionById(cvoucherid, billid, sybillid, dsid, batchMap);
            if (CollectionUtil.isNotEmpty(deletetablelist))
            {
                for (Map deletetable : deletetablelist) {
                    //删除原有数据
                    String cfilesTableName = CollectionUtil.getStringFromMap(deletetable, "cfiles_table_name");
                    List<EsDeleteViewVO> esDeleteViewVOList = daGetBillDetialDao.deleteCollectionDetial(cvoucherid,billid,"da_fk_expensebill","da_fk_expensebill_detail",cfilesTableName, sybillid, dsid, deletetable);
                    esResultViewVO.addEsDeleteViewVOList(esDeleteViewVOList);
                }
            }
        }
        catch (Exception e)
        {
            stringBuffer.append("校验单据信息时发生异常请查询日志TCExpenseBillHandler" + e.getMessage());
            e.printStackTrace();
            logger.error("========================>TCExpenseBillHandler"  + e);

            esResultViewVO.setCode("0000");
            esResultViewVO.setMsg("校验单据信息时发生异常请查询日志TCExpenseBillHandler" + e.getMessage());
            return esResultViewVO;
        }
        if (StringUtils.isBlank(stringBuffer.toString()))
        {
            esResultViewVO.setCode("1000");
            return esResultViewVO;
        }else
        {
            esResultViewVO.setCode("0000");
            esResultViewVO.setMsg("【" + cbillcode + "】" +stringBuffer.toString());
            return esResultViewVO;
        }
    }

    private Map datalog(Map headMap, String extcode, String serialnumber, String msg, Map cvmap, String daheadcguid)
    {
        return DataConveService.converlog(headMap,logMap,cvmap, extcode, msg, serialnumber, daheadcguid);
    }

    @Override
    public Map getLogDetial(List<Map> billDetialist, Map sourceBatch)
    {
        return getVoucherMap(billDetialist, billPublicMap,sourceBatch);
    }

    private Map getVoucherMap(List<Map> billDetialist, HashMap<String, String> billPublicMap, Map sourceBatch)
    {
        //因为同一个主单据下的相关数据，故凭证信息一定相同
        Map billdetial = billDetialist.get(0);
        return DataConveService.getDetialMap(billdetial,billPublicMap, sourceBatch);
    }
}
