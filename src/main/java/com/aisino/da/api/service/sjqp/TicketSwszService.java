package com.aisino.da.api.service.sjqp;

import com.aisino.aosplus.core.ioc.annotation.Impl;
import com.aisino.da.api.service.sjqp.impl.TicketSwszServiceImpl;

import java.util.List;
import java.util.Map;

@Impl(TicketSwszServiceImpl.class)
public interface TicketSwszService {
    /**
     * 查询当前组织是否存在调用税局的配置
     * @return
     */
    Map getFileConfigMap();

    /**
     * 根据页面票据id查询票据信息
     * @param ticketidlist
     * @return
     */
    List<Map> getTicketlist(List<String> ticketidlist);

    List<Map> queryDownloadConfig(List<String> servicenamelist);

    void deleteTicketFile(String ticketid, String fileTableName);

    void addDowloadlog(Map insertmap);

    void insertFilelist(List<Map> filelist, String tableName);

    void updatelog(String cguid);
}
