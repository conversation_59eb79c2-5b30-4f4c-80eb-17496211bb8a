package com.aisino.da.api.service.fkbill.impl;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.entity.EsAddViewVO;
import com.aisino.da.api.entity.EsDeleteViewVO;
import com.aisino.da.api.entity.EsResultViewVO;
import com.aisino.da.api.service.fkbill.DataConveService;
import com.aisino.da.api.service.fkbill.SaveAndCheckDataDetialService;
import com.aisino.da.api.util.ArrayUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CApplybillBillHandler implements SaveAndCheckDataDetialService
{

    @Inject
    private DaGetBillDetialDao daGetBillDetialDao;

    //单据主表
    static HashMap<String, String> billHeadMap = new HashMap<>();
    //单据子表
    static HashMap<String, String> billBodyMap = new HashMap<>();
    //发票主表
    static HashMap<String, String> invoiceHeadMap = new HashMap<>();
    //发票子表
    static HashMap<String, String> invoiceBodyMap = new HashMap<>();
    //银行回单表
    static HashMap<String, String> bankReceiptMap = new HashMap<>();
    //凭证必填
    static HashMap<String, String> voucherRequiredMap = new HashMap<>();
    //单据主表必填
    static HashMap<String, String> billHeadRequiredMap = new HashMap<>();
    //单据子表必填
    static HashMap<String, String> billBodyRequiredMap = new HashMap<>();
    //发票主表必填
    static HashMap<String, String> invoiceHeadRequiredMap = new HashMap<>();
    //发票子表必填
    static HashMap<String, String> invoiceBodyRequiredMap = new HashMap<>();
    //银行回单表必填
    static HashMap<String, String> bankReceiptRequiredMap = new HashMap<>();

    //单据主表必填长度
    static HashMap<String, String> billHeadLengthMap = new HashMap<>();
    //单据子表必填长度
    static HashMap<String, String> billBodyLengthMap = new HashMap<>();
    //发票主表必填长度
    static HashMap<String, String> invoiceHeadLengthMap = new HashMap<>();
    //发票子表必填长度
    static HashMap<String, String> invoiceBodyLengthMap = new HashMap<>();
    //银行回单表必填长度
    static HashMap<String, String> bankReceiptLengthMap = new HashMap<>();
    //树公共字段
    static HashMap<String, String> treePublicMap = new HashMap<>();
    //树单据字段
    static HashMap<String, String> treeBillMap = new HashMap<>();
    //银行树
    static HashMap<String, String> treeBankMap = new HashMap<>();
    //发票树
    static HashMap<String, String> treeInvoiceMap = new HashMap<>();
    //整体公共字段
    static HashMap<String, String> billPublicMap = new HashMap<>();
    //文件表
    static HashMap<String, String> daFileMap = new HashMap<>();
    //公共字段
    static HashMap<String, String> publicCommonMap = new HashMap<>();
    //日志字段
    static HashMap<String, String> logMap = new HashMap<>();

    static
    {
        billHeadMap.put("单据id","oldcguid");
        billHeadMap.put("页面id","oldpageid");
        billHeadMap.put("制单人","ccreatorname");
        billHeadMap.put("审核人","cauditusername");
        billHeadMap.put("申请人","cempguid_name");
        billHeadMap.put("单据模板名称","cbillformwork_name");
        billHeadMap.put("单据编号","cbillcode");
        billHeadMap.put("单据日期","ddate");
        billHeadMap.put("事由","creason_name");
        billHeadMap.put("支付人","cpayerguid_name");
        billHeadMap.put("申请金额","itotalamt");
        billHeadMap.put("费用类型","cexpenseclassname");
        billHeadMap.put("项目","cprojectname");
        billHeadMap.put("费用承担部门","cexpensedeptguid_name");

        billHeadMap.put("申请部门","cdeptguid_name");
        billHeadMap.put("供应商","csupplierguid_name");
        billHeadMap.put("客户","ccustguid_name");
        billHeadMap.put("申请人公司名称","ccompanyguid_name");
        billHeadMap.put("申请人公司id","ccompanyguid");
        billHeadMap.put("支付公司","cpaycompanyguid_name");
        billHeadMap.put("生效人","ceffectempguid_name");

        billHeadMap.put("制单人部门","ccreatordeptname");
        billHeadMap.put("制单人部门id","ccreatordeptid");
        billHeadMap.put("制单人id","ccreatornameid");
        billHeadMap.put("上游单据页面id","sybillpageid");

        billHeadMap.put("影像数","iimgqty");
        billHeadMap.put("附件个数","iappqty");

        billBodyMap.put("主表id","oldheadid");
        billBodyMap.put("子表id","oldcguid");
        billBodyMap.put("费用明细","cexpenseclass_name");
        billBodyMap.put("费用类型","cexpenseclass");
        billBodyMap.put("项目","cproject");
        billBodyMap.put("费用承担部门","caccdept");
        billBodyMap.put("申请金额","itotalamt");

        billHeadRequiredMap.put("申请人公司id","申请人公司id");
        billHeadRequiredMap.put("申请人公司名称","申请人公司名称");
        billHeadRequiredMap.put("单据id","单据id");
        billHeadRequiredMap.put("单据模板名称","单据模板名称");
        billHeadRequiredMap.put("申请金额","申请金额");

        voucherRequiredMap.put("凭证id","凭证id");
        voucherRequiredMap.put("凭证日期","凭证日期");
        voucherRequiredMap.put("凭证编号","凭证编号");

        billBodyRequiredMap.put("主表id","主表id");
        billBodyRequiredMap.put("子表id","子表id");

        billHeadLengthMap.put("id","30");
        billHeadLengthMap.put("其它","500");
        billHeadLengthMap.put("事由","5000");

        billBodyLengthMap.put("id","30");
        billBodyLengthMap.put("其它","500");
        billBodyLengthMap.put("事由","5000");

        billPublicMap.put("凭证id","cvoucherid");
        billPublicMap.put("凭证日期","cvoucherdate");
        billPublicMap.put("凭证编号","cvoucherno");
        billPublicMap.put("外系统凭证号","coutvounumber");
        billPublicMap.put("上游单据日期","sybilldate");
        billPublicMap.put("上游单据id","sybillid");
        billPublicMap.put("分表字段","cfiles_table_name");
        billPublicMap.put("文件数","ifileqty");
        billPublicMap.put("上游单据编号","sybillcode");
        billPublicMap.put("会计年","iyear");
        billPublicMap.put("会计月","imonth");
        billPublicMap.put("会计年月","iyearandmonth");

        daFileMap.put("文件对象存储文件名","filename");
        daFileMap.put("文件类型","cfiletype");
        daFileMap.put("文件哈希码","mdnumber");
        daFileMap.put("文件哈希类型","chxtype");
        daFileMap.put("单据id","cbillid");
        daFileMap.put("文件实际名称","cfilerealname");
        daFileMap.put("银行回单id","oldbankid");
        daFileMap.put("文件来源类型","cfilesourcetype");
        daFileMap.put("文件访问地址","fileurl");

        daFileMap.put("桶名","dxccname");
        daFileMap.put("文件收集方式","filecollmethod");
        daFileMap.put("存储方式","storagemethod");
        daFileMap.put("路径","filepath");

        daFileMap.put("载体类型","carriertype");
        daFileMap.put("排序字段","csortindex");

        publicCommonMap.put("单据id","billid");
        publicCommonMap.put("单据模板名称","cbillformwork_name");
        publicCommonMap.put("单据编号","cbillcode");
        publicCommonMap.put("单据日期","ddate");
        publicCommonMap.put("事由","creason_name");
        publicCommonMap.put("影像数","iimgqty");
        publicCommonMap.put("附件数","iappqty");
        publicCommonMap.put("文件数","ifileqty");
        publicCommonMap.put("制单人","ccreatorname");
        publicCommonMap.put("申请人id","cempguid");
        publicCommonMap.put("申请人","cempguid_name");
        publicCommonMap.put("页面id","oldpageid");
        publicCommonMap.put("申请部门","cdeptguid_name");
        publicCommonMap.put("申请金额","iamt");

        treePublicMap.put("cfiletablename","cfiles_table_name");
        treePublicMap.put("cparent","cvoucherid");
        treePublicMap.put("cvoucherid","cvoucherid");
        treePublicMap.put("sybillid","sybillid");

        treeBillMap.put("cmeansid","单据id");
        treeBillMap.put("cmeanscode","单据编号");
        treeBillMap.put("cbillpageid","页面id");

        logMap.put("制单人","ccreatorname");
        logMap.put("申请金额","iamt");
        logMap.put("事由","creason_name");
        logMap.put("单据日期","ddate");
        logMap.put("单据id","billid");
       // logMap.put("制单人部门id","cdeptguid");
        logMap.put("单据编号","cbillcode");
        logMap.put("申请人id","cempguid");
        logMap.put("申请人","cempguid_name");
        logMap.put("申请部门","cdeptguid_name");

        logMap.put("单据模板名称","cbillformwork_name");
    }

    @Override
    public EsAddViewVO CheckAndSaveBillDetial(List<Map> details, Map logmap, String dsid, boolean isRelated, boolean related)
    {
        EsAddViewVO esAddViewVO = new EsAddViewVO();

        List<Map> daheadlist      = new ArrayList<>(); //默认表存储公共字段
        List<Map> treelist = new ArrayList<>(); //树 //默认表存储公共字段
        List<Map> billheadlist    = new ArrayList<>(); //单据主表
        List<Map> billbodylist    = new ArrayList<>(); //单据子表
        List<Map> billAndVoucherRellist = new ArrayList<>(); //单据凭证关系

        String sourceBatchCguid = CollectionUtil.getStringFromMap(logmap, "cguid");
        boolean iscover = CollectionUtil.getBoolean(logmap, "iscover", false);

        Map<String, Integer> rtnmap = new HashMap<>();
        Integer successnum  = 0;
        Integer failnum = 0;
        Integer ncoverednum = 0;

        Map<String, List> allfilemap = new HashMap<>();
        for (Map billdetial: details)
        {
            List<Map> filelist = new ArrayList<>(); //文件

            Map cvmap = DataConveService.getDetialMap(billdetial,billPublicMap, logmap);
            cvmap.putAll(logmap);
            cvmap.remove("cguid");
            cvmap.put("csourcesysname", CollectionUtil.getStringFromMap(logmap, "csourcesysname"));
            //存储数据
            Map map = dataSave(billdetial,daheadlist,treelist,billheadlist,billbodylist,cvmap,sourceBatchCguid,dsid,filelist, isRelated);
            DataConveService.databillAndVoucher(cvmap,billAndVoucherRellist, billdetial);
            if (CollectionUtil.isNotEmpty(filelist))
            {
                String daFileTableName = daGetBillDetialDao.getDaFileTableName(CollectionUtil.getStringFromMap(cvmap,"cfiles_table_name"));
                //构建文件相关校验
                Map sxcheckmap = DataConveService.getSXMap(map, logmap ,"BILL", filelist,dsid);
                //调用四性检测;
                String check = DataConveService.SXCheck(sxcheckmap);
                if (StringUtils.isNotEmpty(check))
                {
                    esAddViewVO.setMsg(check);
                    return esAddViewVO;
                }

                if (allfilemap.containsKey(daFileTableName))
                {
                    List list = allfilemap.get(daFileTableName);
                    list.addAll(filelist);
                    allfilemap.put(daFileTableName, list);
                }else {
                    //
                    allfilemap.put(daFileTableName, filelist);
                }
            }
        }

        if (!CollectionUtil.isBlankMap(allfilemap))
        {
            for (String tablename : allfilemap.keySet())
            {
                List list = allfilemap.get(tablename);
                esAddViewVO.add(list);
                daGetBillDetialDao.inserDetialByTableNameByCZY(tablename, list, dsid);
            }
        }

        if (CollectionUtil.isNotEmpty(billheadlist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fk_applybill", billheadlist,dsid);
        }

        if (CollectionUtil.isNotEmpty(billbodylist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fk_applybill_detail", billbodylist,dsid);
        }

        if (CollectionUtil.isNotEmpty(treelist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fview_vouandbillrela", treelist,dsid);
        }
        if (CollectionUtil.isNotEmpty(daheadlist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_api_fk_file_collection", daheadlist,dsid);
        }

        if (CollectionUtil.isNotEmpty(billAndVoucherRellist))
        {
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_billAndVoucherRel", billAndVoucherRellist,dsid);
        }

        if (CollectionUtil.isNotEmpty(daheadlist))
        {
            for (Map map : daheadlist) {
                String cvoucherid = CollectionUtil.getStringFromMap(map, "cvoucherid");
                DataConveService.updateSUAIStatusMs(cvoucherid);
                DataConveService.updateIntegrityStatusUpdateMs(map);

            }
        }
        return esAddViewVO;
    }

    private Map dataSave(Map billdetial, List<Map> daheadlist, List<Map> treelist, List<Map> billheadlist, List<Map> billbodylist, Map cvmap, String sourceBatchCguid, String dsid, List<Map> filelist, boolean isRelated)
    {
        Map rtnmap = new HashMap<>();

        Object headlist = billdetial.get("headlist");
        if (headlist != null && headlist instanceof Map)
        {
            //单据主表信息
            Map headMap = (Map)headlist;

            String billheadid = Guid.g();
            rtnmap = DataConveService.converBillHead(headMap, billHeadMap, cvmap,billheadid,billheadlist);
            //单据子表信息
            Object bodylist = headMap.get("bodylist");
            if (bodylist != null && bodylist instanceof  List)
            {
                List<Map> blist= (ArrayList<Map>)bodylist;

                for (Map bodymap : blist)
                {
                    DataConveService.converBillBody(bodymap, billBodyMap,billheadid,billbodylist, cvmap);
                }
            }
            String oldbillid = CollectionUtil.getStringFromMap(headMap, "单据id");
            String sybillid = CollectionUtil.getStringFromMap(billdetial, "上游单据id");
            String ishead = "0";
            if (sybillid.equalsIgnoreCase(oldbillid))
            {
                ishead = "1";
            }
            //处理文件信息
            Object filesobj = billdetial.get("filelist");
            if (filesobj != null && filesobj instanceof List)
            {
                List<Map> oldfilelist= (ArrayList<Map>)filesobj;
                //获取主单据日期
                String billdate = CollectionUtil.getStringFromMap(headMap, "单据日期");
                String billInterval = ArrayUtil.getDataInterval(billdate);

                for (Map filemap : oldfilelist)
                {
                    DataConveService.converfile(filemap, filelist, daFileMap ,"", "", cvmap, oldbillid, ishead, billInterval, "");
                }
            }
            //树表
            DataConveService.convertree2(headMap, cvmap, treelist,treePublicMap,treeBillMap,"1", BILL_URL + "da_fk_applybill_y/da_fk_applybill_y/" + billheadid  + BILL_URL_NAME + "差旅申请单");
            //公共主表
            String isexistfile = "1";
            if (CollectionUtil.isEmpty(filelist))
            {
                isexistfile = "0";
            }
            int ifileqty = 0;
            if (cvmap.get("ifileqty") instanceof Number)
            {
                Object yifileqty = cvmap.get("ifileqty");
                if (yifileqty instanceof Integer)
                {
                    ifileqty = (Integer) yifileqty;
                }else
                {
                    Double aDouble = (Double) cvmap.get("ifileqty");
                    ifileqty = aDouble.intValue();
                }
                
            }
            Map fileistruemap = new HashMap<>();
            if (filelist.size() == ifileqty)
            {
                fileistruemap.put("fileistruename","一致");
                fileistruemap.put("fileistrue","0");

            }
            else
            {
                fileistruemap.put("fileistruename","不一致");
                fileistruemap.put("fileistrue","1");
            }
            DataConveService.converCommDetial(headMap,publicCommonMap,cvmap,daheadlist, sourceBatchCguid, isexistfile, fileistruemap, isRelated, 0);
        }
        return rtnmap;
    }

    @Override
    public EsResultViewVO dataCheck(Map billdetial, Boolean iscover, String dsid, Map batchMap)
    {
        EsResultViewVO esResultViewVO = new EsResultViewVO();

        Map checkDetial = new HashMap<>();
        String cbillcode = "";
        StringBuffer stringBuffer = new StringBuffer();
        try
        {
            DataConveService.valueValiBillDate(voucherRequiredMap, billHeadRequiredMap,billBodyRequiredMap,billHeadLengthMap,billBodyLengthMap,billdetial,stringBuffer);
            //校验数据是否已经录入
            String cvoucherid = CollectionUtil.getStringFromMap(billdetial, "凭证id");
            String sybillid = CollectionUtil.getStringFromMap(billdetial, "上游单据id");
            Object headlist = billdetial.get("headlist");

            String billid = "";
            if (headlist != null && headlist instanceof Map)
            {
                billid = CollectionUtil.getStringFromMap((Map) headlist, "单据id");
                cbillcode = CollectionUtil.getStringFromMap((Map) headlist, "单据编号");
            }

            if (StringUtils.isBlank(billid))
            {
                stringBuffer.append("获取单据历史id为空");
                esResultViewVO.setCode("0000");
                esResultViewVO.setMsg( stringBuffer.toString());
                return esResultViewVO;
            }

            List<Map> deletetablelist = daGetBillDetialDao.queryBillCollectionById(cvoucherid, billid, sybillid, dsid, batchMap);
            if (CollectionUtil.isNotEmpty(deletetablelist))
            {
                for (Map deletetable : deletetablelist) {
                    //删除原有数据
                    String cfilesTableName = CollectionUtil.getStringFromMap(deletetable, "cfiles_table_name");
                    List<EsDeleteViewVO> esDeleteViewVOList = daGetBillDetialDao.deleteCollectionDetial(cvoucherid,billid,"da_fk_applybill","da_fk_applybill_detail",cfilesTableName, sybillid, dsid, deletetable);
                    esResultViewVO.addEsDeleteViewVOList(esDeleteViewVOList);
                }
            }
        }
        catch (Exception e)
        {
            stringBuffer.append("校验单据信息时发生异常请查询日志CApplybillBillHandler" + e.getMessage());
            e.printStackTrace();
            logger.error("========================>CApplybillBillHandler"  + e);
            esResultViewVO.setCode("0000");
            esResultViewVO.setMsg("校验单据信息时发生异常请查询日志CApplybillBillHandler" + e.getMessage());
            return esResultViewVO;
        }
        if (StringUtils.isBlank(stringBuffer.toString()))
        {
            esResultViewVO.setCode("1000");
            return esResultViewVO;
        }else
        {
            esResultViewVO.setCode("0000");
            esResultViewVO.setMsg("【" + cbillcode + "】" +stringBuffer.toString());
            return esResultViewVO;
        }
    }

    private Map datalog(Map headMap, String extcode, String serialnumber, String msg, Map cvmap, String daheadcguid)
    {
        return DataConveService.converlog(headMap,logMap,cvmap, extcode, msg, serialnumber, daheadcguid);
    }

    @Override
    public Map getLogDetial(List<Map> billDetialist, Map sourceBatch)
    {
        return getVoucherMap(billDetialist, billPublicMap, sourceBatch);
    }

    private Map getVoucherMap(List<Map> billDetialist, HashMap<String, String> billPublicMap, Map sourceBatch)
    {
        //因为同一个主单据下的相关数据，故凭证信息一定相同
        Map billdetial = billDetialist.get(0);
        return DataConveService.getDetialMap(billdetial,billPublicMap, sourceBatch);
    }
}
