package com.aisino.da.api.service.fkbill;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.service.fkbill.impl.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BillVoucherCollectDataService
{
    @Inject
    private DaGetBillDetialDao daGetBillDetialDao;

    private static Map<String, SaveAndCheckDataDetialService> map = new HashMap(15);
    @Inject
    private CExpenseBillHandler cExpenseBillHandler;//差旅报销
    @Inject
    private TCExpenseBillHandler tExpenseBillHandler;//通用报销

    @Inject
    private JYExpenseBillHandler jyExpenseBillHandler;//简易报销单

    @Inject
    private RepayBillHandler repayBillHandler;//还款单
    @Inject
    private CreditBilldHandler creditBillHandler;//挂账付款单
    @Inject
    private PaybillBillHandler paybillBillHandler;//支付单
    @Inject
    private TApplybillBillHandler tApplybillBillHandler;//通用申请单
    @Inject
    private CApplybillBillHandler cApplybillBillHandler;//差旅申请单
    @Inject
    private AdvancepaybillBillHandler advancepaybillBillHandler;//预付款单
    @Inject
    private LoanbillBillHandler loanbillBillHandler;//借款单
    @Inject
    private QCLoanbillBillHandler qcLoanbillBillHandler;//期初借款单
    @Inject
    private QCAdvancepaybillBillHandler qcAdvancepaybillBillHandler;//期初预付款单
    @Inject
    private QCCreditBillHandler qcCreditBillHandler;//期初挂账单
    @Inject
    private SalaryPayBillHandler salaryPayBillHandler;//薪资发放单
    @Inject
    private StatementsBillHandler statementsBillHandler;//结算单
    public Map<String, Map> billtype = new HashMap();


    public SaveAndCheckDataDetialService getAdapt(String type) {
        this.init();
        return map.get(type);
    }

    private void init() {
        if (map.size() == 0) {
            synchronized(BillVoucherCollectDataService.class)
            {
                map.put("fk_travelexpense_bill",cExpenseBillHandler);
                map.put("fk_commonexpense_bill",tExpenseBillHandler);
                map.put("fk_repay_bill",repayBillHandler);

                map.put("fk_credit_bill",creditBillHandler);
                map.put("fk_paybill",paybillBillHandler);
                map.put("fk_applybill_bill",tApplybillBillHandler);

                map.put("fk_travelapply_bill",cApplybillBillHandler);
                map.put("fk_advancepaybill_bill",advancepaybillBillHandler);
                map.put("fk_loanbill_bill",loanbillBillHandler);

                map.put("fk_initloanbill_bill",qcLoanbillBillHandler);
                map.put("fk_initadvancepaybill_bill",qcAdvancepaybillBillHandler);
                map.put("fk_initcreditbill",qcCreditBillHandler);
                map.put("fk_salarypaybill",salaryPayBillHandler);
                map.put("tt_statementsbill",statementsBillHandler);

                map.put("fk_simple_expensebill",jyExpenseBillHandler);

                List<Map> pageByMap = daGetBillDetialDao.queryBillType();
                if (!CollectionUtil.isEmpty(pageByMap))
                {
                    for (Map billdetial : pageByMap)
                    {
                        Map<String, Map> objectObjectHashMap = new HashMap<>();
                        String pageid = CollectionUtil.getStringFromMap(billdetial, "pageid");
                        billtype.put(pageid, billdetial);
                    }
                }
            }
        }
    }
}
