package com.aisino.da.api.service.publicother.impl;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.handler.enums.DaDataResult;
import com.aisino.da.api.service.fkbill.DataConveService;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.api.util.ExtFileUploadUtil;
import com.aisino.da.api.util.ext.ESUtil;
import com.aisino.da.api.util.ext.EsConvertUtil;
import com.aisino.da.core.util.DaConfigHelper;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class TicketConveService
{
    @Inject
    private static DaGetBillDetialDao daGetBillDetialDao;
    static HashMap<String, String> ticketReceiptMap = new HashMap<>();

    //发票主表
    static HashMap<String, String> invoiceHeadMap = new HashMap<>();
    //发票子表
    static HashMap<String, String> invoiceBodyMap = new HashMap<>();

    public static final List<String> CCLASSES = Lists.newArrayList("客运汽车票", "机票行程单", "火车票", "出租车票");
    private static final boolean TICKETSTATE = DaConfigHelper.getBoolean("ticket_state", false);

    private static final boolean TICKETNEGATIVEIAMT = DaConfigHelper.getBoolean("ticket_negative_iamt", false);

    private static final boolean TICKETCRELSTATUS = DaConfigHelper.getBoolean("ticketcr_elstatus", false);


    //文件表
    static HashMap<String, String> daFileMap = new HashMap<>();

    static HashMap<String, String> treePublicMap = new HashMap<>();

    static HashMap<String, String> treeInvoiceMap = new HashMap<>();

    static String billUrl = "page/commonvchr/";
    static String billUrlName = "?origin=1&singleTab=1&cstate=view&name=";

    private static final Log log = LogFactory.getLog(TicketConveService.class);

    static
    {
        ticketReceiptMap.put("凭证id","cvoucherid");
        ticketReceiptMap.put("凭证编号","cvoucherno");
        ticketReceiptMap.put("凭证日期","cvoucherdate");

        ticketReceiptMap.put("发票号码","cnumber");
        ticketReceiptMap.put("发票类型","ctype");
        ticketReceiptMap.put("价税合计","itotal");
/*        ticketReceiptMap.put("总金额","iamt");
*/
        invoiceHeadMap.put("凭证id", "cvoucherid");
        invoiceHeadMap.put("凭证日期", "cvoucherdate");
        invoiceHeadMap.put("凭证编号", "cvoucherno");
        invoiceHeadMap.put("外系统凭证号", "coutvounumber");
        invoiceHeadMap.put("主单据日期", "sybilldate");
        invoiceHeadMap.put("主单据id", "sybillid");
        invoiceHeadMap.put("分表字段", "cfiles_table_name");
        invoiceHeadMap.put("文件总数", "ifileqty");
        invoiceHeadMap.put("主单据编号", "sybillcode");
        invoiceHeadMap.put("影像表id", "oldcguid");
        invoiceHeadMap.put("单据id", "cbillguid");
        invoiceHeadMap.put("单据编号", "cbillcode");
        invoiceHeadMap.put("单据子表id", "cbilllineguid");
        invoiceHeadMap.put("发票大类", "cclass");
        invoiceHeadMap.put("发票类型", "ctype");
        invoiceHeadMap.put("发票大类编码", "cclassid");
        invoiceHeadMap.put("发票类型编码", "ctypeid");
        //新增档案字段
        invoiceHeadMap.put("发票类型id", "ticketid");

        invoiceHeadMap.put("发票号码", "cnumber");
        invoiceHeadMap.put("发票代码", "ccode");
        invoiceHeadMap.put("开票日期", "ddate");
        invoiceHeadMap.put("总金额", "iamt");
        invoiceHeadMap.put("价税合计", "itotal");
        invoiceHeadMap.put("乘车人", "passenger");
        invoiceHeadMap.put("乘车人编码", "passengercode");

        invoiceHeadMap.put("销售方名称", "csellername");
        invoiceHeadMap.put("销售方纳税人识别号", "csellertaxid");
        invoiceHeadMap.put("销售方地址电话", "cseller_addr_tel");
        invoiceHeadMap.put("销售方开户行及账户", "cseller_bank_account");
        invoiceHeadMap.put("购买方名称", "cbuyername");
        invoiceHeadMap.put("购买方纳税人识别号", "cbuyertaxid");
        invoiceHeadMap.put("购买方地址电话", "cbuyer_addr_tel");
        invoiceHeadMap.put("购买方开户行及账户", "cbuyer_bank_account");
        invoiceHeadMap.put("省份", "province");
        invoiceHeadMap.put("机器码", "machinecode");
        invoiceHeadMap.put("收款人", "iclerk");
        invoiceHeadMap.put("收款人编码", "iclerkcode");

        invoiceHeadMap.put("复核人", "review");
        invoiceHeadMap.put("复核人编码", "reviewcode");

        invoiceHeadMap.put("备注", "cremark");
        invoiceHeadMap.put("查验状态", "ccheckresult");
        invoiceHeadMap.put("查验时间", "dcheckdate");
        invoiceHeadMap.put("核对结果", "checkresult");
        invoiceHeadMap.put("核对方式", "checkmode");
        invoiceHeadMap.put("来源", "cfrom");
        invoiceHeadMap.put("是否OCR识别", "isocr");
        invoiceHeadMap.put("会计年", "iyear");
        invoiceHeadMap.put("会计月", "imonth");
        invoiceHeadMap.put("会计年月", "iyearandmonth");
        invoiceHeadMap.put("记账日期", "ext_postingdate");

        invoiceHeadMap.put("标准扩展字段1","column1");
        invoiceHeadMap.put("标准扩展字段2","column2");
        invoiceHeadMap.put("标准扩展字段3","column3");
        invoiceHeadMap.put("标准扩展字段4","column4");
        invoiceHeadMap.put("标准扩展字段5","column5");
        invoiceHeadMap.put("标准扩展字段6","column6");
        invoiceHeadMap.put("标准扩展字段7","column7");
        invoiceHeadMap.put("标准扩展字段8","column8");
        invoiceHeadMap.put("标准扩展字段9","column9");
        invoiceHeadMap.put("标准扩展字段10","column10");
        invoiceHeadMap.put("标准扩展字段11","column11");
        invoiceHeadMap.put("标准扩展字段12","column12");
        invoiceHeadMap.put("标准扩展字段13","column13");
        invoiceHeadMap.put("标准扩展字段14","column14");
        invoiceHeadMap.put("标准扩展字段15","column15");
        invoiceHeadMap.put("标准扩展字段16","column16");
        invoiceHeadMap.put("标准扩展字段17","column17");
        invoiceHeadMap.put("标准扩展字段18","column18");
        invoiceHeadMap.put("标准扩展字段19","column19");
        invoiceHeadMap.put("标准扩展字段20","column20");
        invoiceHeadMap.put("标准扩展字段21","column21");
        invoiceHeadMap.put("标准扩展字段22","column22");
        invoiceHeadMap.put("标准扩展字段23","column23");
        invoiceHeadMap.put("标准扩展字段24","column24");
        invoiceHeadMap.put("标准扩展字段25","column25");
        invoiceHeadMap.put("标准扩展字段26","column26");
        invoiceHeadMap.put("标准扩展字段27","column27");
        invoiceHeadMap.put("标准扩展字段28","column28");
        invoiceHeadMap.put("标准扩展字段29","column29");
        invoiceHeadMap.put("标准扩展字段30","column30");
        invoiceHeadMap.put("标准扩展字段31","column31");
        invoiceHeadMap.put("标准扩展字段32","column32");
        invoiceHeadMap.put("标准扩展字段33","column33");
        invoiceHeadMap.put("标准扩展字段34","column34");
        invoiceHeadMap.put("标准扩展字段35","column35");
        invoiceHeadMap.put("标准扩展字段36","column36");
        invoiceHeadMap.put("标准扩展字段37","column37");
        invoiceHeadMap.put("标准扩展字段38","column38");
        invoiceHeadMap.put("标准扩展字段39","column39");
        invoiceHeadMap.put("标准扩展字段40","column40");
        invoiceHeadMap.put("标准扩展字段41","column41");
        invoiceHeadMap.put("标准扩展字段42","column42");
        invoiceHeadMap.put("标准扩展字段43","column43");
        invoiceHeadMap.put("标准扩展字段44","column44");
        invoiceHeadMap.put("标准扩展字段45","column45");
        invoiceHeadMap.put("标准扩展字段46","column46");
        invoiceHeadMap.put("标准扩展字段47","column47");
        invoiceHeadMap.put("标准扩展字段48","column48");
        invoiceHeadMap.put("标准扩展字段49","column49");
        invoiceHeadMap.put("标准扩展字段50","column50");

        invoiceBodyMap.put("开票明细","goodsname");
        invoiceBodyMap.put("规格型号","specifications");
        invoiceBodyMap.put("单位","measureunits");
        invoiceBodyMap.put("数量","numbers");
        invoiceBodyMap.put("单价","notaxprice");
        invoiceBodyMap.put("不含税金额","goodsamount");
        invoiceBodyMap.put("税率","taxrate");
        invoiceBodyMap.put("税额","goodstaxamount");
        invoiceBodyMap.put("标准扩展字段1","column1");
        invoiceBodyMap.put("标准扩展字段2","column2");
        invoiceBodyMap.put("标准扩展字段3","column3");
        invoiceBodyMap.put("标准扩展字段4","column4");
        invoiceBodyMap.put("标准扩展字段5","column5");
        invoiceBodyMap.put("标准扩展字段6","column6");
        invoiceBodyMap.put("标准扩展字段7","column7");
        invoiceBodyMap.put("标准扩展字段8","column8");
        invoiceBodyMap.put("标准扩展字段9","column9");
        invoiceBodyMap.put("标准扩展字段10","column10");
        invoiceBodyMap.put("标准扩展字段11","column11");
        invoiceBodyMap.put("标准扩展字段12","column12");
        invoiceBodyMap.put("标准扩展字段13","column13");
        invoiceBodyMap.put("标准扩展字段14","column14");
        invoiceBodyMap.put("标准扩展字段15","column15");
        invoiceBodyMap.put("标准扩展字段16","column16");
        invoiceBodyMap.put("标准扩展字段17","column17");
        invoiceBodyMap.put("标准扩展字段18","column18");
        invoiceBodyMap.put("标准扩展字段19","column19");
        invoiceBodyMap.put("标准扩展字段20","column20");
        invoiceBodyMap.put("标准扩展字段21","column21");
        invoiceBodyMap.put("标准扩展字段22","column22");
        invoiceBodyMap.put("标准扩展字段23","column23");
        invoiceBodyMap.put("标准扩展字段24","column24");
        invoiceBodyMap.put("标准扩展字段25","column25");
        invoiceBodyMap.put("标准扩展字段26","column26");
        invoiceBodyMap.put("标准扩展字段27","column27");
        invoiceBodyMap.put("标准扩展字段28","column28");
        invoiceBodyMap.put("标准扩展字段29","column29");
        invoiceBodyMap.put("标准扩展字段30","column30");
        invoiceBodyMap.put("标准扩展字段31","column31");
        invoiceBodyMap.put("标准扩展字段32","column32");
        invoiceBodyMap.put("标准扩展字段33","column33");
        invoiceBodyMap.put("标准扩展字段34","column34");
        invoiceBodyMap.put("标准扩展字段35","column35");
        invoiceBodyMap.put("标准扩展字段36","column36");
        invoiceBodyMap.put("标准扩展字段37","column37");
        invoiceBodyMap.put("标准扩展字段38","column38");
        invoiceBodyMap.put("标准扩展字段39","column39");
        invoiceBodyMap.put("标准扩展字段40","column40");
        invoiceBodyMap.put("标准扩展字段41","column41");
        invoiceBodyMap.put("标准扩展字段42","column42");
        invoiceBodyMap.put("标准扩展字段43","column43");
        invoiceBodyMap.put("标准扩展字段44","column44");
        invoiceBodyMap.put("标准扩展字段45","column45");
        invoiceBodyMap.put("标准扩展字段46","column46");
        invoiceBodyMap.put("标准扩展字段47","column47");
        invoiceBodyMap.put("标准扩展字段48","column48");
        invoiceBodyMap.put("标准扩展字段49","column49");
        invoiceBodyMap.put("标准扩展字段50","column50");

        invoiceHeadMap.put("发票接口名称","ext_ticketsourcename");
        invoiceHeadMap.put("进销项标识","ext_tickettype");

        daFileMap.put("存储文件名","filename");
        daFileMap.put("文件类型","cfiletype");
        daFileMap.put("文件哈希码","mdnumber");
        daFileMap.put("文件哈希类型","chxtype");
        daFileMap.put("单据id","cbillid");
        daFileMap.put("文件实际名称","cfilerealname");
        daFileMap.put("银行回单id","oldbankid");
        daFileMap.put("文件来源类型","cfilesourcetype");
        daFileMap.put("文件访问地址","fileurl");
        daFileMap.put("桶名","dxccname");
        daFileMap.put("文件收集方式","filecollmethod");
        daFileMap.put("存储方式","storagemethod");
        daFileMap.put("路径","filepath");
        daFileMap.put("载体类型","carriertype");
        daFileMap.put("排序字段","csortindex");

        treePublicMap.put("cfiletablename","cfiles_table_name");
        treePublicMap.put("cparent","凭证id");
        treePublicMap.put("cvoucherid","凭证id");
        treePublicMap.put("sybillid","主单据id");

        treeInvoiceMap.put("cmeanscode","发票号码");
        treeInvoiceMap.put("cmeansname","票据");
        treeInvoiceMap.put("cparent","单据id");
    }

    public static String CheckDataByTicket(Map ticketmap, Map sourceBatch, String dsid, boolean iscover)
    {
        Map logMap = new HashMap<>();
        StringBuffer sb = new StringBuffer();

        String cnumber = CollectionUtil.getStringFromMap(ticketmap, "发票号码");
        if (StringUtils.isBlank(cnumber)) {
            sb.append("票据号码为空数据不能入库");
        }


        //判断金额是否为负数
        String iamtstr = CollectionUtil.getStringFromMap(ticketmap, "总金额");
        //判断总金额是否为-数 >= 0为true  < 0为false
        boolean greaterThanZero = isGreaterThanZero(iamtstr);

        String cvoucherid = CollectionUtil.getStringFromMap(ticketmap, "凭证id");
        if (StringUtils.isBlank(cvoucherid) && (!TICKETNEGATIVEIAMT || greaterThanZero)) {
            sb.append("票据关联凭证id为空数据不能入库");
        }
        String ctype = CollectionUtil.getStringFromMap(ticketmap, "发票类型");
        if (StringUtils.isBlank(ctype)) {
            sb.append("票据类型为空数据不能入库");
        }

        String ccode = CollectionUtil.getStringFromMap(ticketmap, "发票代码");
        String msg = "";
        if (StringUtils.isBlank(ccode)) {
            ccode = "";
            msg = "票号为【" + cnumber + "】";

        } else {
            msg = "票号为【" + cnumber + "】" + "编码为【" + ccode + "】";
        }
        //查询数据是否已存在iscover 覆盖
        //单据id....
        String cbillguid = CollectionUtil.getStringFromMap(ticketmap, "单据id");
        //提取组织id增加组织相关过滤
        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
        //为true 代表存在提示,
        boolean b = true;
        //判断是否含有 完整性状态增加:人工核对完整 6,人工核对不完整 7
        boolean iscintegritystatus = daGetBillDetialDao.queryTicketDetialIsCintegritywithvou(cvoucherid, cbillguid, cnumber, ccode, ctype, corgnid, dsid);
        if (iscintegritystatus) {
            sb.append("已人工核对完整的票据,不允许重复收集");
        }
        else
        {
            if (iscover) {
                //则判断单据+票据是否有重复记录
                b = daGetBillDetialDao.queryTicketDetialIsBiscollectwithvou(cvoucherid, cbillguid, cnumber, ccode, ctype, corgnid, dsid);
                if (b) {
                    sb.append("随凭证或单据收集，不允许重复收集");
                } else {
                    //则判断票据是否有重复记录
                    b = daGetBillDetialDao.queryTicketDetialAndIscover(cvoucherid, cbillguid, cnumber, ccode, ctype, corgnid, dsid);
                    if (b) {
                        sb.append("已发生后续操作，不允许重复收集");
                    }
                }

            } else {
                b = daGetBillDetialDao.queryTicketDetial(cvoucherid, cbillguid, cnumber, ccode, ctype, corgnid, dsid);
                if (b) {
                    sb.append("已存在，不允许重复收集");
                }
            }
        }

        //增加开票日期格式校验
        String ticketdate = CollectionUtil.getStringFromMap(ticketmap, "开票日期");
        if (StringUtils.isNotBlank(ticketdate))
        {
            boolean valid = ArrayUtil.isValid(ticketdate);
            if (!valid) {
                sb.append("非必填字段【开票日期】格式错误,请检查数据格式(yyyy-MM-dd)");
            }
        }
        //票据校验必填校验，固只检验实际字段
        for (String key : ticketReceiptMap.keySet()) {
            if (TICKETNEGATIVEIAMT && !greaterThanZero && key.contains("凭证")) {

            } else {
                if (ticketmap.containsKey(key)) {
                    if (isObjectEmpty(ticketmap.get(key))) {
                        sb.append("必填字段【" + key + "】值为空");
                    } else if (key.contains("日期")) {
                        boolean valid = ArrayUtil.isValid(ticketmap.get(key).toString());
                        if (!valid) {
                            sb.append("必填字段【" + key + "】格式错误,请检查数据格式(yyyy-MM-dd)");
                        }
                    }
                } else {
                    sb.append("票据资料中缺少必填字段【" + key + "】");
                }
            }


            //校验文件信息
            CheckFileName(ticketmap, sb);


            if (sb.length() > 0)
            {
                logMap.putAll(sourceBatch);
                logMap.put("cnumber", cnumber);
                logMap.put("ccode", ccode);
                logMap.put("csyncexceptionmsg", msg + sb.toString());
                logMap.put("cguid", Guid.g());
                String ccreatedate = CollectionStringUtil.getSysDateTime();
                logMap.put("ccreatedate", ccreatedate);
                List<Map> loglist = new ArrayList<>();
                loglist.add(logMap);
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_ticket_collect_log", loglist, dsid);
                return msg + sb.toString();
            }
        }
        return sb.toString();
    }

    private static void CheckFileName(Map ticketmap, StringBuffer sb)
    {
        Object filesobj = ticketmap.get("文件详情");
        //构建文件路径
        if (filesobj != null && filesobj instanceof List) {
            List<Map> oldfilelist = (ArrayList<Map>) filesobj;
            for (Map filemap : oldfilelist)
            {
                String cfilerealname = CollectionUtil.getStringFromMap(filemap, "文件实际名称");
                String filename = CollectionUtil.getStringFromMap(filemap, "存储文件名");
                if (!ArrayUtil.isValidFileName(cfilerealname))
                {
                    sb.append("文件实际名称").append(cfilerealname).append("缺少文件名后缀或文件格式错误!");
                }
                if (!ArrayUtil.isValidFileName(filename))
                {
                    sb.append("存储文件名").append(filename).append("缺少文件名后缀或文件格式错误!");
                }
            }
        }
    }
    private static Boolean isObjectEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isEmpty(s);
    }

    public static DaDataResult SaveDataByTicket(Map ticketmap, Map sourceBatch, String dsid, boolean canSXcheck, Map stomedMapByExtCode, List<Map> datailBsinessList)
    {
        List<Map> tickheadlist = new ArrayList<>();//票据主表
        List<Map> tickbodylist = new ArrayList<>();//票据子表

        List<Map> ticketAndVoucherRellist = new ArrayList<>(); //票据单据回单关联关系
        List<Map> treelist = new ArrayList<>(); //树 //默认表存储公共字段
        List filelist = new ArrayList<>();
        Map commonMap = getTicketMap(ticketmap, sourceBatch, tickheadlist, tickbodylist, treelist, ticketAndVoucherRellist, dsid, canSXcheck, stomedMapByExtCode, datailBsinessList);
        boolean status = CollectionUtil.getBoolean(commonMap, "status", false);
        try {
            if (!status) {
                Map logMap = new HashMap<>();
                logMap.putAll(sourceBatch);
                logMap.put("cnumber", commonMap.get("cnumber"));
                logMap.put("ccode", commonMap.get("ccode"));
                logMap.put("csyncexceptionmsg", commonMap.get("msg"));
                logMap.put("cguid", Guid.g());
                String ccreatedate = CollectionStringUtil.getSysDateTime();
                logMap.put("ccreatedate", ccreatedate);
                List<Map> loglist = new ArrayList<>();
                loglist.add(logMap);
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_ticket_collect_log", loglist, dsid);
                return new DaDataResult().error("").sxerror(commonMap.get("msg").toString());
            }


            if (CollectionUtil.isNotEmpty(tickheadlist)) {
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_invoice", tickheadlist, dsid);
            }

            if (CollectionUtil.isNotEmpty(tickbodylist)) {
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_invoiceline", tickbodylist, dsid);
            }

            if (CollectionUtil.isNotEmpty(ticketAndVoucherRellist)) {
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_voucherandticketrel", ticketAndVoucherRellist, dsid);
            }

            if (CollectionUtil.isNotEmpty(treelist)) {
                daGetBillDetialDao.inserDetialByTableNameByCZY("da_fview_vouandbillrela", treelist, dsid);
            }

            if (CollectionUtil.isNotEmpty(tickheadlist))
            {
                for (Map map : tickheadlist) {
                    String cvoucherid = CollectionUtil.getStringFromMap(map, "cvoucherid");
                    updateSUAIStatusMs(cvoucherid);
                    updateIntegrityStatusUpdateMs(map);
                }
            }
        }catch (Exception e)
        {
            Map logMap = new HashMap<>();
            logMap.putAll(sourceBatch);
            logMap.put("cnumber", commonMap.get("cnumber"));
            logMap.put("ccode", commonMap.get("ccode"));
            logMap.put("csyncexceptionmsg", "票据数据入库异常：【" + e.getMessage() + "】");
            logMap.put("cguid", Guid.g());
            String ccreatedate = CollectionStringUtil.getSysDateTime();
            logMap.put("ccreatedate", ccreatedate);
            List<Map> loglist = new ArrayList<>();
            loglist.add(logMap);
            daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_ticket_collect_log", loglist, dsid);

            return new DaDataResult().error("票据数据入库异常：【" + e.getMessage() + "】");

        }finally
        {
            ApsContextDb apsContext = new ApsContextDb();
            DbService db = apsContext.getDb(dsid);
            db.commit();
        }

        return new DaDataResult().sucess("票据存储完成");
    }

    /**
     * 更新凭证关联关系
     * @param cguid
     */
    public static void updateSUAIStatusMs(String cguid)
    {
        if (StringUtils.isNotEmpty(cguid))
        {
            List<String> list = new ArrayList();
            list.add(cguid);
            publishSUAIStatusMs(list);
        }
    }

    private static void updateIntegrityStatusUpdateMs(Map map)
    {
        Message ms = new Message("da.bbs.IntegrityStatusUpdateMs");
        List<Map> objects = new ArrayList<>();
        objects.add(map);
        Object rtn = ms.publish(objects);
    }

    private static void publishSUAIStatusMs(List<String> list){
        String ctablename = "da_invoice";
        Message ms = new Message("da.suai.updaterelstatusrrecollectms");
        Map par=new HashMap();
        par.put("ctablename", ctablename);
        par.put("cvoucherids", list);
        Object rtn = ms.publish(par);
    }
    private static Map getTicketMap(Map ticketmap, Map sourceBatch, List<Map> tickheadlist, List<Map> tickbodylist, List<Map> treelist, List ticketAndVoucherRellist, String dsid , boolean sXcheck, Map stomedMapByExtCode, List<Map> datailBsinessList)
    {
        //获取票据主表,并生成存储结构
        String invoiceheadid = Guid.g();
        Map invoicehead = converInvoiceHeadAndBody(ticketmap, sourceBatch, tickheadlist, tickbodylist, invoiceheadid, datailBsinessList);
        List<Map> filelist = new ArrayList<>();
        String tdate = CollectionUtil.getStringFromMap(ticketmap, "开票日期");
        String cnumber = CollectionUtil.getStringFromMap(ticketmap, "发票号码");
        String cvoucherid = CollectionUtil.getStringFromMap(ticketmap, "凭证id");

        String ticketInterval = ArrayUtil.getDataInterval(tdate);
        invoicehead.put("datainterval", ticketInterval);
        //获取单据子表
        //构建文件路径
        String extcode = CollectionUtil.getStringFromMap(sourceBatch, "extcode");
        String cserialnumber = CollectionUtil.getStringFromMap(sourceBatch, "cserialnumber");
        String dafilepath =  cvoucherid + "/" +  cnumber + "/" + CollectConstant.TICKET_NAME;
        DataConveService.publicextconverfile(ticketmap, filelist, daFileMap, "", invoiceheadid, invoicehead, "", "1", dafilepath, sourceBatch, ticketInterval, "");
        //处理文件合计内的文件
        //增加处理有文件认为已经收集文件。。。。
        if (DaConfigHelper.getBoolean("process_ticket_files_separately", false)) {
            JSONObject jsonObject = null;
            try {
                String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String msgToken = Integer.toHexString(Integer.parseInt(format));
                String msgSendTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss"));
                //头信息
                Map<String, String> headerMap = new HashMap<>();
                headerMap.put("serviceId", DaConfigHelper.getString("TAX_serviceId"));//接口标识
                headerMap.put("appsecrkey", DaConfigHelper.getString("TAX_appsecrkey"));//秘钥
                headerMap.put("sourceAppCode", DaConfigHelper.getString("TAX_sourceAppCode"));//系统名称
                headerMap.put("msgSendTime", msgSendTime);//时间戳
                headerMap.put("version", DaConfigHelper.getString("TAX_version"));//版本
                headerMap.put("msgToken", msgToken);//token
                //参数
                String invoicecode = CollectionUtil.getStringFromMap(ticketmap, "发票代码");
                String invoicenumber = CollectionUtil.getStringFromMap(ticketmap, "发票号码");
                Map<String, Object> bodyMap = new HashMap<>();
                bodyMap.put("invoicecode", invoicecode);//发票代码
                bodyMap.put("invoicenumber", invoicenumber);//发票号码
                bodyMap.put("billingdatestart", "");//开票日期始
                bodyMap.put("billingdateend", "");//开票日期末
                JSONObject object = new JSONObject(bodyMap);
                //url地址
                String url = DaConfigHelper.getString("TAX_url");
                //发送请求
                HttpResponse res = postResponse(url, object, headerMap);
                jsonObject = JSONObject.parseObject(EntityUtils.toString(res.getEntity()));
            } catch (IOException e) {
                log.error("数电发票档案文件查询接口返回值解析错误：" + e);
            }
            Map<String, Object> jsonMap = jsonObject;

            Object filejson = jsonMap.get("resultList");

            if (filejson != null && filejson instanceof List) {
                List<Map> oldfilelist = (ArrayList<Map>) filejson;
                for (Map oldfile : oldfilelist) {
                    Map filemap = new HashMap();
                    filemap.put("存储文件名", oldfile.get("filename"));
                    filemap.put("xbrlname", "");
                    filemap.put("存储方式", "url");
                    //文件来源类型(0凭证的文件，1单据的文件，2发票的文件(ocr识别文件，非ocr识别文件),3银行回单,4模拟打印文件,5其他， 6会计账簿， 7会计报告，8其他会计资料9电子档案模拟打印设置的
                    filemap.put("文件来源类型", "2");
                    filemap.put("文件类型", "2");
                    filemap.put("文件收集方式", "1");
                    filemap.put("文件实际名称", oldfile.get("filename"));
                    filemap.put("xbrl文件哈希码", oldfile.get("mdnumber"));
                    filemap.put("xbrl文件哈希码类型", oldfile.get("chxtype"));
                    filemap.put("xbrl文件访问地址", oldfile.get("filepath"));
                    filemap.put("凭证id", ticketmap.get("凭证id"));
                    DataConveService.publicextconverfile(filemap, filelist, daFileMap, "", invoiceheadid, invoicehead, "", "0", dafilepath, sourceBatch, ticketInterval, "");
                }
            }
        } else {
            Object filesobj = ticketmap.get("文件详情");

            if (filesobj != null && filesobj instanceof List) {
                List<Map> oldfilelist = (ArrayList<Map>) filesobj;

                for (Map filemap : oldfilelist) {
                    filemap.put("凭证id", ticketmap.get("凭证id"));
                    DataConveService.publicextconverfile(filemap, filelist, daFileMap, "", invoiceheadid, invoicehead, "", "0", dafilepath, sourceBatch, ticketInterval, "");
                }
            }
        }

        String cfiles_table_name = CollectionUtil.getStringFromMap(invoicehead, "cfiles_table_name");
        ticketmap.put("cfiles_table_name", cfiles_table_name);
        //文件四性检测
        convertree(ticketmap, sourceBatch, treelist, treePublicMap, treeInvoiceMap, "2", invoiceheadid, "", billUrl + "da_ticket_ysj/da_ticket_ysj/" + invoiceheadid + billUrlName + "票据", "0", cfiles_table_name);
        //构建关联关系
        Map ticketAndVoucherRel = new HashMap<>();
        ticketAndVoucherRel.putAll(sourceBatch);
        ticketAndVoucherRel.putAll(invoicehead);
        ticketAndVoucherRel.put("cbillid", invoicehead.get("cbillguid"));
        ticketAndVoucherRel.put("cvoucode", invoicehead.get("cvoucherno"));

        ticketAndVoucherRel.put("cguid", Guid.g());
        ticketAndVoucherRellist.add(ticketAndVoucherRel);

        String daFileTableName = daGetBillDetialDao.getDaFileTableName(cfiles_table_name);
        if (CollectionUtil.isNotEmpty(filelist)) {
            //
            if (TICKETSTATE) {
                invoicehead.put("ext_existfile_name", "已收集");
                invoicehead.put("ext_existfile", "1");
            }
            List nfilelist = new ArrayList<>();
            for (Map filemap : filelist) {
                Map map = ExtFileUploadUtil.dowloadFile(filemap, stomedMapByExtCode, sXcheck);
                boolean status = CollectionUtil.getBoolean(map, "status", false);
                //若为true代表文件下载成功若四性检测可以进行检测
                if (!status) {
                    map.put("cnumber", invoicehead.get("cnumber"));
                    map.put("ccode", invoicehead.get("ccode"));
                    return map;
                }

                //将下载文件成功的map 放入新的list以便数据入库
                filemap.put("cstatus", "1");
                filemap.put("cstatus_name", "收集成功");
                filemap.put("storagemethod", "default");
                filemap.put("cfilessize", map.get("cfilessize"));
                //增加ES字段
                filemap.put("cfileid", filemap.get("cguid"));
                filemap.put("cfilenum", "");
                filemap.put("cqznum","");
                filemap.put("caccountclassname","");
                filemap.put("caccountclasscode","");
                filemap.put("cdetailclassname","");
                filemap.put("cdetailclasscode","");
                filemap.put("cperioddate","");
                filemap.put("ccustody_year_name","");

                //提取票据字段
                filemap.put("cfilecontents", invoicehead.get("cfilecontents"));
                nfilelist.add(filemap);
            }

            //都下载完成且四性检测通过认为文件下载完成拆入下载完成字段
            int sjnum = nfilelist.size();
            int ifileqty = CollectionUtil.getIntFromMap(invoicehead, "ifileqty");
            if (sXcheck && ifileqty > 0 && ifileqty != sjnum)
            {
                Map map = new HashMap<>();
                map.put("cnumber", invoicehead.get("cnumber"));
                map.put("ccode", invoicehead.get("ccode"));
                map.put("msg", "四性检测不通过:实际文件数【" + sjnum + "】与传递过来要收集的文件数【" + ifileqty +"】不一致");
                return map;
            }

            invoicehead.put("downloadfilenum", sjnum);




            String ccode = CollectionUtil.getStringFromMap(invoicehead, "ccode");
            String ctype = CollectionUtil.getStringFromMap(invoicehead, "ctype");
            String cbillguid = CollectionUtil.getStringFromMap(invoicehead, "cbillguid");

            //数据开始存储,因为不覆盖时数据发生校验无法走到这里，走到这里的为覆盖情况，处理覆盖情况下的重复数据
            String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
            List<Map> invoicelist = daGetBillDetialDao.queryTicketDetialList(cvoucherid, cbillguid, cnumber, ccode, ctype, corgnid, dsid);

            List<String> deletefilecguidlist = new ArrayList<>();

            if (CollectionUtil.isNotEmpty(invoicelist))
            {
                deletefilecguidlist = daGetBillDetialDao.queryTicketFile(invoicelist, dsid);
                daGetBillDetialDao.deleteTicketDetial(cvoucherid, cbillguid, invoicehead, cnumber, invoicelist, corgnid, dsid);
            }
            daGetBillDetialDao.inserDetialByTableNameByCZY(daFileTableName, nfilelist, dsid);

            try
            {
                if (CollectionUtil.isNotEmpty(nfilelist))
                {
                    ESUtil.addSeDetial(nfilelist, dsid, "2");
                }
                if (CollectionUtil.isNotEmpty(deletefilecguidlist))
                {
                    ESUtil.deleteSeDetial(deletefilecguidlist, "2");
                }

            }catch (Exception e)
            {
                log.error("票据调用ES发生错误：" + e);
                e.printStackTrace();
                log.error("票据调用ES发生错误：" + e);
            }


        } else {
            if (TICKETSTATE) {
                invoicehead.put("ext_existfile_name", "未收集");
                invoicehead.put("ext_existfile", "0");
            }

            String ccode = CollectionUtil.getStringFromMap(invoicehead, "ccode");
            String ctype = CollectionUtil.getStringFromMap(invoicehead, "ctype");
            String cbillguid = CollectionUtil.getStringFromMap(invoicehead, "cbillguid");
            //数据开始存储,因为不覆盖时数据发生校验无法走到这里，走到这里的为覆盖情况，处理覆盖情况下的重复数据
            String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
            List<Map> invoicelist = daGetBillDetialDao.queryTicketDetialList(cvoucherid, cbillguid, cnumber, ccode, ctype, corgnid, dsid);
            List<String> deletefilecguidlist = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(invoicelist)) {
                deletefilecguidlist = daGetBillDetialDao.queryTicketFile(invoicelist, dsid);
                daGetBillDetialDao.deleteTicketDetial(cvoucherid, cbillguid, invoicehead, cnumber, invoicelist, corgnid, dsid);
            }
            try
            {
                if (CollectionUtil.isNotEmpty(deletefilecguidlist))
                {
                    ESUtil.deleteSeDetial(deletefilecguidlist, "2");
                }

            }catch (Exception e)
            {
                log.error("票据调用ES发生错误：" + e);
                e.printStackTrace();
                log.error("票据调用ES发生错误：" + e);
            }
        }
        tickheadlist.add(invoicehead);

        Map rtnmap = new HashMap<>();
        rtnmap.putAll(invoicehead);
        rtnmap.put("status", true);
        return rtnmap;
    }

    public static Map getTicketHead(Map ticketmap)
    {
        Map nHeadMap = new HashMap<>();
        for (String key : invoiceHeadMap.keySet()) {
            if ("价税合计".equals(key)) {
                String cclass = CollectionUtil.getStringFromMap(ticketmap, "发票大类");
                if (CCLASSES.contains(cclass)) {
                    nHeadMap.put(invoiceHeadMap.get(key), ticketmap.get("总金额"));
                    continue;
                }
            }
            if ("会计年月".equals(key)) {
                String iyear = CollectionUtil.getStringFromMap(ticketmap, "会计年");
                String imonth = CollectionUtil.getStringFromMap(ticketmap, "会计月");
                String pzdate = CollectionUtil.getStringFromMap(ticketmap, "凭证日期");

                String tdate = CollectionUtil.getStringFromMap(ticketmap, "开票日期");
                if (StringUtils.isNotEmpty(tdate) && tdate.length() > 6) {
                    String[] ts = tdate.split("-");
                    if (ts.length == 3) {
                        nHeadMap.put("tyear", ts[0]);
                        nHeadMap.put("tmonth", ts[1]);
                    }
                }

                if (StringUtils.isNotEmpty(iyear) && StringUtils.isNotEmpty(imonth)) {
                    nHeadMap.put(invoiceHeadMap.get(key), iyear + "-" + imonth);
                } else {
                    if (StringUtils.isNotEmpty(pzdate)) {
                        String[] split = pzdate.split("-");
                        if (split.length == 3) {
                            iyear = split[0];
                            imonth = split[1];
                            nHeadMap.put(invoiceHeadMap.get("会计年"), iyear);
                            nHeadMap.put(invoiceHeadMap.get("会计月"), imonth);
                            nHeadMap.put(invoiceHeadMap.get(key), iyear + "-" + imonth);
                        }
                    }
                }
                continue;
            }

            if (isObjectNotEmpty(ticketmap.get(key))) {
                nHeadMap.put(invoiceHeadMap.get(key), ticketmap.get(key));
            }
        }
        nHeadMap.put("cbillcode", ticketmap.get("单据编号"));

        return nHeadMap;
    }
    private static Map converInvoiceHeadAndBody(Map ticketmap, Map sourceBatch, List<Map> tickheadlist, List<Map> tickbodylist, String invoiceheadid, List<Map> datailBsinessList) {
        Map nHeadMap = new HashMap<>();
        nHeadMap.putAll(sourceBatch);
        for (String key : invoiceHeadMap.keySet()) {
            if ("价税合计".equals(key)) {
                String cclass = CollectionUtil.getStringFromMap(ticketmap, "发票大类");
                if (CCLASSES.contains(cclass)) {
                    nHeadMap.put(invoiceHeadMap.get(key), ticketmap.get("总金额"));
                    continue;
                }
            }
            if ("会计年月".equals(key)) {
                String iyear = CollectionUtil.getStringFromMap(ticketmap, "会计年");
                String imonth = CollectionUtil.getStringFromMap(ticketmap, "会计月");
                String pzdate = CollectionUtil.getStringFromMap(ticketmap, "凭证日期");

                String tdate = CollectionUtil.getStringFromMap(ticketmap, "开票日期");
                if (StringUtils.isNotEmpty(tdate) && tdate.length() > 6) {
                    String[] ts = tdate.split("-");
                    if (ts.length == 3) {
                        nHeadMap.put("tyear", ts[0]);
                        nHeadMap.put("tmonth", ts[1]);
                    }
                }

                if (StringUtils.isNotEmpty(iyear) && StringUtils.isNotEmpty(imonth)) {
                    nHeadMap.put(invoiceHeadMap.get(key), iyear + "-" + imonth);
                } else {
                    if (StringUtils.isNotEmpty(pzdate)) {
                        String[] split = pzdate.split("-");
                        if (split.length == 3) {
                            iyear = split[0];
                            imonth = split[1];
                            nHeadMap.put(invoiceHeadMap.get("会计年"), iyear);
                            nHeadMap.put(invoiceHeadMap.get("会计月"), imonth);
                            nHeadMap.put(invoiceHeadMap.get(key), iyear + "-" + imonth);
                        }
                    }
                }
                continue;
            }

            nHeadMap.put(invoiceHeadMap.get(key), ticketmap.get(key));
        }
        //放入新增权限字段。。。
        //档案对照乘车人
        nHeadMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(nHeadMap,"passenger"),CollectionUtil.getStringFromMap(nHeadMap,"passengercode"),"user", "cdapassengerid", "cdapassenger"));
        //档案对照复核人
        nHeadMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(nHeadMap,"passenger"),CollectionUtil.getStringFromMap(nHeadMap,"passengercode"),"user","cdareviewerid","cdareviewer"));
        //档案对照收款人
        nHeadMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(nHeadMap,"passenger"),CollectionUtil.getStringFromMap(nHeadMap,"passengercode"),"user","cpayeeid","cpayee"));

        nHeadMap.put("ctemplateid", "da_data_collection_ticket");
        nHeadMap.put("cbillcode", ticketmap.get("单据编号"));
        //构建body数据
        Object bodylist = ticketmap.get("票据详情子表");

        List<Map> invoiceblist = new ArrayList<>();
        if (bodylist != null && bodylist instanceof List) {
            invoiceblist = (ArrayList<Map>) bodylist;
            for (Map bodymap : invoiceblist) {
                converInvoiceBody(bodymap, invoiceBodyMap, invoiceheadid, tickbodylist, sourceBatch);
            }
        }

        StringBuffer stringBuffer = EsConvertUtil.getTicketCfilecontentsBySJ(ticketmap, invoiceblist);

        //生成文件存储表，
        String ddtate = CollectionUtil.getStringFromMap(ticketmap, "开票日期");
        String cfiles_table_name = "";
        if (StringUtils.isNotBlank(ddtate) && ddtate.length() > 4) {
            cfiles_table_name = "da_files_" + ddtate.substring(0, 4);
        } else {

            cfiles_table_name = "da_files_" + getSysYear();
        }
        nHeadMap.put("cfiles_table_name", cfiles_table_name);
        nHeadMap.put("cfiletablename", cfiles_table_name);
        sourceBatch.put("cfiles_table_name", cfiles_table_name);
        sourceBatch.put("cfiletablename", cfiles_table_name);

        nHeadMap.put("datasource", CollectionUtil.getStringFromMap(sourceBatch, "csourcesysname"));

        makestatusValue(nHeadMap);
        nHeadMap.put("biscollectwithvou", "0");
        nHeadMap.putAll(makeBatchDefalultValue(sourceBatch));
        nHeadMap.put("cguid", invoiceheadid);
        //修改数据放入逻辑
        nHeadMap.put("cfilecontents", stringBuffer);
       // tickheadlist.add(nHeadMap);
        return nHeadMap;
    }


    private static Map makestatusValue(Map dMap) {
        //组卷状态
        dMap.put("centityfilestatus", "0");
        dMap.put("centityfilestatus_name", "收集成功");
        //归档状态
        dMap.put("ceastatus", "0");
        dMap.put("ceastatus_name", "收集成功");
        //四性检测状态
    /*    dMap.put("isuaistatus", "0");
        dMap.put("csuaistatus", "未检测");*/
        //手工关联状态
        dMap.put("imanualrelstatus", "0");
        dMap.put("cmanualrelstatus", "未关联");

        dMap.put("isuaistatus", "1");
        dMap.put("csuaistatus", "已检测");

        //票据归档状态
        dMap.put("cbilleastatus", "0");
        dMap.put("cbilleastatus_name", "收集成功");

        String cvoucherid = CollectionUtil.getStringFromMap(dMap, "cvoucherid");
        if (StringUtils.isNotEmpty(cvoucherid) && TICKETCRELSTATUS) {
            dMap.put("crelstatus", "1");
            dMap.put("crelstatus_name", "已关联");
        } else {
            dMap.put("crelstatus", "0");
            dMap.put("crelstatus_name", "未关联");
        }
        return dMap;
    }

    private static Boolean isObjectNotEmpty(Object o) {
        String s = Objects.toString(o, "");
        return StringUtils.isNotEmpty(s);
    }

    public static String converInvoiceBody(Map bodymap, HashMap<String, String> invoiceBodyMap, String invoiceheadid, List<Map> invoicebodylist, Map cvmap) {
        StringBuffer bodysb = new StringBuffer();
        Map nBodyMap = new HashMap<>();
        for (String key : invoiceBodyMap.keySet()) {
            nBodyMap.put(invoiceBodyMap.get(key), bodymap.get(key));
            if(isObjectNotEmpty(bodymap.get(key)))
            {
                bodysb.append(bodymap.get(key));
            }
        }
        nBodyMap.put("cheadguid", invoiceheadid);
        nBodyMap.putAll(makeBatchDefalultValue(cvmap));
        nBodyMap.put("cguid", Guid.g());
        invoicebodylist.add(nBodyMap);
        return bodysb.toString();
    }

    private static Map makeBatchDefalultValue(Map batchmap) {
        Map map = new HashMap<>();
        String corgnid = CollectionUtil.getStringFromMap(batchmap, "corgnid");
        String corgnid_name = CollectionUtil.getStringFromMap(batchmap, "corgnid_name");
        String cadminorgnid = CollectionUtil.getStringFromMap(batchmap, "cadminorgnid");
        String cadminorgnid_name = CollectionUtil.getStringFromMap(batchmap, "cadminorgnid_name");
        String ccreatorid = CollectionUtil.getStringFromMap(batchmap, "ccreatorid");
        String ccreatorid_name = CollectionUtil.getStringFromMap(batchmap, "ccreatorid_name");
        String ccreatedate = CollectionStringUtil.getSysDateTime();
        String csourcesysname = CollectionUtil.getStringFromMap(batchmap, "csourcesysname");

        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate", ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name", ccreatorid_name);

        map.put("ccollectorid", ccreatorid);
        map.put("ccollectorid_name", ccreatorid_name);
        map.put("csourcesysname", csourcesysname);
        return map;
    }

    public static void converfile(Map oldfileMap, List<Map> filelist, HashMap<String, String> daFileMap, String blankid, String invoiceid, Map cvmap, String oldbillid, String ishead) {
        String filename = CollectionUtil.getStringFromMap(oldfileMap, "存储文件名");
        if (StringUtils.isBlank(filename)) {
            return;
        }
        Map nfileMap = new HashMap<>();
        for (String key : daFileMap.keySet()) {
            if ("文件类型".equalsIgnoreCase(key)) {
                //文件来源类型(0凭证的文件，1单据的文件，2发票的文件(ocr识别文件，非ocr识别文件),3银行回单,4模拟打印文件,5其他， 6会计账簿， 7会计报告，8其他会计资料9电子档案模拟打印设置的
                String cfilesourcetype = CollectionUtil.getStringFromMap(oldfileMap, "文件来源类型");
                if (!StringUtils.isBlank(cfilesourcetype)) {
                    if (cfilesourcetype.contains("4")) {
                        nfileMap.put(daFileMap.get(key), "2");
                        nfileMap.put("syssource", "fk-imm");
                    } else if (cfilesourcetype.contains("2")) {
                        nfileMap.put(daFileMap.get(key), "1");
                        nfileMap.put("syssource", "fk-imm");
                    } else if (cfilesourcetype.contains("9")) {
                        nfileMap.put(daFileMap.get(key), "2");
                        nfileMap.put("syssource", "fk-imm");
                    } else {
                        nfileMap.put(daFileMap.get(key), "0");
                        nfileMap.put("syssource", "fk-appendix");
                    }
                } else {
                    nfileMap.put(daFileMap.get(key), oldfileMap.get(key));
                }
            } else if ("文件收集方式".equalsIgnoreCase(key)) {
                Object sjfs = oldfileMap.get(key);
                if (sjfs instanceof Number) {
                    if (sjfs instanceof Integer) {
                        nfileMap.put(daFileMap.get(key), sjfs);
                    } else {
                        Double aDouble = (Double) oldfileMap.get(key);
                        int i = aDouble.intValue();
                        nfileMap.put(daFileMap.get(key), i);
                    }
                } else {
                    nfileMap.put(daFileMap.get(key), sjfs);
                }

            } else if ("文件来源类型".equalsIgnoreCase(key)) {
                if (StringUtils.isNotBlank(invoiceid)) {
                    nfileMap.put(daFileMap.get(key), "2");
                } else if (StringUtils.isNotBlank(blankid)) {
                    nfileMap.put(daFileMap.get(key), "3");
                } else {
                    nfileMap.put(daFileMap.get(key), "1");
                }
            } else {
                nfileMap.put(daFileMap.get(key), oldfileMap.get(key));

            }
        }
        nfileMap.put("cguid", Guid.g());
        if (StringUtils.isBlank(blankid) && StringUtils.isBlank(invoiceid)) {
            nfileMap.put("cbusinesstype", "1");
        } else if (StringUtils.isNotEmpty(invoiceid)) {
            nfileMap.put("cbusinesstype", "2");
        } else if (StringUtils.isNotEmpty(blankid)) {
            nfileMap.put("cbusinesstype", "3");
        }
        nfileMap.put("ishead", ishead);
        nfileMap.put("bankid", blankid);
        nfileMap.put("invoiceid", invoiceid);
        if (StringUtils.isNotBlank(oldbillid)) {
            nfileMap.put("cbillid", oldbillid);
        }
        nfileMap.putAll(cvmap);
        String cfiletype = CollectionUtil.getStringFromMap(nfileMap, "cfiletype");
        if (StringUtils.isBlank(cfiletype)) {
            nfileMap.put("cfiletype", "0");
        }
        String xbrlname = CollectionUtil.getStringFromMap(oldfileMap, "xbrlname");
        if (StringUtils.isNotBlank(invoiceid) && StringUtils.isNotBlank(xbrlname)) {
            //增加xbrl 文件处理
            Map xbrlmap = new HashMap<>();
            xbrlmap.putAll(nfileMap);
            xbrlmap.put("cguid", Guid.g());
            xbrlmap.put("filename", xbrlname);
            xbrlmap.put("cfilerealname", xbrlname);
            xbrlmap.put("mdnumber", oldfileMap.get("xbrl文件哈希码"));
            xbrlmap.put("chxtype", oldfileMap.get("xbrl文件哈希码类型"));

            filelist.add(xbrlmap);
        }

        filelist.add(nfileMap);
    }

    public static void convertree(Map headMap, Map cvmap, List<Map> treelist, HashMap<String, String> treePublicMap, HashMap<String, String> treePrivateMap, String ctype, String invoiceheadid, String bankid, String url, String ishead, String cfiles_table_name) {
        Map tMap = new HashMap<>();

        for (String key : treePublicMap.keySet()) {
            if (key == "cparent") {
                String cparent = CollectionUtil.getStringFromMap(headMap, treePublicMap.get(key));
                if (StringUtils.isBlank(cparent)) {
                    tMap.put(key, "000000");
                } else {
                    tMap.put(key, headMap.get(treePublicMap.get(key)));
                }
            } else {
                tMap.put(key, headMap.get(treePublicMap.get(key)));
            }
        }

        for (String key : treePrivateMap.keySet()) {
            if (!"1".equalsIgnoreCase(ctype) && !"cparent".contains(key)) {
                if ("cmeansname".equalsIgnoreCase(key)) {
                    tMap.put(key, treePrivateMap.get(key));
                } else {
                    tMap.put(key, headMap.get(treePrivateMap.get(key)));
                }
            } else if ("2".equalsIgnoreCase(ctype)) {
                Object o = headMap.get(treePrivateMap.get(key));
                if (isObjectNotEmpty(o)) {
                    tMap.put(key, o);

                } else {
                    //tMap.put(key, headMap.get(treePrivateMap.get(key)));

                    tMap.put(key, CollectionUtil.getStringFromMap(headMap, "凭证id"));
                }
            } else if ("3".equalsIgnoreCase(ctype)) {
                tMap.put(key, headMap.get(treePrivateMap.get(key)));
            }
        }

        if ("2".equalsIgnoreCase(ctype)) {
            tMap.put("cmeansid", invoiceheadid);
            String cmeansname = CollectionUtil.getStringFromMap(headMap, "发票类型");
            if (StringUtils.isNotBlank(cmeansname)) {
                tMap.put("cmeansname", cmeansname);
            }
        } else if ("3".equalsIgnoreCase(ctype)) {
            tMap.put("cmeansid", bankid);
        }


        tMap.put("ctype", ctype);
        tMap.put("cguid", Guid.g());
        tMap.put("pageurl", url);
        tMap.put("ishead", ishead);
        tMap.putAll(makeBatchDefalultValue(cvmap));
        treelist.add(tMap);
    }

    public static Map getSXMap(Map datamap, Map batchmap, String caccountclass, List<Map> filelist, String dsid) {
        Map sxmap = new HashMap<>();
        sxmap.put("caccountclass", caccountclass);
        sxmap.put("dsid", dsid);
        sxmap.put("currentOrgnId", CollectionUtil.getStringFromMap(batchmap, "corgnid"));
        sxmap.put("currentOrgnName", CollectionUtil.getStringFromMap(batchmap, "corgnid_name"));
        sxmap.put("currentAdminOrgnId", CollectionUtil.getStringFromMap(batchmap, "cadminorgnid"));
        sxmap.put("currentAdminOrgnName", CollectionUtil.getStringFromMap(batchmap, "cadminorgnid_name"));
        sxmap.put("currentUserId", CollectionUtil.getStringFromMap(batchmap, "ccollector"));
        sxmap.put("currentUserName", CollectionUtil.getStringFromMap(batchmap, "ccollector_name"));

        List filel = new ArrayList<>();
        Map filemap = new HashMap<>();
        filemap.put("ccode", datamap.get("ccode"));
        filemap.put("cnumber", datamap.get("cnumber"));

        filemap.put("fileList", filelist);
        filel.add(filemap);
        sxmap.put("list", filel);
        return sxmap;
    }

    private static String getSysDate() {
        Date date = new Date();
        SimpleDateFormat s = new SimpleDateFormat("yyyy-MM");
        String time = s.format(date);
        return time;
    }

    private static String getSysYear() {
        Date date = new Date();
        SimpleDateFormat s = new SimpleDateFormat("yyyy");
        String time = s.format(date);
        return time;
    }

    public static void getTicketLog(Map ticketmap, Map sourceBatch, String dsid, String savemsg) {
        Map logMap = new HashMap<>();

        String cnumber = CollectionUtil.getStringFromMap(ticketmap, "发票号码");

        String ccode = CollectionUtil.getStringFromMap(ticketmap, "发票代码");
        String msg = "";
        if (StringUtils.isBlank(ccode)) {
            ccode = "";
            msg = "票号为【" + cnumber + "】";

        } else {
            msg = "票号为【" + cnumber + "】" + "编码为【" + ccode + "】";
        }
        logMap.putAll(sourceBatch);
        logMap.put("cnumber", cnumber);
        logMap.put("ccode", ccode);
        logMap.put("csyncexceptionmsg", msg + savemsg);
        logMap.put("cguid", Guid.g());
        List<Map> loglist = new ArrayList<>();
        loglist.add(logMap);
        daGetBillDetialDao.inserDetialByTableNameByCZY("da_fc_ticket_collect_log", loglist, dsid);
    }

    /**
     * post请求
     *
     * @param url
     * @param jsonParam
     * @param headerMap
     * @return json
     */
    public static HttpResponse postResponse(String url, JSONObject jsonParam, Map<String, String> headerMap) {
        CloseableHttpClient client = HttpClients.createDefault();
        //     要调用的接口方法
        HttpPost post = new HttpPost(url);
        //        JSONObject jsonObject = null;
        HttpResponse res = null;
        try {
            StringEntity body = new StringEntity(jsonParam.toString(), "UTF-8");
            body.setContentType("application/json");
            post.setEntity(body);
            Set<Map.Entry<String, String>> heads = headerMap.entrySet();
            for (Map.Entry<String, String> head : heads) {
                post.setHeader(head.getKey(), head.getValue());
            }
            res = client.execute(post);
        } catch (Exception e) {
            log.error("数电发票档案文件查询接口调用出错！" + url);
            log.error(e.toString());
        }finally {
            try {
                client.close();
            } catch (IOException e) {
                log.error("关闭链接异常！" + url);
                log.error(e.toString());
            }
        }
        return res;
    }

    private static boolean isGreaterThanZero(String strNum) {
        if (strNum == null || strNum.isEmpty()) {
            return false;
        }

        // 使用正则表达式排除非数字和负数
        if (!strNum.matches("\\d+(\\.\\d+)?")) {
            return false;
        }

        // 使用BigDecimal来进行精确的比较
        BigDecimal bd = new BigDecimal(strNum);
        BigDecimal zero = BigDecimal.ZERO;
        return bd.compareTo(zero) >= 0;
    }
}
