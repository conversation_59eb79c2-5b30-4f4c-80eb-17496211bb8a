package com.aisino.da.api.service.sjqp.impl;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.da.api.dao.TicketSwszDao;
import com.aisino.da.api.service.sjqp.TicketSwszService;

import java.util.List;
import java.util.Map;

@Service
public class TicketSwszServiceImpl implements TicketSwszService
{
    @Inject
    private TicketSwszDao ticketSwszDao;

    @Override
    public Map getFileConfigMap() {
        return ticketSwszDao.ticketSwszDao();
    }

    @Override
    public List<Map> getTicketlist(List<String> ticketidlist) {
        return ticketSwszDao.getTicketlist(ticketidlist);
    }

    @Override
    public List<Map> queryDownloadConfig(List<String> servicenamelist) {
        return ticketSwszDao.queryDownloadConfig(servicenamelist);
    }

    @Override
    public void deleteTicketFile(String ticketid, String fileTableName) {
        ticketSwszDao.deleteTicketFile(ticketid, fileTableName);
    }

    @Override
    public void addDowloadlog(Map insertmap) {
        ticketSwszDao.addDowloadlog(insertmap);
    }

    @Override
    public void insertFilelist(List<Map> filelist, String tableName) {
        ticketSwszDao.insertFilelist(filelist, tableName);
    }

    @Override
    public void updatelog(String cguid) {
        ticketSwszDao.updatelog(cguid);
    }
}
