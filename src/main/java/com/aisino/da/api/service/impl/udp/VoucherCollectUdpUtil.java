package com.aisino.da.api.service.impl.udp;

import cn.hutool.core.util.ObjectUtil;
import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Bean;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.MapUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.da.api.dao.CollectDataUtilDaoInterface;
import com.aisino.da.bbs.action.integrity.IntegrityRuleCheckAction;
import com.aisino.da.fc.dao.filecollect.CollectDataDaoInterface;
import com.aisino.da.fc.util.StaticCodeClass;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * VoucherCollectUdpUtil
 *
 * <AUTHOR>
 * @version 1.0, 2023/11/24
 * @see [相关类/方法]
 * @since [产品/模块版本]
 * 凭证清单工具类
 */
@Bean
public class VoucherCollectUdpUtil  {

    private static final Logger LOG = LoggerFactory.getLogger(VoucherCollectUdpUtil.class);

    @Inject
    CollectDataDaoInterface collectDataDaoInterface;
    @Inject
    CollectDataUtilDaoInterface collectDataUtilDaoInterface;
    @Inject
    IntegrityRuleCheckAction integrityRuleCheckAction;
    @Inject
    VoucherCollectUdpSql voucherCollectUdpSql;

    /**
     * 获取凭证数据，转化sap凭证清单数据;
     * @return
     */
    public Object getVoucherList(List<Map> dataList,Map<String,Object> sourceBatchMap,Map<String, List<Map>> oldDataMap,List<Map> oldDataListZb){
        // 按照年度和凭证字号分组
        Map<String, List<Map>> dataMap = dataList.stream().collect(Collectors.groupingBy(map -> CollectionUtil.getStringFromMap(map, "GJAHR") + CollectionUtil.getStringFromMap(map, "BELNR")));
        // 凭证数据集合
        List<Map> voucherList = new ArrayList<>(dataMap.size()/2);
        // 凭证元数据集合
        List<Map> voucherMetaDataList = new ArrayList<>(dataMap.size()/2);
        // 凭证元数据子表集合
        List<Map> voucherMetaSonDataList = new ArrayList<>(dataList.size()/2);
        // 凭证错误数据集合 用于添加日志信息
        List<Map> errVoucherList = new ArrayList<>();
        // 保存用户覆盖收集的列表id，用于删除列表数据
        List<String> errVoucherIds = new ArrayList<>();
        // 保存资料检索查看元数据表
        List<Map>  vouandbillrelaList = new ArrayList<>();
        // 保存凭证类型数据
        List<Map>  voucherTypeList = new ArrayList<>();
        // 保存凭证和单据数据
        List<Map>  voucherBillList = new ArrayList<>();
        // 保存明细账map
        Map<String,Object> mxzMap = new HashMap<>(5);
        // 保存列表明细账map
        List<Map>  mxzList = new ArrayList<>();
        // 保存明细账元数据主表
        List<Map>  mxzMdList = new ArrayList<>();
        // 保存明细账元数据子表
        List<Map>  mxzMdzList = new ArrayList<>();
        // 保存明细账需要删除的列表id
        List<String>  errMxzListId = new ArrayList<>();
        // 保存明细账得会计期间，包含则证明进入后续业务，同时凭证也不允许收集同期间的数据
        List<String>  mzxPeriodList = new ArrayList<>();
        // 是否覆盖收集
        String icover = CollectionUtil.getStringFromMap(sourceBatchMap, "icover");
        LOG.info("sap返回数据结果集为:"+dataList.size());
        LOG.info("将sap数据进行分组，数据量为:"+dataMap.size());
        // 明细id
        List<Map> detailClassByMainCodeList = collectDataDaoInterface.queryDetailClassByMainCode(StaticCodeClass.voucherRoot);
        List<Map> busTypeInfoList = collectDataDaoInterface.queryBusTypeInfo();
        List<Map> detailBusinessList = collectDataUtilDaoInterface.getDetailBusiness(CollectionUtil.getStringFromMap(sourceBatchMap, "extcode"));
        // 获取用户信息
        Map<String, String> userInfo = voucherCollectUdpSql.getUserInfo();
        for (List<Map> value : dataMap.values()) {
            String retMsg;
            // 转换凭证map
            Map<String, Object> voucherMap = this.getVoucherMap(value, sourceBatchMap,detailClassByMainCodeList,busTypeInfoList,detailBusinessList,userInfo);
            String cvoutypename = CollectionUtil.getStringFromMap(voucherMap, "cvoutypename");
            String cperioddatelist = CollectionUtil.getStringFromMap(voucherMap, "cperioddatelist");
            mxzMap.put(cperioddatelist,new HashMap<>(voucherMap));
            // todo 元数据主表校验
            retMsg = this.validateMetaDate(voucherMap);
            // 有值 校验失败,不进行后续流程
            if (StringUtil.isNotEmpty(retMsg)) {
                voucherMap.put("csyncexceptionmsg",retMsg);
                voucherMap.put("msg",retMsg);
                this.transformCollectionLogs(voucherMap,sourceBatchMap);
                errVoucherList.add(voucherMap);
                continue;
            }
            /*// 转换明细账数据
            this.getSubsidiaryMap(voucherMap, sourceBatchMap,mxzMap);*/
            // 获取列表旧数据
            List<Map> oldList = oldDataMap.get(cvoutypename);
            // todo 是否覆盖收集
            retMsg = this.isOverlayCollection(icover, voucherMap, oldList, errVoucherIds);
            // 有值 校验失败,不进行后续流程
            if (StringUtil.isNotEmpty(retMsg)) {
                voucherMap.put("csyncexceptionmsg",retMsg);
                voucherMap.put("msg",retMsg);
                this.transformCollectionLogs(voucherMap,sourceBatchMap);
                errVoucherList.add(voucherMap);
                continue;
            }
            Map<String,Object> voucherMetaDataMap = (Map) voucherMap.get("md_voucher");
            // 转换查看元数据表数据
            Map<String, Object> vouandbillrelaMap = this.getVouandbillrelaMap(voucherMetaDataMap, sourceBatchMap);
            vouandbillrelaList.add(vouandbillrelaMap);
            // 转换凭证类型表数据
            Map<String, Object> voucherTypeMap = this.getVoucherTypeMap(voucherMap, sourceBatchMap, voucherTypeList);
            if(MapUtil.isNotEmpty(voucherTypeMap)){
                voucherTypeList.add(voucherTypeMap);
            }
            // 获取凭证号段
            String extCvouchercoderange = voucherMap.get("ext_cvouchercoderange").toString();
            if(!"310".equals(extCvouchercoderange) && !"710".equals(extCvouchercoderange)){
                // 转换凭证和单据的关联表数据
                Map<String, Object> voucherBillMap = this.getVoucherBillMap(voucherMap, sourceBatchMap);
                voucherBillList.add(voucherBillMap);
            }
            // 添加凭证数据
            voucherList.add(voucherMap);
            // 元数据
            voucherMetaDataList.add(voucherMetaDataMap);
            // 元数据子表
            voucherMetaSonDataList.addAll((List<Map>)voucherMap.get("md_voucherz"));
        }
        // 将凭证元数据子表分组
        Map<String, List<Map>> groupByMzx = voucherMetaSonDataList.stream().collect(Collectors.groupingBy(map -> map.get("cperioddatelist").toString()));
        // 判断账簿是否覆盖收集;
        this.isOverlayCollectionZb(icover,mxzMap,sourceBatchMap,oldDataListZb, errMxzListId,mxzList,mxzMdList,mxzMdzList,groupByMzx);
        // 将数据保存到各个集合;
        String errMxzMsg = this.saveSubledger(mxzMap, mxzList, mxzMdList, mxzMdzList, mzxPeriodList);
        //StringBuilder errPeriodMsg = new StringBuilder();
        // 将包含期间内的凭证数据进行过滤
        /*if(CollectionUtil.isNotEmpty(mzxPeriodList)){
            mzxPeriodList.forEach(mzxPeriod -> errPeriodMsg.append("明细账会计期间").append(mzxPeriod).append("以进入后续操作,凭证数据不再进行收集当前期间;"));
            List<String> filtervoucherId = voucherList.stream().filter(voucher -> mzxPeriodList.contains(CollectionUtil.getStringFromMap(voucher, "cperioddatelist")))
                    .map(voucher -> CollectionUtil.getStringFromMap(voucher, "cguid")).collect(Collectors.toList());
            voucherList = this.filterVoucherData(voucherList,filtervoucherId);
            voucherMetaDataList = this.filterVoucherData(voucherMetaDataList,filtervoucherId);
            voucherMetaSonDataList = this.filterVoucherData(voucherMetaSonDataList,filtervoucherId);
            vouandbillrelaList = this.filterVoucherData(vouandbillrelaList,filtervoucherId);
            voucherTypeList = this.filterVoucherData(voucherTypeList,filtervoucherId);
            voucherBillList = this.filterVoucherData(voucherBillList,filtervoucherId);
            errVoucherIds =  errVoucherIds.stream().filter(errVoucherid -> !filtervoucherId.contains(errVoucherid)).collect(Collectors.toList());
        }*/
        // 开启事务
        DbService dbService = voucherCollectUdpSql.getDb().getDbService();
        dbService.checkToStartTransaction();
        try {
            LOG.info("调用完整性校验");
            // 调用完整性校验
            try {
                integrityRuleCheckAction.integrityMatchingVoucher(voucherList,voucherMetaDataList,voucherMetaSonDataList,CollectionUtil.getStringFromMap(sourceBatchMap, "csourceorgnid"),sourceBatchMap);
            }catch (Exception ignored){}
            LOG.info("结束完整性校验");
            // 删除列表数据(包含跟这个凭证有关系的所以表)
            LOG.info("删除凭证列表数据"+errVoucherIds.size());
            voucherCollectUdpSql.deleteVoucherList(errVoucherIds);
            // 删除列表数据(包含跟这个明细账有关系的所以表)
            LOG.info("删除明细账列表数据"+errMxzListId.size());
            voucherCollectUdpSql.deleteMxzList(errMxzListId);
            LOG.info("删除明细账子表数据"+mxzMdzList.size());
            voucherCollectUdpSql.deleteList("da_fc_md_subsidiary_bookz",mxzMdzList);
            // 新增数据
            LOG.info("开始新增数据");
            voucherCollectUdpSql.addList("da_fc_voucherinfo",voucherList);
            voucherCollectUdpSql.addList("da_fc_md_voucher",voucherMetaDataList);
            voucherCollectUdpSql.addList("da_fc_md_voucherz",voucherMetaSonDataList);
            voucherCollectUdpSql.addList("da_fview_vouandbillrela",vouandbillrelaList);
            voucherCollectUdpSql.addList("da_fc_fap_vouchertype",voucherTypeList);
            voucherCollectUdpSql.addList("da_fc_billandvoucherrel",voucherBillList);
            voucherCollectUdpSql.addList("da_fc_billandvoucherrel_temp",voucherBillList);
            // 凭证错误数据
            voucherCollectUdpSql.addList("da_fc_voucherinfo_log",errVoucherList);
            // 明细账数据
            voucherCollectUdpSql.addList("da_fc_accountbook",mxzList);
            voucherCollectUdpSql.addList("da_fc_md_subsidiary_book",mxzMdList);
            voucherCollectUdpSql.addList("da_fc_md_subsidiary_bookz",mxzMdzList);
            LOG.info("结束新增数据");
            LOG.info("开始创建文件表");
            // 创建文件表
            List<String> fileNameList = voucherList.stream().map(voucherMap -> CollectionUtil.getStringFromMap(voucherMap, "cfiles_table_name")).distinct().collect(Collectors.toList());
            voucherCollectUdpSql.createFileTable(fileNameList);
            // 删除凭证的版式文件
            LOG.info("开始删除版式文件");
            voucherCollectUdpSql.deleteVoucherFile(errVoucherIds,fileNameList);
            dbService.commit();
        } catch (Exception e){
            LOG.info("添加凭证清单数据出现错误"+e.getMessage());
            dbService.rollback();
            throw new BusinessException(e.getMessage());
        }
        LOG.info("调用流水匹配凭证状态服务");
        // 凭证覆盖收集清除回单流水匹配凭证状态服务
        try {
            voucherCollectUdpSql.callUpdateMatchStatusMs(icover,CollectionUtil.getStringFromMap(sourceBatchMap, "cserialnumber"));
        }catch (Exception ignored){}
        LOG.info("结束流水匹配凭证状态服务");

        int size = voucherList.size();
        int size1 = errVoucherList.size();
        LOG.info("凭证清单收集完成--------");
        return "本次共收集"+(size+size1) + "条数据,成功 "+ size +"条数据,错误 "+size1 + "条数据。"+errMxzMsg+"其他失败记录请查看日志详情。";
    }

    /**
     * 保存到各个集合
     * @param mxzMap
     * @param mxzList
     * @param mxzMdList
     * @param mxzMdzList
     * @param mzxPeriodList
     */
    public String saveSubledger(Map<String, Object> mxzMap, List<Map> mxzList, List<Map> mxzMdList, List<Map> mxzMdzList, List<String> mzxPeriodList) {
        StringBuilder sb = new StringBuilder();
        mxzMap.forEach((key, value) -> {
            if(value instanceof Map) {
                Map<String,Object> mxzNewMap = (Map) value;
                String msg = CollectionUtil.getStringFromMap(mxzNewMap, "msg");
                if(StringUtil.isNotEmpty(msg)){
                    if(msg.contains("后续操作")){
                        sb.append("明细账会计期间").append(key).append("以进入后续操作,不再进行收集当前期间数据");
                        // mzxPeriodList.add(key);
                    }/*else if(msg.contains("手工上传")){
                        sb.append("明细账").append(key).append("期间,已通过手工上传或接口同步收集，不允许重复收集;");
                    }*/
                }/*else {
                    Map mdSubsidiary = (Map) mxzNewMap.get("md_subsidiary");
                    List<Map> mdSubsidiaryz = (List<Map>) mxzNewMap.get("md_subsidiaryz");
                    mxzList.add(mxzNewMap);
                    mxzMdList.add(mdSubsidiary);
                    mxzMdzList.addAll(mdSubsidiaryz);
                }*/
            }
        });
        return sb.toString();
    }


    /**
     * 动态获取预置字段数据
     * @param dataMap
     * @param detailClassByMainCodeList
     * @param busTypeInfoList
     * @param detailBusinessList
     * @param sourceBatch
     * @return
     */
    public  String getPresetDynamicData(Map<String,Object> dataMap,List<Map> detailClassByMainCodeList,
                                        List<Map> busTypeInfoList, List<Map> detailBusinessList,Map sourceBatch ){
        String extcode = CollectionUtil.getStringFromMap(sourceBatch, "extcode");
        String csourcesysname = CollectionUtil.getStringFromMap(sourceBatch, "csourcesysname");
        String browsePageId = "da_fc_voucherinfo_list";
        String browseTemplateId = "da_fc_voucherinfo_list_template";
        dataMap.put("cdatasource",extcode);
        dataMap.put("csourcecode",extcode);
        dataMap.put("csourcesysname",csourcesysname);
        dataMap.put("csourcename",csourcesysname);
        //组装明细分类、业务编码、名称、guid
        String cdetailclassname;
        //todo 是收集的凭证，固定为 记账凭证,接口未传输在此补齐
        cdetailclassname = "记账凭证";
        dataMap.put("creportname",cdetailclassname);
        dataMap.put("cdetailclassname",cdetailclassname);
        dataMap.put("cdetailclassguid_name",cdetailclassname);
        HashMap<Object, Object> temp = new HashMap<>();
        boolean rtnFlag = true;
        String rtnMsg = "";
        for (Map detailClassMap : detailClassByMainCodeList) {
            String cname = detailClassMap.get("cname").toString();
            Object cguid = detailClassMap.get("cguid");
            Object ccode = detailClassMap.get("ccode");
            if(cname.equals(cdetailclassname)){
                Object istatus = detailClassMap.get("istatus");
                if("0".equals(istatus)){//未启用
                    rtnMsg = "明细分类："+cdetailclassname+"未启用";
                    rtnFlag = false;
                    break;
                }
                dataMap.put("cdetailclassguid",cguid);
                dataMap.put("cdetailclasscode",ccode);
                break;
            }
            temp.put(cguid,ccode);
        }
        String cdetailclassguid = CollectionUtil.getStringFromMap(dataMap, "cdetailclassguid");
        //明细分类转换
        if(rtnFlag && StringUtils.isEmpty(cdetailclassguid)){
            boolean flag = false;
            if(CollectionUtil.isNotEmpty(detailBusinessList)){
                for (Map map1 : detailBusinessList) {
                    Object cmxnameguid_name = map1.get("cmxnameguid_name");
                    if(cdetailclassname.equals(cmxnameguid_name)){//是需要映射的
                        Object cmxnameguid = map1.get("cmxnameguid");
                        flag = true;
                        dataMap.put("cdetailclassguid",cmxnameguid);
                        dataMap.put("cdetailclasscode",temp.get(cmxnameguid));
                        break;
                    }
                }
            }
            if(!flag){
                rtnMsg = "明细分类："+cdetailclassname+"转换失败";
                rtnFlag = false;
            }
        }
        dataMap.put("cpageid",browsePageId);
        dataMap.put("ctemplateid",browseTemplateId);
        Object csourceorganid = sourceBatch.get("csourceorgnid");
        Object csourceorganname = sourceBatch.get("csourceorgnid_name");
        Object ccollectorid = sourceBatch.get("ccollector");
        Object ccollectorid_name = sourceBatch.get("ccollector_name");
        dataMap.put("ccollecttime", getSysTime());//收集时间（精确到秒）
        dataMap.put("ccollectdate", getSysDate());//收集日期 (查询条件 年月日)
        dataMap.put("cmanualrelstatus", "未关联");
        dataMap.put("imanualrelstatus", 0);
        dataMap.put("csourceorganid", csourceorganid);
        dataMap.put("csourceorganname", csourceorganname);
        dataMap.put("ccollectorid", ccollectorid);
        dataMap.put("ccollectorid_name",ccollectorid_name);
        //todo 是收集的凭证,验证和转换业务类型
        Object cvouchersource = dataMap.get("cvouchersource");
        dataMap.put("cbustypename",cvouchersource);
        boolean flag = false;
        HashMap<Object, Object> temp1 = new HashMap<>();
        for (Map busTypeInfoMap : busTypeInfoList) {
            Object cguid = busTypeInfoMap.get("cguid");
            Object ccode = busTypeInfoMap.get("ccode");
            Object cname = busTypeInfoMap.get("cname");
            if(cname.equals(cvouchersource)){
                flag = true;
                dataMap.put("cbustypeguid", cguid);
                dataMap.put("cbustypecode", ccode);
                break;
            }
            temp1.put(cguid,ccode);
        }
        if(!flag){
            if(CollectionUtil.isNotEmpty(detailBusinessList)){
                for (Map map1 : detailBusinessList) {
                    Object cservicetypename = map1.get("cservicetypename");
                    if(cvouchersource.equals(cservicetypename)){//是需要映射的
                        Object cfilestype = map1.get("cfilestype");
                        flag = true;
                        dataMap.put("cbustypeguid", cfilestype);
                        dataMap.put("cbustypecode", temp1.get(cfilestype));
                        break;
                    }
                }
                if(!flag){
                    rtnMsg = "凭证来源“"+cvouchersource+"”转换业务类型失败";
                }
            }
        }
        return rtnMsg;
    }

    /**
     * 时间
     * @return
     */
    public String  getSysTime(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time =s.format(date);
        return time;
    }

    /**
     * 日期
     * @return
     */
    public String  getSysDate(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd");
        String time =s.format(date);
        return time;
    }

    /**
     * 赋值数据库基本字段
     * @param map
     * @param sourceBatch
     * @return
     */
    public String makeDefalultValueForInterface(Map map,Map sourceBatch){
        Object corgnid = sourceBatch.get("corgnid");
        Object corgnid_name = sourceBatch.get("corgnid_name");
        Object cadminorgnid = sourceBatch.get("cadminorgnid");
        Object cadminorgnid_name = sourceBatch.get("cadminorgnid_name");
        Object ccreatedate = getSysTime();
        Object ccreatorid = sourceBatch.get("ccreatorid");
        Object ccreatorid_name = sourceBatch.get("ccreatorid_name");
        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate",ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);
        String g = Guid.g();
        map.put("cguid", g);
        return g;
    }

    /**
     * 获取凭证map
     * @return
     */
    public Map<String,Object> getVoucherMap(List<Map> value,Map<String,Object> sourceBatchMap,List<Map> detailClassByMainCodeList,
                                            List<Map> busTypeInfoList,List<Map> detailBusinessList,Map<String,String> userInfo){
        // 来源组织id
        String csourceorgnid = CollectionUtil.getStringFromMap(sourceBatchMap, "csourceorgnid");
        String cserialnumber = CollectionUtil.getStringFromMap(sourceBatchMap, "cserialnumber");
        Map<String, Object> dataMapNew = new HashMap<>(30);
        Map<String, Object> newRow = value.get(0);
        // 组织id
        //String bukrs = CollectionUtil.getStringFromMap(newRow, "BUKRS");
        dataMapNew.put("corgnid", csourceorgnid);
        dataMapNew.put("csourceorganid", csourceorgnid);
        //会计年度
        String gjahr = CollectionUtil.getStringFromMap(newRow, "GJAHR");
        dataMapNew.put("cyear", gjahr);
        dataMapNew.put("cfiles_table_name", "da_files_" + gjahr);
        //会计期间
        String monat = CollectionUtil.getStringFromMap(newRow, "MONAT");
        dataMapNew.put("cmonth", monat);
        if (monat.length() == 1) {
            //1-9
            //会计期间（查询2023-01）
            dataMapNew.put("cperioddate", gjahr + "-0" + monat);
            //会计期间(列表202301)
            dataMapNew.put("cperioddatelist", gjahr + "0" + monat);
            dataMapNew.put("cperiod", gjahr + "0" + monat);
        } else {//01 12
            //会计期间（查询2023-12）
            dataMapNew.put("cperioddate", gjahr + "-" + monat);
            //会计期间(列表202312)
            dataMapNew.put("cperioddatelist", gjahr + "" + monat);
            dataMapNew.put("cperiod", gjahr + "" + monat);
        }
        //会计凭证编号
        String belnr = CollectionUtil.getStringFromMap(newRow, "BELNR");
        //凭证类型名称
        String ltext = CollectionUtil.getStringFromMap(newRow, "LTEXT");
        dataMapNew.put("ivoucode", belnr);
        //凭证字号
        //dataMapNew.put("cvoucode",  belnr);
        dataMapNew.put("cvoucode",ltext+"-"+belnr);
        //凭证guid 财年+凭证编号
        dataMapNew.put("cvoucherid", gjahr+belnr);
        // 组织名称
        String butxt = CollectionUtil.getStringFromMap(newRow, "BUTXT");
        dataMapNew.put("corgnid_name", butxt);
        dataMapNew.put("csourceorganname", butxt);
        //凭证类型编码
        String blart = CollectionUtil.getStringFromMap(newRow, "BLART");
        dataMapNew.put("cvoutypecode", blart);
        //凭证类型名称
        dataMapNew.put("cvoutypename", ltext);
        //凭证来源
        dataMapNew.put("cvouchersource", "一汽丰田");
        //输入日期
        String cpudt = CollectionUtil.getStringFromMap(newRow, "CPUDT");
        //凭证日期
        dataMapNew.put("cvoucherdate", cpudt);
        //过账日期
        String budat = CollectionUtil.getStringFromMap(newRow, "BUDAT");
        //记账日期
        dataMapNew.put("cpostdate", budat);
        // 人员
        String usnam = CollectionUtil.getStringFromMap(newRow, "USNAM");
        dataMapNew.put("creviewer", usnam);//复核人
        dataMapNew.put("cauditorname", usnam);//审核人
        dataMapNew.put("cvoucreatorname", usnam);//制单人
        // 获取制单人id
        dataMapNew.put("ext_cvoucreatorid", userInfo.get(usnam));
        dataMapNew.put("cpostername", usnam);//记账人
        //凭证抬头文本
        String bktxt = CollectionUtil.getStringFromMap(newRow, "BKTXT");
        dataMapNew.put("csummary", bktxt);//摘要
        // 单据id，取参考凭证号
        dataMapNew.put("cbillid",CollectionUtil.getStringFromMap(newRow, "XBLNR"));
        // 档案字段
        dataMapNew.put("crelstatus","0");
        dataMapNew.put("crelstatus_name","未关联");
        dataMapNew.put("ieastatus","0");
        dataMapNew.put("ceastatus","收集成功");
        dataMapNew.put("centityfilestatus_name","收集成功");
        dataMapNew.put("centityfilestatus","0");
        dataMapNew.put("isuaistatus","1");
        dataMapNew.put("csuaistatus","已检测");
        dataMapNew.put("cbustypename", CollectionUtil.getStringFromMap(dataMapNew, "cvouchersource"));
        dataMapNew.put("cvoutypeguid", "");
        dataMapNew.put("cdetailclassguid_name", "记账凭证");
        dataMapNew.put("cdetailclassname", "记账凭证");
        // 凭证唯一标识 "csourceorganid,cvoucherdate,cvoutypename,ivoucode,cvoucode"
        dataMapNew.put("cbusinessprimarykeystr","!"+
                CollectionUtil.getStringFromMap(dataMapNew, "csourceorganid")+"!"+
                CollectionUtil.getStringFromMap(dataMapNew, "cvoucherdate")+"!"+
                CollectionUtil.getStringFromMap(dataMapNew, "cvoutypename")+"!"+
                CollectionUtil.getStringFromMap(dataMapNew, "ivoucode")+"!"+
                CollectionUtil.getStringFromMap(dataMapNew, "cvoucode"));
        //附单数
        dataMapNew.put("iaffix", "0");
        dataMapNew.put("isexistfile", 0);
        dataMapNew.put("cnumberoffiles", "0");
        dataMapNew.put("cserialnumber", cserialnumber);
        String ivoucode = CollectionUtil.getStringFromMap(dataMapNew, "ivoucode");
        if (StringUtils.isNotBlank(ivoucode)) {
            if (ivoucode.length() > 3) {
                String substring = ivoucode.substring(0, 3);
                dataMapNew.put("ext_cvouchercoderange", substring);
                if ("110".equals(substring) || "130".equals(substring) || "800".equals(substring) ||"720".equals(substring) || "150".equals(substring) || "160".equals(substring) || "590".equals(substring)) {
                    dataMapNew.put("crelstatus_name", "已关联");
                    dataMapNew.put("crelstatus", "1");
                }
            } else if (ivoucode.length() == 3) {
                dataMapNew.put("ext_cvouchercoderange", ivoucode);
                if ("110".equals(ivoucode) || "130".equals(ivoucode) || "800".equals(ivoucode) ||"720".equals(ivoucode) || "150".equals(ivoucode) || "160".equals(ivoucode) || "590".equals(ivoucode)) {
                    dataMapNew.put("crelstatus_name", "已关联");
                    dataMapNew.put("crelstatus", "1");
                }
            }
        }
        // 动态获取预置数据，明细分类，业务类型等;
        String presetDynamicDataStr = this.getPresetDynamicData(dataMapNew, detailClassByMainCodeList, busTypeInfoList, detailBusinessList, sourceBatchMap);
        if(StringUtil.isNotEmpty(presetDynamicDataStr)){
            throw new BusinessException(presetDynamicDataStr);
        }
        // 获取平台表预置字段，不可使用session,定时任务会进行调用
        this.makeDefalultValueForInterface(dataMapNew,sourceBatchMap);
        String clistguid = CollectionUtil.getStringFromMap(dataMapNew, "cguid");
        dataMapNew.put("clistguid", clistguid);

        // 凭证列表组装完成

        // ****开始元数据组装
        Map<String, Object> metaDataMap = new HashMap<>(dataMapNew);
        this.makeDefalultValueForInterface(metaDataMap,sourceBatchMap);
        String cheadid = CollectionUtil.getStringFromMap(metaDataMap, "cguid");
        // 元数据保存列表id
        metaDataMap.put("clistguid", clistguid);

        // ****结束元数据组装
        // ****开始元数据子表组装
        List<Map> metaDataSubunitList = new ArrayList<>();
        BigDecimal idebitamtSum = new BigDecimal(0);
        BigDecimal icreditamt = new BigDecimal(0);
        for (Map metasonMap : value) {
            Map<String, Object> metaDataSubMap = new HashMap<>(dataMapNew);
            this.makeDefalultValueForInterface(metaDataSubMap,sourceBatchMap);
            metaDataSubMap.put("clistguid", clistguid);
            metaDataSubMap.put("cheadid", cheadid);
            //借贷 S借方,H贷方
            String shkzg = CollectionUtil.getStringFromMap(metasonMap, "SHKZG");
            //本位币金额（+/-)
            String zdmbtr = CollectionUtil.getStringFromMap(metasonMap, "ZDMBTR").replace(",","");
            //S借方
            if ("S".equals(shkzg)) {
                //借方金额
                metaDataSubMap.put("idebitamt", zdmbtr);
                idebitamtSum = idebitamtSum.add(new BigDecimal(zdmbtr));
                //H贷方
            } else if ("H".equals(shkzg)) {
                //贷方金额
                metaDataSubMap.put("icreditamt", zdmbtr);
                icreditamt = icreditamt.add(new BigDecimal(zdmbtr));
            }
            //货币
            String waers = CollectionUtil.getStringFromMap(metasonMap, "WAERS");
            //币种
            metaDataSubMap.put("ccurname", waers);
            // 凭证货币金额（+/-)。凭证货币金额的绝对值
            String zwrbtr = CollectionUtil.getStringFromMap(metasonMap, "ZWRBTR");
            //原币
            metaDataSubMap.put("iamt_f", zwrbtr);
            // 数量
            String menge = CollectionUtil.getStringFromMap(metasonMap, "MENGE");
            metaDataSubMap.put("iqty", menge);
            // 科目描述
            String txt50 = CollectionUtil.getStringFromMap(metasonMap, "TXT50");
            metaDataSubMap.put("cacctstr", txt50);
            // 摘要
            String sgtxt = CollectionUtil.getStringFromMap(metasonMap, "SGTXT");
            metaDataSubMap.put("csummary", sgtxt);
            // 一汽特有字段
            metaDataSubMap.put("ext_bukrs", CollectionUtil.getStringFromMap(metasonMap, "BUKRS"));
            metaDataSubMap.put("ext_gjahr", CollectionUtil.getStringFromMap(metasonMap, "GJAHR"));
            metaDataSubMap.put("ext_belnr", CollectionUtil.getStringFromMap(metasonMap, "BELNR"));
            metaDataSubMap.put("ext_buzei", CollectionUtil.getStringFromMap(metasonMap, "BUZEI"));
            metaDataSubMap.put("ext_butxt", CollectionUtil.getStringFromMap(metasonMap, "BUTXT"));
            metaDataSubMap.put("ext_monat", CollectionUtil.getStringFromMap(metasonMap, "MONAT"));
            metaDataSubMap.put("ext_blart", CollectionUtil.getStringFromMap(metasonMap, "BLART"));
            metaDataSubMap.put("ext_ltext", CollectionUtil.getStringFromMap(metasonMap, "LTEXT"));
            metaDataSubMap.put("ext_tcode", CollectionUtil.getStringFromMap(metasonMap, "TCODE"));
            metaDataSubMap.put("ext_cpudt", CollectionUtil.getStringFromMap(metasonMap, "CPUDT"));
            metaDataSubMap.put("ext_cputm", CollectionUtil.getStringFromMap(metasonMap, "CPUTM"));
            metaDataSubMap.put("ext_augdt", CollectionUtil.getStringFromMap(metasonMap, "AUGDT"));
            metaDataSubMap.put("ext_zfbdt", CollectionUtil.getStringFromMap(metasonMap, "ZFBDT"));
            metaDataSubMap.put("ext_fdtag", CollectionUtil.getStringFromMap(metasonMap, "FDTAG"));
            metaDataSubMap.put("ext_bldat", CollectionUtil.getStringFromMap(metasonMap, "BLDAT"));
            metaDataSubMap.put("ext_waers", CollectionUtil.getStringFromMap(metasonMap, "WAERS"));
            metaDataSubMap.put("ext_budat", CollectionUtil.getStringFromMap(metasonMap, "BUDAT"));
            metaDataSubMap.put("ext_fipos", CollectionUtil.getStringFromMap(metasonMap, "FIPOS"));
            metaDataSubMap.put("ext_usnam", CollectionUtil.getStringFromMap(metasonMap, "USNAM"));
            metaDataSubMap.put("ext_bktxt", CollectionUtil.getStringFromMap(metasonMap, "BKTXT"));
            metaDataSubMap.put("ext_xblnr", CollectionUtil.getStringFromMap(metasonMap, "XBLNR"));
            metaDataSubMap.put("ext_sgtxt", CollectionUtil.getStringFromMap(metasonMap, "SGTXT"));
            metaDataSubMap.put("ext_zuonr", CollectionUtil.getStringFromMap(metasonMap, "ZUONR"));
            metaDataSubMap.put("ext_hkont", CollectionUtil.getStringFromMap(metasonMap, "HKONT"));
            metaDataSubMap.put("ext_txt50", CollectionUtil.getStringFromMap(metasonMap, "TXT50"));
            metaDataSubMap.put("ext_bschl", CollectionUtil.getStringFromMap(metasonMap, "BSCHL"));
            metaDataSubMap.put("ext_shkzg", CollectionUtil.getStringFromMap(metasonMap, "SHKZG"));
            metaDataSubMap.put("ext_zshkzg", CollectionUtil.getStringFromMap(metasonMap, "ZSHKZG"));
            metaDataSubMap.put("ext_xnegp", CollectionUtil.getStringFromMap(metasonMap, "XNEGP"));
            metaDataSubMap.put("ext_umskz", CollectionUtil.getStringFromMap(metasonMap, "UMSKZ"));
            metaDataSubMap.put("ext_ltext1", CollectionUtil.getStringFromMap(metasonMap, "LTEXT1"));
            String zwrbtr1 = CollectionUtil.getStringFromMap(metasonMap, "ZWRBTR").replace(",","");
            metaDataSubMap.put("ext_zwrbtr",new BigDecimal(zwrbtr1));
            String zdmbtr1 = CollectionUtil.getStringFromMap(metasonMap, "ZDMBTR").replace(",","");
            metaDataSubMap.put("ext_zdmbtr", new BigDecimal(zdmbtr1));
            metaDataSubMap.put("ext_kostl", CollectionUtil.getStringFromMap(metasonMap, "KOSTL"));
            metaDataSubMap.put("ext_ktext", CollectionUtil.getStringFromMap(metasonMap, "KTEXT"));
            metaDataSubMap.put("ext_prctr", CollectionUtil.getStringFromMap(metasonMap, "PRCTR"));
            metaDataSubMap.put("ext_ktext2", CollectionUtil.getStringFromMap(metasonMap, "KTEXT2"));
            metaDataSubMap.put("ext_fkber", CollectionUtil.getStringFromMap(metasonMap, "FKBER"));
            metaDataSubMap.put("ext_fkbtx", CollectionUtil.getStringFromMap(metasonMap, "FKBTX"));
            metaDataSubMap.put("ext_rstgr", CollectionUtil.getStringFromMap(metasonMap, "RSTGR"));
            metaDataSubMap.put("ext_txt20", CollectionUtil.getStringFromMap(metasonMap, "TXT20"));
            metaDataSubMap.put("ext_kunnr", CollectionUtil.getStringFromMap(metasonMap, "KUNNR"));
            metaDataSubMap.put("ext_name1", CollectionUtil.getStringFromMap(metasonMap, "NAME1"));
            metaDataSubMap.put("ext_lifnr", CollectionUtil.getStringFromMap(metasonMap, "LIFNR"));
            metaDataSubMap.put("ext_name2", CollectionUtil.getStringFromMap(metasonMap, "NAME2"));
            metaDataSubMap.put("ext_menge", CollectionUtil.getStringFromMap(metasonMap, "MENGE"));
            metaDataSubMap.put("ext_matnr", CollectionUtil.getStringFromMap(metasonMap, "MATNR"));
            metaDataSubMap.put("ext_maktx", CollectionUtil.getStringFromMap(metasonMap, "MAKTX"));
            metaDataSubMap.put("ext_mtart", CollectionUtil.getStringFromMap(metasonMap, "MTART"));
            metaDataSubMap.put("ext_mtbez", CollectionUtil.getStringFromMap(metasonMap, "MTBEZ"));
            metaDataSubMap.put("ext_bwtar", CollectionUtil.getStringFromMap(metasonMap, "BWTAR"));
            metaDataSubMap.put("ext_gsber", CollectionUtil.getStringFromMap(metasonMap, "GSBER"));
            metaDataSubMap.put("ext_gtext", CollectionUtil.getStringFromMap(metasonMap, "GTEXT"));
            metaDataSubMap.put("ext_ebeln", CollectionUtil.getStringFromMap(metasonMap, "EBELN"));
            metaDataSubMap.put("ext_stblg", CollectionUtil.getStringFromMap(metasonMap, "STBLG"));
            metaDataSubMap.put("ext_idxsp", CollectionUtil.getStringFromMap(metasonMap, "IDXSP"));
            metaDataSubMap.put("ext_j_1aindtxt", CollectionUtil.getStringFromMap(metasonMap, "J_1AINDTXT"));
            metaDataSubMap.put("ext_augbl", CollectionUtil.getStringFromMap(metasonMap, "AUGBL"));
            metaDataSubMap.put("ext_anln1", CollectionUtil.getStringFromMap(metasonMap, "ANLN1"));
            metaDataSubMap.put("ext_lokkt", CollectionUtil.getStringFromMap(metasonMap, "LOKKT"));
            metaDataSubMap.put("ext_fistl", CollectionUtil.getStringFromMap(metasonMap, "FISTL"));
            metaDataSubMap.put("ext_kblnr", CollectionUtil.getStringFromMap(metasonMap, "KBLNR"));
            metaDataSubMap.put("ext_kblpos", CollectionUtil.getStringFromMap(metasonMap, "KBLPOS"));
            metaDataSubMap.put("ext_zz001", CollectionUtil.getStringFromMap(metasonMap, "ZZ001"));
            metaDataSubMap.put("ext_zz001_txt", CollectionUtil.getStringFromMap(metasonMap, "ZZ001_TXT"));
            metaDataSubMap.put("ext_zz002", CollectionUtil.getStringFromMap(metasonMap, "ZZ002"));
            metaDataSubMap.put("ext_zz002_txt", CollectionUtil.getStringFromMap(metasonMap, "ZZ002_TXT"));
            metaDataSubMap.put("ext_zz003", CollectionUtil.getStringFromMap(metasonMap, "ZZ003"));
            metaDataSubMap.put("ext_zz003_txt", CollectionUtil.getStringFromMap(metasonMap, "ZZ003_TXT"));
            metaDataSubMap.put("ext_zz004", CollectionUtil.getStringFromMap(metasonMap, "ZZ004"));
            metaDataSubMap.put("ext_zz005", CollectionUtil.getStringFromMap(metasonMap, "ZZ005"));
            metaDataSubMap.put("ext_zz006", CollectionUtil.getStringFromMap(metasonMap, "ZZ006"));
            metaDataSubMap.put("ext_zz007", CollectionUtil.getStringFromMap(metasonMap, "ZZ007"));
            metaDataSubMap.put("ext_zz009", CollectionUtil.getStringFromMap(metasonMap, "ZZ009"));
            metaDataSubMap.put("ext_zz009_txt", CollectionUtil.getStringFromMap(metasonMap, "ZZ009_TXT"));
            metaDataSubMap.put("ext_zz010", CollectionUtil.getStringFromMap(metasonMap, "ZZ010"));
            metaDataSubMap.put("ext_zz008", CollectionUtil.getStringFromMap(metasonMap, "ZZ008"));
            metaDataSubMap.put("ext_zz011", CollectionUtil.getStringFromMap(metasonMap, "ZZ011"));
            metaDataSubMap.put("ext_xref1", CollectionUtil.getStringFromMap(metasonMap, "XREF1"));
            metaDataSubMap.put("ext_xref2", CollectionUtil.getStringFromMap(metasonMap, "XREF2"));
            metaDataSubMap.put("ext_xref3", CollectionUtil.getStringFromMap(metasonMap, "XREF3"));
            metaDataSubMap.put("ext_zzysrq", CollectionUtil.getStringFromMap(metasonMap, "ZZYSRQ"));

            metaDataSubunitList.add(metaDataSubMap);
        }
        dataMapNew.put("idebittotal", idebitamtSum);
        dataMapNew.put("icredittotal", icreditamt);
        metaDataMap.put("idebittotal", idebitamtSum);
        metaDataMap.put("icredittotal", icreditamt);
        // 将元数据map和元数据子表数据保持到列表上;
        dataMapNew.put("md_voucher",metaDataMap);
        dataMapNew.put("md_voucherz",metaDataSubunitList);
        return dataMapNew;
    }


    /**
     * 凭证元数据校验
     * @param dataMap
     */
    public String validateMetaDate(Map<String, Object> dataMap) {
        // todo 元数据如何校验
        // 元数据主表校验
        String cvoucode = CollectionUtil.getStringFromMap(dataMap, "cvoucode");
        if(StringUtil.isEmpty(cvoucode)){
            return "[凭证字号]不能为空";
        }
        String cvoucreatorname = CollectionUtil.getStringFromMap(dataMap, "cvoucreatorname");
        if(StringUtil.isEmpty(cvoucreatorname)){
            return "[制单人名称]不能为空";
        }
        String cyear = CollectionUtil.getStringFromMap(dataMap, "cyear");
        if(StringUtil.isEmpty(cyear)){
            return "[会计年]不能为空";
        }
        String cmonth = CollectionUtil.getStringFromMap(dataMap, "cmonth");
        if(StringUtil.isEmpty(cmonth)){
            return "[会计月]不能为空";
        }
        String cvoutypename = CollectionUtil.getStringFromMap(dataMap, "cvoutypename");
        if(StringUtil.isEmpty(cvoutypename)){
            return "[凭证类型名称]不能为空";
        }
        String cvoutypecode = CollectionUtil.getStringFromMap(dataMap, "cvoutypecode");
        if(StringUtil.isEmpty(cvoutypecode)){
            return "[凭证类型编码]不能为空";
        }
        String ivoucode = CollectionUtil.getStringFromMap(dataMap, "ivoucode");
        if(StringUtil.isEmpty(ivoucode)){
            return "[凭证号]不能为空";
        }
        String cvoucherid = CollectionUtil.getStringFromMap(dataMap, "cvoucherid");
        if(StringUtil.isEmpty(cvoucherid)){
            return "[凭证Id]不能为空";
        }
        String cvoucherdate = CollectionUtil.getStringFromMap(dataMap, "cvoucherdate");
        if(StringUtil.isEmpty(cvoucherdate)){
            return "[凭证日期]不能为空";
        }
        String cpostername = CollectionUtil.getStringFromMap(dataMap, "cpostername");
        if(StringUtil.isEmpty(cpostername)){
            return "[记账人]不能为空";
        }
        return  "";
    }

    /**
     * 凭证是否覆盖收集
     */
    public String isOverlayCollection(String is,Map<String,Object> dataMap,List<Map> oldList,List<String> errListId){
        String cbusinessprimarykeystr = CollectionUtil.getStringFromMap(dataMap, "cbusinessprimarykeystr");
        if(CollectionUtil.isNotEmpty(oldList)){
            Optional<Map> oldMapOpt = oldList.stream().filter(map -> cbusinessprimarykeystr.equals(CollectionUtil.getStringFromMap(map, "cbusinessprimarykeystr"))).findFirst();
            if(oldMapOpt.isPresent()){
                Map<String,Object> oldMap = (Map) oldMapOpt.get();
                // 覆盖收集并且列表存在重复数据
                if("1".equals(is)){
                    // 判断列表数据是否进入后续流程
                    String ieastatus = CollectionUtil.getStringFromMap(oldMap, "ieastatus");
                    String centityfilestatus = CollectionUtil.getStringFromMap(oldMap, "centityfilestatus");
                    // 只要电子和实体任意一个不是收集成功，则不允许覆盖收集
                    // 否则则允许覆盖收集，删除列表数据，把列表id记录到错误的集合当中统一删除
                    if(!"0".equals(ieastatus) || !"0".equals(centityfilestatus)){
                        dataMap.put("msg","该条记录已进行后续操作，不允许重复收集");
                        dataMap.put("csyncexceptionmsg","该条记录已进行后续操作，不允许重复收集");
                        return "该条记录已进行后续操作，不允许重复收集";
                    }else {
                        String cguid = CollectionUtil.getStringFromMap(oldMap, "cguid");
                        errListId.add(cguid);
                    }
                }else {
                    dataMap.put("msg", "该记录已进行收集，不允许重复收集");
                    dataMap.put("csyncexceptionmsg", "该记录已进行收集，不允许重复收集");
                    return "该记录已进行收集，不允许重复收集";
                }
            }
        }
        return "";
    }


    /**
     * 账簿明细账是否覆盖收集
     */
    public void isOverlayCollectionZb(String is,Map<String,Object> mxzMap,Map<String,Object> sourceBatchMap,List<Map> oldList,
                                      List<String> errListId,List<Map> mxzList,List<Map> mdMxzList,
                                      List<Map> mdzMxzList,Map<String,List<Map>> groupByMzx){

        Collection<Object> values = mxzMap.values();
        for (Object value : values) {
            if(value instanceof Map){
                Map<String,Object> dataMap = (Map) value;
                String cperioddatelist = CollectionUtil.getStringFromMap(dataMap, "cperioddatelist");
                if(CollectionUtil.isNotEmpty(oldList)){
                    Optional<Map> oldMapOpt = oldList.stream().filter(map -> cperioddatelist.equals(CollectionUtil.getStringFromMap(map, "cperioddatelist"))).findFirst();
                    if(oldMapOpt.isPresent()){
                        Map<String,Object> oldMap = (Map) oldMapOpt.get();
                        String cdatasource = CollectionUtil.getStringFromMap(oldMap, "cdatasource");
                        // 覆盖收集并且列表存在重复数据
                        if("1".equals(is)){
                            // 判断列表数据是否进入后续流程
                            int ieastatus = CollectionUtil.getIntFromMap(oldMap, "ieastatus");
                            int centityfilestatus = CollectionUtil.getIntFromMap(oldMap, "centityfilestatus");
                            // 只要电子和实体任意一个不是收集成功，则不允把许覆盖收集
                            // 否则则允许覆盖收集，删除列表数据，列表id记录到错误的集合当中统一删除
                            if(ieastatus!=0 || centityfilestatus!= 0){
                                dataMap.put("msg","该条记录已进行后续操作，不允许重复收集");
                                dataMap.put("csyncexceptionmsg","该条记录已进行后续操作，不允许重复收集");
                            }/*else {
                                String cguid = CollectionUtil.getStringFromMap(oldMap, "cguid");
                                errListId.add(cguid);
                            }*/
                        }else {
                            if(StringUtil.isEmpty(cdatasource)){
                                String cguid = CollectionUtil.getStringFromMap(oldMap, "cguid");
                                errListId.add(cguid);
                            }else {
                                dataMap.put("msg", "该条记录已通过手工上传或接口同步收集，不允许重复收集");
                                dataMap.put("csyncexceptionmsg", "该条记录已通过手工上传或接口同步收集，不允许重复收集");
                            }
                        }
                    }
                }
            }
        }

        for (Map.Entry<String, Object> newMxzMap : mxzMap.entrySet()) {
            String cperioddatelist = newMxzMap.getKey();
            Map mxzvalue = (Map) newMxzMap.getValue();
            Optional<Map> oldMapOpt = oldList.stream().filter(map -> cperioddatelist.equals(CollectionUtil.getStringFromMap(map, "cperioddatelist"))).findFirst();
            Map<String,Object> oldmap = new HashMap<>();
            if(oldMapOpt.isPresent()){
                oldmap = oldMapOpt.get();
            }
            String cdatasource = CollectionUtil.getStringFromMap(oldmap, "cdatasource");
            String mxzmsg = CollectionUtil.getStringFromMap(mxzvalue, "msg") == null ? "":CollectionUtil.getStringFromMap(mxzvalue, "msg");
            if(StringUtil.isNotEmpty(cdatasource)){
                if(!mxzmsg.contains("后续操作")){
                    Map<String, Object> map1 = voucherCollectUdpSql.getMdsubsidiaryMap(oldmap.get("cguid") + "");
                    this.getMdzSubsidiaryMap(groupByMzx,sourceBatchMap,oldmap.get("cguid")+"",
                            map1.get("cguid")+"",cperioddatelist,mdzMxzList);
                }
            }else {
                // 不存在
                Map<String,Object> subsidiaryMap = new HashMap<>(mxzvalue);
                this.makeDefalultValueForInterface(subsidiaryMap,sourceBatchMap);
                List<Map> detailClassByMainCodeList = collectDataDaoInterface.queryDetailClassByMainCode(StaticCodeClass.book);
                Optional<Map> codeMapOpt = detailClassByMainCodeList.stream().filter(detaMap -> "ZB02".equals(CollectionUtil.getStringFromMap(detaMap, "ccode"))).findFirst();
                if(codeMapOpt.isPresent()){
                    Map<String,Object> codeMap = codeMapOpt.get();
                    String cdetailclassname = CollectionUtil.getStringFromMap(codeMap, "cname");
                    String cdetailclasscode = CollectionUtil.getStringFromMap(codeMap, "ccode");
                    String cguid = CollectionUtil.getStringFromMap(codeMap, "cguid");
                    String istatus = CollectionUtil.getStringFromMap(codeMap, "istatus");
                    if("0".equals(istatus)){
                        throw new BusinessException("明细分类："+cdetailclassname+"未启用");
                    }
                    subsidiaryMap.put("cdetailclasscode", cdetailclasscode);
                    subsidiaryMap.put("cdetailclassname", cdetailclassname);
                    subsidiaryMap.put("creportname", cdetailclassname);
                    subsidiaryMap.put("cdetailclassguid_name", cdetailclassname);
                    subsidiaryMap.put("cdetailclassguid", cguid);
                }
                // 会计账簿唯一标识
                subsidiaryMap.put("cbusinessprimarykeystr",
                        "!"+CollectionUtil.getStringFromMap(subsidiaryMap,"csourceorganid")+"!"+
                                CollectionUtil.getStringFromMap(subsidiaryMap,"cperioddatelist")+"!"+
                                CollectionUtil.getStringFromMap(subsidiaryMap,"cdetailclasscode"));
                // 凭证元数据改为账簿元数据，列表id需统一
                Map<String,Object> mdSubsidiaryMap = new HashMap<>((Map) subsidiaryMap.get("md_voucher"));
                mdSubsidiaryMap.put("clistguid",subsidiaryMap.get("cguid"));
                mdSubsidiaryMap.put("cpageid","da_fc_accountbook_list");
                mdSubsidiaryMap.put("ctemplateid","da_fc_accountbook_list_template");
                this.makeDefalultValueForInterface(mdSubsidiaryMap,sourceBatchMap);
                mxzList.add(subsidiaryMap);
                mdMxzList.add(mdSubsidiaryMap);
                this.getMdzSubsidiaryMap(groupByMzx,sourceBatchMap,subsidiaryMap.get("cguid")+"",
                        mdSubsidiaryMap.get("cguid")+"",cperioddatelist,mdzMxzList);
            }
        }

    }


    public void getMdzSubsidiaryMap(Map<String,List<Map>> groupByMzx, Map<String,Object> sourceBatchMap,
                                    String clistguid,String cheadid,String cperioddatelist,List<Map> mdzSubsidiaryMap){
        List<Map> maps = groupByMzx.get(cperioddatelist);
        if(CollectionUtil.isNotEmpty(maps)){
            List<Map<String, Object>> collect = maps.stream().map(mdSubsidiary -> {
                Map<String, Object> hashMap = new HashMap<>(mdSubsidiary);
                this.makeDefalultValueForInterface(hashMap, sourceBatchMap);
                hashMap.put("clistguid", clistguid);
                hashMap.put("cheadid", cheadid);
                hashMap.put("cpageid", "da_fc_accountbook_list");
                hashMap.put("ctemplateid", "da_fc_accountbook_list_template");
                // todo 以下字段如何确定 ？？
                hashMap.put("cbalancedirsel", mdSubsidiary.get("cbalancedirsel"));
                hashMap.put("ibalanceamt", mdSubsidiary.get("ibalanceamt"));
                return hashMap;
            }).collect(Collectors.toList());
            mdzSubsidiaryMap.addAll(collect);
        }
    }


    /**
     * 转化资料检索查看数据
     * @param dataMap
     * @param sourceBatchMap
     * @return
     */
    public Map<String,Object> getVouandbillrelaMap(Map<String,Object> dataMap,Map<String,Object> sourceBatchMap){
        Map<String,Object> map = new HashMap<>(20);
        this.makeDefalultValueForInterface(map,sourceBatchMap);
        map.put("cvoucherid",CollectionUtil.getStringFromMap(dataMap,"cvoucherid"));
        map.put("ctype","0");
        map.put("cmeansid",CollectionUtil.getStringFromMap(dataMap,"cvoucherid"));
        map.put("cmeanscode",CollectionUtil.getStringFromMap(dataMap,"cvoucode"));
        map.put("cmeansname",CollectionUtil.getStringFromMap(dataMap,"cvoutypename"));
        map.put("cparent","000000");
        map.put("cfiletablename",CollectionUtil.getStringFromMap(dataMap,"cfiles_table_name"));
        map.put("clistguid",CollectionUtil.getStringFromMap(dataMap,"clistguid"));
        map.put("pageurl","page/commonvchr/da_fin_md_voucher/da_fin_md_voucher/"+CollectionUtil.getStringFromMap(dataMap,"cguid")+"?origin=1&singleTab=1&cstate=view&name=记账凭证");
        return map;
    }

    /**
     * 转换凭证类型表数据
     * @param dataMap
     * @param sourceBatchMap
     * @param typeList
     * @return
     */
    public Map<String,Object> getVoucherTypeMap(Map<String,Object> dataMap,Map<String,Object> sourceBatchMap,List<Map> typeList){
        // 保证凭证类型唯一
        String cvoutypename = CollectionUtil.getStringFromMap(dataMap, "cvoutypename");
        if (CollectionUtil.isNotEmpty(typeList)) {
            List<String> typeNameList = typeList.stream().map(typeMap -> CollectionUtil.getStringFromMap(typeMap, "cname")).collect(Collectors.toList());
            if (typeNameList.contains(cvoutypename)) {
                return null;
            }
        }
        Map<String,Object> map = new HashMap<>(20);
        this.makeDefalultValueForInterface(map,sourceBatchMap);
        // todo 对方系统没传凭证类型id
        map.put("cvoutypeguid","");
        map.put("clistguid",CollectionUtil.getStringFromMap(dataMap,"cguid"));
        map.put("cdatasource",CollectionUtil.getStringFromMap(dataMap,"cdatasource"));
        map.put("csourceorganid",CollectionUtil.getStringFromMap(dataMap,"csourceorganid"));
        map.put("csourceorganname",CollectionUtil.getStringFromMap(dataMap,"csourceorganname"));
        map.put("ccode",CollectionUtil.getStringFromMap(dataMap,"cvoutypecode"));
        map.put("cname",cvoutypename);
        return map;
    }

    /**
     * 转换凭证和单据表数据
     * @param dataMap
     * @param sourceBatchMap
     * @return
     */
    public Map<String,Object>  getVoucherBillMap(Map<String,Object> dataMap,Map<String,Object> sourceBatchMap){
        Map<String,Object> map = new HashMap<>(20);
        this.makeDefalultValueForInterface(map,sourceBatchMap);
        // todo 对方系统没传
        // 单据id
        map.put("csourceorganid",CollectionUtil.getStringFromMap(dataMap,"csourceorganid"));
        map.put("csourceorganname",CollectionUtil.getStringFromMap(dataMap,"csourceorganname"));
        map.put("cdatasource",CollectionUtil.getStringFromMap(dataMap,"cdatasource"));
        map.put("cbillid",CollectionUtil.getStringFromMap(dataMap,"cbillid"));
        map.put("cvoucherid",CollectionUtil.getStringFromMap(dataMap,"cvoucherid"));
        map.put("cvoucode",CollectionUtil.getStringFromMap(dataMap,"cvoucode"));
        map.put("clistguid",CollectionUtil.getStringFromMap(dataMap,"cguid"));
        // todo 以下字段如何确定？？？
        map.put("cbillcode",CollectionUtil.getStringFromMap(dataMap,"cbillcode"));
        map.put("creason",CollectionUtil.getStringFromMap(dataMap,"creason"));
        return map;
    }

    public void transformCollectionLogs(Map<String,Object> map,Map<String,Object> sourceBatchMap){
        // 添加收集日志需要的字段
        map.put("csourcebatchid", CollectionUtil.getStringFromMap(sourceBatchMap,"cguid"));
        map.put("cserialnumber", CollectionUtil.getStringFromMap(sourceBatchMap,"cserialnumber"));
        map.put("ctransactionnum", CollectionUtil.getStringFromMap(sourceBatchMap,"ctransactionnum"));
    }


    /**
     * 转换明细账数据
     * @param voucherMap
     * @param sourceBatchMap
     * @return
     */
    public Map<String,Object> getSubsidiaryMap(Map<String,Object> voucherMap,Map<String,Object> sourceBatchMap,Map<String,Object> mxzMap){
        Map<String,Object> subsidiaryMap = new HashMap<>(voucherMap);
        subsidiaryMap.put("cpageid","da_fc_accountbook_list");
        subsidiaryMap.put("ctemplateid","da_fc_accountbook_list_template");
        this.makeDefalultValueForInterface(subsidiaryMap,sourceBatchMap);
        String period = CollectionUtil.getStringFromMap(subsidiaryMap,"cperioddatelist");
        Object mxzObj = mxzMap.get(period);
        if(ObjectUtil.isNotEmpty(mxzObj) && mxzObj instanceof Map){
            // 如果明细账月度存在，动态将元数据子表获取，并保存到一个集合
            Map<String, Object> mxzOldMap = (Map<String, Object>) mxzObj;
            Map<String, Object> mdMxzOldMap = (Map<String, Object>)mxzOldMap.get("md_subsidiary");
            List<Map> mdzMxzOldList = (List<Map>) mxzOldMap.get("md_subsidiaryz");
            // 获取明细账的元数据主子表
            List<Map> mdSubsidiaryList = new ArrayList<>((List<Map>)subsidiaryMap.get("md_voucherz"));
            mdSubsidiaryList.forEach(mdSubsidiary -> {
                Map<String,Object> hashMap = new HashMap<>(mdSubsidiary);
                this.makeDefalultValueForInterface(hashMap,sourceBatchMap);
                hashMap.put("clistguid",mxzOldMap.get("cguid"));
                hashMap.put("cheadid",mdMxzOldMap.get("cguid"));
                hashMap.put("cpageid","da_fc_accountbook_list");
                hashMap.put("ctemplateid","da_fc_accountbook_list_template");
                // todo 以下字段如何确定 ？？
                hashMap.put("cbalancedirsel",mdSubsidiary.get("cbalancedirsel"));
                hashMap.put("ibalanceamt",mdSubsidiary.get("ibalanceamt"));
                mdzMxzOldList.add(hashMap);
            });
            return mxzOldMap;
        }else {
            this.makeDefalultValueForInterface(subsidiaryMap,sourceBatchMap);
            List<Map> detailClassByMainCodeList = collectDataDaoInterface.queryDetailClassByMainCode(StaticCodeClass.book);
            Optional<Map> codeMapOpt = detailClassByMainCodeList.stream().filter(detaMap -> "ZB02".equals(CollectionUtil.getStringFromMap(detaMap, "ccode"))).findFirst();
            if(codeMapOpt.isPresent()){
                Map<String,Object> codeMap = codeMapOpt.get();
                String cdetailclassname = CollectionUtil.getStringFromMap(codeMap, "cname");
                String cdetailclasscode = CollectionUtil.getStringFromMap(codeMap, "ccode");
                String cguid = CollectionUtil.getStringFromMap(codeMap, "cguid");
                String istatus = CollectionUtil.getStringFromMap(codeMap, "istatus");
                if("0".equals(istatus)){
                    throw new BusinessException("明细分类："+cdetailclassname+"未启用");
                }
                subsidiaryMap.put("cdetailclasscode", cdetailclasscode);
                subsidiaryMap.put("cdetailclassname", cdetailclassname);
                subsidiaryMap.put("creportname", cdetailclassname);
                subsidiaryMap.put("cdetailclassguid_name", cdetailclassname);
                subsidiaryMap.put("cdetailclassguid", cguid);
            }
            // 会计账簿唯一标识
            subsidiaryMap.put("cbusinessprimarykeystr",
                    "!"+CollectionUtil.getStringFromMap(subsidiaryMap,"csourceorganid")+"!"+
                            CollectionUtil.getStringFromMap(subsidiaryMap,"cperioddatelist")+"!"+
                            CollectionUtil.getStringFromMap(subsidiaryMap,"cdetailclasscode"));
            // 凭证元数据改为账簿元数据，列表id需统一
            Map<String,Object> mdSubsidiaryMap = new HashMap<>((Map) subsidiaryMap.get("md_voucher"));
            mdSubsidiaryMap.put("clistguid",subsidiaryMap.get("cguid"));
            mdSubsidiaryMap.put("cpageid","da_fc_accountbook_list");
            mdSubsidiaryMap.put("ctemplateid","da_fc_accountbook_list_template");
            this.makeDefalultValueForInterface(mdSubsidiaryMap,sourceBatchMap);
            subsidiaryMap.put("md_subsidiary",mdSubsidiaryMap);
            List<Map> mdVoucherz = (List<Map>) subsidiaryMap.get("md_voucherz");
            // 凭证子表元数据改为账簿子表元数据，列表id需统一，父id需统一
            List<Map> mdSubsidiaryList = new ArrayList<>();
            mdVoucherz.forEach(mdSubsidiary -> {
                Map<String,Object> hashMap = new HashMap<>(mdSubsidiary);
                this.makeDefalultValueForInterface(hashMap,sourceBatchMap);
                hashMap.put("clistguid",subsidiaryMap.get("cguid"));
                hashMap.put("cheadid",mdSubsidiaryMap.get("cguid"));
                hashMap.put("cpageid","da_fc_accountbook_list");
                hashMap.put("ctemplateid","da_fc_accountbook_list_template");
                // todo 以下字段如何确定 ？？
                hashMap.put("cbalancedirsel",hashMap.get("cbalancedirsel"));
                hashMap.put("ibalanceamt",hashMap.get("ibalanceamt"));
                mdSubsidiaryList.add(hashMap);
            });
            subsidiaryMap.put("md_subsidiaryz",mdSubsidiaryList);
            // 保存明细账月度的列表
            mxzMap.put(period,subsidiaryMap);
        }
        return subsidiaryMap;
    }

    public List<Map> filterVoucherData(List<Map> list,List<String> filtervoucherId){
        list = list.stream().filter(value1 -> !filtervoucherId.contains(CollectionUtil.getStringFromMap(value1,"clistguid"))).collect(Collectors.toList());
        return list;
    }

    /**
     * //去除字符串中空格、换行、制表
     * @param str
     * @param pattern
     * @param replace
     * @return
     */
    public String replaceSpecialtyStr(String str,String pattern,String replace){
        if(StringUtil.isEmpty(pattern)){
            pattern="\\s*|\t|\r|\n";
        }
        if(StringUtil.isEmpty(pattern)){
            replace="";
        }
        return Pattern.compile(pattern).matcher(str).replaceAll(replace);
    }

    /**
     * 将对方返回的json数据分为两部分保存，防止字段长度不够;
     * @param sourceBatchMap
     * @param json
     * @param resultMapStr
     */
    public void saveSourceBatchJsond(Map<String, Object> sourceBatchMap, String json, String resultMapStr) {
        if (StringUtil.isEmpty(json) || StringUtil.isEmpty(resultMapStr)) {
            LOG.info("获取sap结果为空1" + json);
            LOG.info("获取sap结果为空2" + resultMapStr);
            throw new BusinessException("获取sap结果为空");
        }

        try {
            // 指定截取的数量
            int chunkSize = 65535;
            List<Map> returnList = new ArrayList<>();
            Object cguid = sourceBatchMap.get("cguid");
            // 截取json字符串
            List<String> jsonChunks = this.splitString(json, chunkSize);
            // 截取resultMapStr字符串
            List<String> resultMapStrChunks = this.splitString(resultMapStr, chunkSize);
            int maxSize = Math.max(jsonChunks.size(), resultMapStrChunks.size());
            for (int i = 0; i < maxSize; i++) {
                Map<String, Object> map = new HashMap<>(13);
                makeDefalultValueForInterface(map, sourceBatchMap);
                map.put("csourcebatchid", cguid);
                if (i < jsonChunks.size()) {
                    map.put("cdatajson", jsonChunks.get(i));
                }
                if (i < resultMapStrChunks.size()) {
                    map.put("ccalljsondetail", resultMapStrChunks.get(i));
                }
                map.put("cendtime", getSysTime());
                returnList.add(map);
            }
            LOG.info("开始新增收集日志子表");
            voucherCollectUdpSql.addList("da_api_file_source_batch_jsond", returnList);
            LOG.info("结束新增收集日志子表");
        } catch (Exception e){
            LOG.info("保存收集日志子表失败" + e.getMessage());
            throw new BusinessException("保存收集日志子表失败" + e.getMessage());
        }
    }

    /**
     *  按指定大小截取字符串
     */
    private List<String> splitString(String input, int chunkSize) {
        List<String> chunks = new ArrayList<>();
        int length = input.length();
        for (int i = 0; i < length; i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, length);
            chunks.add(input.substring(i, endIndex));
        }
        return chunks;
    }

}
