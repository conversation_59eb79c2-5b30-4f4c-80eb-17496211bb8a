package com.aisino.da.api.service.publicother.impl;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.CollectLogDAOInterface;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.dao.PzZlRelationDao;
import com.aisino.da.api.handler.enums.DaDataResult;
import com.aisino.da.api.service.DaGetOtherDetialService;
import com.aisino.da.api.service.publicother.SaveAndCheckOtherDataDetialService;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.core.util.ArrayUtil;
import com.aisino.da.fc.bean.CollectParamsBean;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 银行流水数据处理
 */
@Service
public class PzZlRelationHandler implements SaveAndCheckOtherDataDetialService
{
    @Inject
    private DaGetBillDetialDao daGetBillDetialDao;

    @Inject
    private CollectLogDAOInterface collectLogDAOInterface;

    @Inject
    private static PzZlRelationDao pzzlrelationdao;

    @Inject
    private DaGetOtherDetialService daGetOtherDetialService;

    /**
     * 银行流水数据入库
     * @param params
     * @return
     */
    @Override
    public Map collectOtherDetial(Params params)
    {
        CollectParamsBean collectParamsBean = new CollectParamsBean();
        String startTime = BankReceiptConveService.getSysTime();
        collectParamsBean.setStartTime(startTime);

        String ccallcreatedate = CollectionStringUtil.getSysDateTime();
        String code = params.getString("code");//编码
        String serialnumber = params.getString("serialnumber");//同步序列号
        String extcode = params.getString("extcode");//来源系统编码
        String msg = params.getString("businesserrormsg");//业务异常信息
        int totalNumTemp = params.getInt("batchtotalnum");//总批次
        if (totalNumTemp <= 0)
        {
            //当未告知批次时默认只需要入库一批
            totalNumTemp = 1;
        }

        Map msgmap = new HashMap<>();
        //code 为000000 默认为正常返回数据
        String dsid = params.getString("tenantdsid");//数据源
        Map sourceBatch = collectLogDAOInterface.getSourceBatch(serialnumber, extcode, dsid);
        if(CollectionUtil.isBlankMap(sourceBatch))
        {
            msg = "未获取到" + serialnumber + "批次收集发起记录收集";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            return msgmap;
        }
        int ibatchtotalnum = CollectionUtil.getIntFromMap(sourceBatch, "ibatchtotalnum");
        if (ibatchtotalnum < 1)
        {
            sourceBatch.put("ibatchtotalnum",totalNumTemp);
        }



        String csynstatus = CollectionUtil.getStringFromMap(sourceBatch, "csynstatus");
        String sourceBatchId = CollectionUtil.getStringFromMap(sourceBatch, "cguid");
        if (csynstatus.equalsIgnoreCase(CollectConstant.COLLECTION_COMPLETED_CODE))
        {
            msg = "该批次数据已收集完成请勿重复提交";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            return msgmap;
        }
        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
        ApsContextDb apsContext = new ApsContextDb();
        DbService db = apsContext.getDb(dsid);
        /*Map pzMap=db.queryMap(" select * from da_keypair_config where cguid=? ",extcode);
        String csystemname=ArrayUtil.getStringFromMap(pzMap,"cSystemCode_name");*/
        //若总批次大于一需要判断数据已入库数量,超量数据不予入库;
        int SourceBatchDetailcount = collectLogDAOInterface.querySourceBatchDetail(sourceBatchId, dsid);

        if (totalNumTemp >= 1)
        {
            //查询已入库数据数
            if (SourceBatchDetailcount >= totalNumTemp)
            {
                msg = "已收集" + totalNumTemp + "批数据,请勿重复请求";
                msgmap.put("code","1000");
                msgmap.put("msg",msg);
                Map insertSourceBatchDetail = BankReceiptConveService.makeErrorInsertSourceBatchDetail(msg, params, sourceBatch);
                collectLogDAOInterface.insertSourceBatchDetail(insertSourceBatchDetail, collectParamsBean);
                db.commit();
                return msgmap;
            }
        }

        int cout = 0;
        int failcount = 0;
        int successcount = 0;

        //更新状态数据入库
        updateExtSourceBatchStatus(CollectConstant.COLLECTING,CollectConstant.COLLECTING_CODE, totalNumTemp ,sourceBatchId, dsid);
        Map<String, String> updateMap = new HashMap<>();
        String billdetialjson = JsonUtil.toJSON(params);
        updateMap.put("ccalljsondetail",billdetialjson);

        if (StringUtils.isNotBlank(code) && "000000".equalsIgnoreCase(code))
        {
            //开始回单数据入库
            List<Map> voulist = params.getListMap("voulist");//凭证数据
            if (CollectionUtil.isEmpty(voulist)) {
                msg = "获取【凭证关联关系】数据voulist为空!";
            }else {
                String param = CollectionUtil.getStringFromMap(sourceBatch, "param");
                boolean iscover = false;
                if (StringUtils.isNotBlank(param))
                {
                    Map paramMap = JsonUtil.fromJSON(param, Map.class);
                    iscover = CollectionUtil.getBoolean(paramMap, "iscover", false);
                }else
                {
                    iscover = CollectionUtil.getBoolean(sourceBatch, "icover", false);
                }

                DaDataResult daDataResult = checkAndSaveData(voulist, sourceBatch, dsid,iscover);
                //取出本批数据处理结果
                cout = daDataResult.count;
                failcount = daDataResult.failcount;
                successcount = daDataResult.successcount;

                if (daDataResult.cstatus)
                {
                    String collmsg = daDataResult.getCollmsg("凭证关联关系");
                    msg = daDataResult.getMsg() + daDataResult.getSxmsg();
                    msg = collmsg + "详情:" + msg;
                }
                else
                {
                    msg = daDataResult.getMsg() + daDataResult.getSxmsg();
                }
            }
        }else
        {
            msg = params.getString("businesserrormsg");;
        }
        //返回之前更新回调信息

        String sysDateTime = CollectionStringUtil.getSysDateTime();
        updateMap.put("cendtime", sysDateTime);

        //明细入库时,计算数据总数，跟成功的数据数。。。。。。。。
        Map<String, Integer> numbermap = daGetBillDetialDao.getNumber(sourceBatchId,dsid);
        //查询总数..
        Integer icollectnum = CollectionUtil.getIntFromMap(numbermap,"icollectnum");//记录的数据总数
        Integer icollectsuccessnum = CollectionUtil.getIntFromMap(numbermap,"icollectsuccessnum");//记录的成功数据总数

        //生成日志明细
        Map insertSourceBatchDetail = BankReceiptConveService.makeErrorInsertSourceBatchDetail(msg, params, sourceBatch);

        //明细存储总数
        insertSourceBatchDetail.put("icollectnum",cout);
        insertSourceBatchDetail.put("icollectsuccessnum",successcount);

        collectLogDAOInterface.insertSourceBatchDetail(insertSourceBatchDetail, collectParamsBean);

        //数据入库之前判断数据库批次
        SourceBatchDetailcount = collectLogDAOInterface.querySourceBatchDetail(sourceBatchId, dsid);

        if (!CollectConstant.CANCELLATION_CODE.equalsIgnoreCase(csynstatus) && (totalNumTemp == 1 || SourceBatchDetailcount >= totalNumTemp))
        {
            //单批次数据处理完成认为数据入库处理完成不允许
            updateMap.put("csynstatus", CollectConstant.COLLECTION_COMPLETED_CODE);
            updateMap.put("csynstatus_name", CollectConstant.COLLECTION_COMPLETED);

        }else
        {
            updateMap.put("csynstatus", CollectConstant.COLLECTING_CODE);
            updateMap.put("csynstatus_name",CollectConstant.COLLECTING);
            //多批次情况
        }

        //放入总的提示信息
        /*int ctotal = cout +icollectnum;
        int succestotal = successcount + icollectsuccessnum;
        String countmsg = "凭证关联关系共收集" + ctotal + "条,\n\r成功" + succestotal + "条,\n\r失败" + (ctotal - succestotal)  + "条";
        */
        updateMap.put("msg",msg);
        updateMap.put("ccallcreatedate",ccallcreatedate);

        updateExtSourceBatch(updateMap, sourceBatchId, dsid);
        msgmap.put("code","1000");
        msgmap.put("msg","数据处理完成：" + msg);
        db.commit();
        return msgmap;
    }

    public void updateExtSourceBatchStatus(String name, String code, int ibatchtotalnum, String cguid, String dsid) {
        daGetBillDetialDao.updateExtSourceBatchStatus(name, code, ibatchtotalnum ,cguid,dsid);
    }

    public void updateExtSourceBatch(Map updateMap, String cguid, String dsid)
    {
        daGetBillDetialDao.upadteExtSourceBatchByCZY(updateMap, cguid, dsid);
    }

    private DaDataResult checkAndSaveData(List<Map> vouList, Map sourceBatch, String dsid, boolean iscover)
    {
        StringBuffer stringBuffer = new StringBuffer();
        int count = vouList.size();
        int failcount = 0;
        int successcount = 0;
        String corgnid = ArrayUtil.getStringFromMap(sourceBatch, "corgnid");
        //String csystemname=ArrayUtil.getStringFromMap(sourceBatch,"csourcesysname");
        String ccreatedate = CollectionStringUtil.getSysDateTime();
        String cserialnumber=ArrayUtil.getStringFromMap(sourceBatch,"cserialnumber");
        String ccollector=ArrayUtil.getStringFromMap(sourceBatch,"ccreatorid");
        String ccollector_name=ArrayUtil.getStringFromMap(sourceBatch,"ccreatorid_name");
        String extcode=ArrayUtil.getStringFromMap(sourceBatch,"extcode");
        ApsContextDb apsContext = new ApsContextDb();
        DbService db = apsContext.getDb(dsid);

        Map orgnMap=db.queryMap(" select * from aos_orgn where cguid='"+corgnid+"' ");
        String corgncode= ArrayUtil.getStringFromMap(orgnMap,"ccode");
        String corgnname= ArrayUtil.getStringFromMap(orgnMap,"cname");
        Map<String,String> taotalKeys=new HashMap();
        for (Map vouMap : vouList)
        {
            vouMap.put("corgnid",corgnid);
            vouMap.put("corgncode",corgncode);
            vouMap.put("corgnname",corgnname);
            //vouMap.put("csystemname",csystemname); 去掉默认值，让对方传
            //增加新的字段
            vouMap.put("ccreatedate",ccreatedate);
            vouMap.put("cserialnumber",cserialnumber);
            vouMap.put("ccollector",ccollector);
            vouMap.put("ccollector_name",ccollector_name);
            vouMap.put("extcode",extcode);
            //20250213 ddy 改造
            vouMap.put("cCreatorId",ccollector);
            vouMap.put("cCreatorId_name",ccollector_name);
            vouMap.put("cOrgnId_name",corgnname);
            String checkmsg = checkDataByVouList(vouMap, sourceBatch, dsid,iscover);
            if (checkmsg.length() > 0)
            {
                stringBuffer.append(checkmsg);
                failcount++;
                makeErrorLog(vouMap,sourceBatch,dsid,checkmsg);
            }else
            {
                //数据入库
                DaDataResult daDataResult = saveDataByVouList(vouMap, sourceBatch, dsid,taotalKeys);
                if (!daDataResult.getCstatus())
                {
                    stringBuffer.append(daDataResult.getMsg());
                    failcount++;
                    makeErrorLog(vouMap,sourceBatch,dsid,daDataResult.getMsg());
                }else
                {
                    successcount++;
                }
            }
        }
        return new DaDataResult().sucess("凭证关联关系收集完成"  + stringBuffer).addcount(count).addsuccesscount(successcount).addfailcount(failcount);
    }

    private void makeErrorLog(Map vouMap, Map sourceBatch,String dsid,String checkmsg) {
        Map logMap = new HashMap();
        String cguid=Guid.g();

        String cvouchercode=ArrayUtil.getStringFromMap(vouMap,"cvouchercode");
        logMap.put("cguid",cguid);
        logMap.put("cvoucherid",ArrayUtil.getStringFromMap(vouMap,"cvoucherid"));
        logMap.put("cvouchercode",cvouchercode);
        if(!ArrayUtil.isBlank(cvouchercode)&&cvouchercode.contains("-")){
            String[] cvouchercodeArr=cvouchercode.split("-");
            logMap.put("ivouchercode",cvouchercodeArr[1]);
        }
        logMap.put("cvoucherdate",ArrayUtil.getStringFromMap(vouMap,"cvoucherdate"));
        logMap.put("csourcebatchid",ArrayUtil.getStringFromMap(sourceBatch,"cguid"));
        logMap.put("csyncexceptionmsg",checkmsg);
        logMap.put("corgnid",ArrayUtil.getStringFromMap(vouMap,"corgnid"));
        logMap.put("corgnid_name",ArrayUtil.getStringFromMap(vouMap,"corgnname"));
        String ccreatedate = CollectionStringUtil.getSysDateTime();
        logMap.put("ccreatedate", ccreatedate);
        logMap.put("ccreatorid", "1");
        logMap.put("ctimestamp", cguid);
        logMap.put("cpageid", "da_voucher_zl_rel_log_list");
        logMap.put("cpageid_name", "凭证与原始资料关系");
        logMap.put("ctemplateid", "da_voucher_zl_rel_log_list_template");
        logMap.put("ctemplateid_name", "凭证与原始资料关系");
        logMap.put("cadminorgnid", ArrayUtil.getStringFromMap(sourceBatch,"cadminorgnid"));
        logMap.put("cadminorgnid_name",ArrayUtil.getStringFromMap(sourceBatch,"cadminorgnid_name"));
        List<Map> loglist = new ArrayList<>();
        loglist.add(logMap);
        pzzlrelationdao.insertDetialByTableName("da_voucher_zl_rel_log", loglist,dsid);

    }


    public static String checkDataByVouList(Map vouMap, Map sourceBatch, String dsid, boolean iscover) {
        StringBuffer sb = new StringBuffer();
        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
        //校验凭证数据
        CheckVouMap(vouMap,sb);
        return sb.toString();
    }

    private static void CheckVouMap(Map vouMap, StringBuffer sb) {
      String cvoucherid=ArrayUtil.getStringFromMap(vouMap,"cvoucherid");
      String cvouchercode=ArrayUtil.getStringFromMap(vouMap,"cvouchercode");
      String cvoucherdate=ArrayUtil.getStringFromMap(vouMap,"cvoucherdate");
      String cbusinesscode=ArrayUtil.getStringFromMap(vouMap,"cbusinesscode");
      String ctype=ArrayUtil.getStringFromMap(vouMap,"ctype");
      if(ArrayUtil.isBlank(cvoucherid)){
          sb.append("必填字段凭证ID:cvoucherid【" + cvoucherid + "】值为空,");
      }
      if(ArrayUtil.isBlank(cvouchercode)){
          sb.append("必填字段凭证编号:cvouchercode【" + cvouchercode + "】值为空,");
      }
      if(ArrayUtil.isBlank(cvoucherdate)){
          sb.append("必填字段凭证日期:cvoucherdate【" + cvoucherdate + "】值为空,");
      }
      if(ArrayUtil.isBlank(cbusinesscode)){
          sb.append("必填字段业务编号:cbusinesscode【" + cbusinesscode + "】值为空,");
      }
      if(ArrayUtil.isBlank(ctype)){
            sb.append("必填字段原始资料类型:ctype【" + ctype + "】值为空,");
      }
    }
    public static DaDataResult saveDataByVouList(Map vouMap, Map sourceBatch, String dsid,Map<String,String> taotalKeys) {
        List<Map> voulist = new ArrayList<>();//银行回单数据
        try {
            String corgnid = ArrayUtil.getStringFromMap(sourceBatch, "corgnid");
            String cvoucherid = ArrayUtil.getStringFromMap(vouMap, "cvoucherid");
            String ctype=ArrayUtil.getStringFromMap(vouMap,"ctype");
            String cbusinesscode=ArrayUtil.getStringFromMap(vouMap,"cbusinesscode");
            String cbusinessid=ArrayUtil.getStringFromMap(vouMap,"cbusinessid");
            String cinvoicecode=ArrayUtil.getStringFromMap(vouMap,"cinvoicecode");
            String cvouchercode=ArrayUtil.getStringFromMap(vouMap,"cvouchercode");

            if(!ArrayUtil.isBlank(ctype)){
                vouMap.put("ctypename",CollectConstant.PZ_ZL_RELATION_MAP.get(ctype));
                if(!"1".equalsIgnoreCase(ctype)){
                    cbusinessid=cbusinesscode;
                }
            }
            vouMap.put("cbusinessid",cbusinessid);
            //20250213 ddy 优化改造
            String cguid=Guid.g();
            vouMap.put("cguid",cguid);
            vouMap.put("cTimeStamp",cguid);
            voulist.add(vouMap);
            String key=cvoucherid+"_"+cbusinessid+"_"+cbusinesscode+"_"+ctype+"_"+cinvoicecode;
            //入库前删除上次的数据
            //pzzlrelationdao.deleteVouZlRelation(cvoucherid,dsid, corgnid);
           /* boolean existFlag= pzzlrelationdao.queryVouZlRelationByDimension(cvoucherid,cbusinessid,cbusinesscode,ctype,dsid,cinvoicecode,corgnid);
            if(existFlag){
                String err="关联关系存在相同维度:凭证id【"+cvoucherid+"】,业务id【"+cbusinessid+"】,业务号码【"+cbusinesscode+"】,资料类型【"+ctype+"】,发票号码【"+cinvoicecode+"】";
                return new DaDataResult().error(err);
            }*/
            if(taotalKeys.containsKey(key)){
                String err="关联关系存在相同记录:凭证id【"+cvoucherid+"】,凭证号["+cvouchercode+"],业务id【"+cbusinessid+"】,业务号码【"+cbusinesscode+"】,资料类型【"+ctype+"】,发票号码【"+cinvoicecode+"】";
                return new DaDataResult().error(err);
            }
            pzzlrelationdao.deleteVouZlRelationByDimension(cvoucherid,cbusinessid,cbusinesscode,ctype,dsid,cinvoicecode,corgnid);
            pzzlrelationdao.insertDetialByTableName("da_voucher_zl_rel", voulist,dsid);
            //把保存的缓存起来,处理报文中存在相同记录的数据
            taotalKeys.put(key,key);
        } catch (Exception e) {
            return new DaDataResult().error("凭证关联关系入库异常：【" + e.getMessage() + "】");
        }finally {
            ApsContextDb apsContext = new ApsContextDb();
            DbService db = apsContext.getDb(dsid);
            db.commit();
        }
        return new DaDataResult().sucess("凭证关联关系收集完成");
    }
}
