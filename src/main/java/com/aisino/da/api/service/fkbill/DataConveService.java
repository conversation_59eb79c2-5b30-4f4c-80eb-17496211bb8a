package com.aisino.da.api.service.fkbill;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.BeanHelper;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.api.util.DaApiProFileUtil;
import com.aisino.da.api.util.ENComParametersUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

public class DataConveService
{
    public static final  List<String> CCLASSES = Lists.newArrayList("客运汽车票", "机票行程单", "火车票", "出租车票");

    public static void converfile(Map oldfileMap, List<Map> filelist, HashMap<String, String> daFileMap, String blankid, String invoiceid, Map cvmap, String oldbillid, String ishead, String interval, String cfilecontents)
    {
        String filename = CollectionUtil.getStringFromMap(oldfileMap, "文件对象存储文件名");
        if (StringUtils.isNotBlank(invoiceid) && StringUtils.isBlank(filename))
        {
            return;
        }
        Map nfileMap  = new HashMap<>();
        nfileMap.put("cfilecontents", cfilecontents);
        for (String key : daFileMap.keySet())
        {
            if ("文件类型".equalsIgnoreCase(key))
            {
                //文件来源类型(0凭证的文件，1单据的文件，2发票的文件(ocr识别文件，非ocr识别文件),3银行回单,4模拟打印文件,5其他， 6会计账簿， 7会计报告，8其他会计资料9电子档案模拟打印设置的
                String cfilesourcetype = CollectionUtil.getStringFromMap(oldfileMap, "文件来源类型");
                if(!StringUtils.isBlank(cfilesourcetype))
                {
                    if (cfilesourcetype.contains("4"))
                    {
                        nfileMap.put(daFileMap.get(key),"2");
                        nfileMap.put("syssource", "fk-imm");
                    }
                    else  if (cfilesourcetype.contains("2"))
                    {
                        nfileMap.put(daFileMap.get(key),"1");
                        nfileMap.put("syssource", "fk-imm");
                    }else if (cfilesourcetype.contains("9"))
                    {
                        nfileMap.put(daFileMap.get(key),"2");
                        nfileMap.put("syssource", "fk-imm");
                    }
                    else
                    {
                        nfileMap.put(daFileMap.get(key),"0");
                        nfileMap.put("syssource", "fk-appendix");
                    }
                }else
                {
                    nfileMap.put(daFileMap.get(key),oldfileMap.get(key));
                }
            }else if ("文件收集方式".equalsIgnoreCase(key))
            {
                Object sjfs = oldfileMap.get(key);
                if (sjfs instanceof Number)
                {
                    if (sjfs instanceof Integer)
                    {
                        nfileMap.put(daFileMap.get(key), sjfs);
                    }else
                    {
                        Double aDouble = (Double) oldfileMap.get(key);
                        int i = aDouble.intValue();
                        nfileMap.put(daFileMap.get(key),i);
                    }
                }else
                {
                    nfileMap.put(daFileMap.get(key),sjfs);
                }

            }else if ("文件来源类型".equalsIgnoreCase(key))
            {
                if (StringUtils.isNotBlank(invoiceid))
                {
                    nfileMap.put(daFileMap.get(key),"2");
                }
                else if (StringUtils.isNotBlank(blankid))
                {
                    nfileMap.put(daFileMap.get(key),"3");
                }else
                {
                    nfileMap.put(daFileMap.get(key),"1");
                }
            }
            else if ("排序字段".equalsIgnoreCase(key))
            {
                if (isObjectEmpty(oldfileMap.get(key)))
                {
                    nfileMap.put(daFileMap.get(key),"0");
                }else
                {
                    nfileMap.put(daFileMap.get(key),oldfileMap.get(key));
                }
            }
            else
            {
                nfileMap.put(daFileMap.get(key),oldfileMap.get(key));

            }
        }
        nfileMap.put("cguid", Guid.g());
        if (StringUtils.isBlank(blankid) && StringUtils.isBlank(invoiceid))
        {
            nfileMap.put("cbusinesstype","1");
        }
        else if (StringUtils.isNotEmpty(invoiceid))
        {
            nfileMap.put("cbusinesstype","2");
        }
        else if (StringUtils.isNotEmpty(blankid))
        {
            nfileMap.put("cbusinesstype","3");
        }
        nfileMap.put("ishead",ishead);
        nfileMap.put("bankid", blankid);
        nfileMap.put("invoiceid", invoiceid);
        if(StringUtils.isNotBlank(oldbillid))
        {
            nfileMap.put("cbillid", oldbillid);
        }
        nfileMap.putAll(cvmap);
        nfileMap.put("cguid",Guid.g());
        String cfiletype = CollectionUtil.getStringFromMap(nfileMap, "cfiletype");
        if (StringUtils.isBlank(cfiletype))
        {
            nfileMap.put("cfiletype", "0");
        }

        //云费控数据放入载体类型，如果存在值则认为费控已传递，不存在则需要自己处理
        String carriertype = CollectionUtil.getStringFromMap(nfileMap, "carriertype");
        if (StringUtils.isBlank(carriertype))
        {
            nfileMap.put("carriertype", DaApiProFileUtil.isImageFile(filename));
        }

        nfileMap.put("cstatus","0");
        nfileMap.put("cstatus_name","未收集");

        nfileMap.put("datainterval", interval);
        String xbrlname = CollectionUtil.getStringFromMap(oldfileMap, "xbrlname");
        if(StringUtils.isNotBlank(invoiceid) && StringUtils.isNotBlank(xbrlname))
        {
            //增加xbrl 文件处理
            Map xbrlmap = new HashMap<>();
            xbrlmap.putAll(nfileMap);
            xbrlmap.put("cguid", Guid.g() );
            xbrlmap.put("filename", xbrlname);
            xbrlmap.put("cfilerealname",xbrlname);
            xbrlmap.put("mdnumber",oldfileMap.get("xbrl文件哈希码"));
            xbrlmap.put("chxtype",oldfileMap.get("xbrl文件哈希码类型"));
      /*      xbrlmap.put("cstatus","9");
            xbrlmap.put("cstatus_name","未收集,来自云费控数据");*/
            xbrlmap.put("cstatus","0");
            xbrlmap.put("cstatus_name","未收集");
            xbrlmap.put("datainterval", interval);

            if (StringUtils.isBlank(carriertype))
            {
                xbrlmap.put("carriertype", DaApiProFileUtil.isImageFile(xbrlname));
            }
            xbrlmap.put("datainterval", interval);
            filelist.add(xbrlmap);
        }

        filelist.add(nfileMap);
    }

    public static void databankAndVoucher(Map commonmap, List<Map> bankAndVoucherRellist, Map Bankmap)
    {
        Map bMap = new HashMap<>();

        bMap.put("ctransactionnum", Bankmap.get("ctransactionnum"));
        bMap.put("cvoucherid", commonmap.get("cvoucherid"));
        bMap.put("cvoucode", commonmap.get("cvoucherno"));
        bMap.put("cguid",Guid.g());
        bMap.putAll(makeBatchDefalultValue(commonmap));
        bankAndVoucherRellist.add(bMap);
    }
    public static void dataTicketAndVoucher(Map commonmap, List<Map> ticketAndVoucherRellist, Map invoicemap)
    {
        Map ticketAndVoucherRel = new HashMap<>();
        ticketAndVoucherRel.putAll(commonmap);
        ticketAndVoucherRel.putAll(invoicemap);
        ticketAndVoucherRel.put("cguid",Guid.g());
        ticketAndVoucherRellist.add(ticketAndVoucherRel);
    }

    public static void publicextconverfile(Map oldfileMap, List<Map> filelist, HashMap<String, String> daFileMap, String blankid, String invoiceid, Map cvmap, String oldbillid, String ishead, String dafilepath, Map sourceBatch, String interval, String cfilecontents)
    {
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        String filename = enComParametersUtil.getCommonStringValue(oldfileMap, "存储文件名", "filename");
        String storagemethod = enComParametersUtil.getCommonStringValue(oldfileMap,"存储方式","storagemethod");
        String mdnumber = enComParametersUtil.getCommonStringValue(oldfileMap, "文件哈希码", "hxcode");
        String chxtype = enComParametersUtil.getCommonStringValue(oldfileMap, "文件哈希类型", "chxtype");
        String xbrlname = CollectionUtil.getStringFromMap(oldfileMap, "xbrlname");
        String subxbrlname = CollectionUtil.getStringFromMap(oldfileMap, "xbrlname");
        //若文件名跟xbrl文件名均为空，则不处理文件
        if (StringUtils.isBlank(filename) && StringUtils.isBlank(xbrlname))
        {
            return;
        }

        Map nfileMap  = new HashMap<>();
        nfileMap.put("cfilecontents", cfilecontents);
        nfileMap.putAll(cvmap);
        nfileMap.remove("msg");
        for (String key : daFileMap.keySet())
        {
            if ("文件类型".equalsIgnoreCase(key) || "cfiletype".equalsIgnoreCase(key))
            {
                //文件来源类型(0凭证的文件，1单据的文件，2发票的文件(ocr识别文件，非ocr识别文件),3银行回单,4模拟打印文件,5其他， 6会计账簿， 7会计报告，8其他会计资料9电子档案模拟打印设置的
                String cfilesourcetype = enComParametersUtil.getCommonStringValue(oldfileMap, "文件来源类型","cfilesourcetype");
                if(!StringUtils.isBlank(cfilesourcetype))
                {
                    if (cfilesourcetype.contains("4"))
                    {
                        nfileMap.put(daFileMap.get(key),"2");
                        nfileMap.put("syssource", "fk-imm");
                    }
                    else  if (cfilesourcetype.contains("2"))
                    {
                        nfileMap.put(daFileMap.get(key),"1");
                        nfileMap.put("syssource", "fk-imm");
                    }else if (cfilesourcetype.contains("9"))
                    {
                        nfileMap.put(daFileMap.get(key),"2");
                        nfileMap.put("syssource", "fk-imm");
                    }
                    else
                    {
                        nfileMap.put(daFileMap.get(key),"0");
                        nfileMap.put("syssource", "fk-appendix");
                    }
                }else
                {
                    nfileMap.put(daFileMap.get(key),oldfileMap.get(key));
                }
            }else if ("文件收集方式".equalsIgnoreCase(key) || "filecollmethod".equalsIgnoreCase(key))
            {
                Object sjfs = oldfileMap.get(key);
                if (sjfs instanceof Number)
                {
                    if (sjfs instanceof Integer)
                    {
                        nfileMap.put(daFileMap.get(key), sjfs);
                    }else
                    {
                        Double aDouble = (Double) oldfileMap.get(key);
                        int i = aDouble.intValue();
                        nfileMap.put(daFileMap.get(key),i);
                    }
                }
                else
                {
                    nfileMap.put(daFileMap.get(key),sjfs);
                }

            } else if ("文件来源类型".equalsIgnoreCase(key) || "cfilesourcetype".equalsIgnoreCase(key))
            {
                if (StringUtils.isNotBlank(invoiceid))
                {
                    nfileMap.put(daFileMap.get(key),"2");
                }
                else if (StringUtils.isNotBlank(blankid))
                {
                    nfileMap.put(daFileMap.get(key),"3");
                }else
                {
                    nfileMap.put(daFileMap.get(key),"1");
                }
            }else if ("存储文件名".equalsIgnoreCase(key) || "filename".equalsIgnoreCase(key))
            {
                filename = CollectionUtil.getStringFromMap(oldfileMap, key);
                //只有url形式传输可以截取文件名，其它形式不可以。。。
                if("url".equalsIgnoreCase(storagemethod)){

                    if (StringUtils.isNotBlank(mdnumber) && StringUtils.isNotBlank(chxtype)) {
                        String[] split = filename.split("\\.");
                        String filetype = split[split.length - 1];
                        filename = chxtype + mdnumber + "." + filetype;

                    }
                }

                nfileMap.put(daFileMap.get(key),filename);
            }
            else if ("排序字段".equalsIgnoreCase(key) || "csortindex".equalsIgnoreCase(key))
            {
                if (isObjectEmpty(oldfileMap.get(key)))
                {
                    nfileMap.put(daFileMap.get(key),"0");
                }else
                {
                    nfileMap.put(daFileMap.get(key),oldfileMap.get(key));
                }
            }
            else
            {
                nfileMap.put(daFileMap.get(key),oldfileMap.get(key));

            }
        }
        nfileMap.put("cguid", Guid.g());
        if (StringUtils.isBlank(blankid) && StringUtils.isBlank(invoiceid))
        {
            nfileMap.put("cbusinesstype","1");
        }
        else if (StringUtils.isNotEmpty(invoiceid))
        {
            nfileMap.put("cbusinesstype","2");
        }
        else if (StringUtils.isNotEmpty(blankid))
        {
            nfileMap.put("cbusinesstype","3");
        }
        nfileMap.put("ishead",ishead);
        nfileMap.put("bankid", blankid);
        nfileMap.put("invoiceid", invoiceid);

        //只有local形式传输修改电子档案文件路径
        if ("local".equalsIgnoreCase(storagemethod)) {
            if(StringUtils.isNotEmpty(mdnumber) && StringUtils.isNotEmpty(chxtype)){
                dafilepath = dafilepath+"/"+chxtype+"/"+mdnumber;
            }
        }
        nfileMap.put("dafilepath", dafilepath);
        nfileMap.put("cserialnumber",sourceBatch.get("cserialnumber"));
        nfileMap.put("syssource",sourceBatch.get("csourcesysname"));

        if(StringUtils.isNotBlank(oldbillid))
        {
            nfileMap.put("cbillid", oldbillid);
        }
        nfileMap.put("cguid",Guid.g());

        String cfiletype = CollectionUtil.getStringFromMap(nfileMap, "cfiletype");
        if (StringUtils.isBlank(cfiletype))
        {
            nfileMap.put("cfiletype", "0");
        }

        String carriertype = CollectionUtil.getStringFromMap(nfileMap, "carriertype");
        if (StringUtils.isBlank(carriertype))
        {
            nfileMap.put("carriertype", DaApiProFileUtil.isImageFile(filename));
        }

        nfileMap.put("datainterval", interval);
        nfileMap.put("cstatus","0");
        nfileMap.put("cstatus_name","收集中");

        if(StringUtils.isNotBlank(invoiceid) && StringUtils.isNotBlank(xbrlname) && StringUtils.isNotBlank(filename))
        {
            //增加xbrl 文件处理
            Map xbrlmap = new HashMap<>();
            xbrlmap.putAll(nfileMap);
            xbrlmap.put("cguid", Guid.g() );
            xbrlmap.put("filename", subxbrlname);
            xbrlmap.put("cfilerealname",xbrlname);
            xbrlmap.put("mdnumber",oldfileMap.get("xbrl文件哈希码"));
            xbrlmap.put("chxtype",oldfileMap.get("xbrl文件哈希码类型"));
            xbrlmap.put("fileurl",oldfileMap.get("xbrl文件访问地址"));
            xbrlmap.put("datainterval", interval);

            if (StringUtils.isBlank(carriertype))
            {
                xbrlmap.put("carriertype", DaApiProFileUtil.isImageFile(xbrlname));
            }

            filelist.add(xbrlmap);
        }else if(StringUtils.isNotBlank(xbrlname) && StringUtils.isBlank(filename))
        {
            // 若存在xbrl文件,不存在板式文件
            nfileMap.put("filename", subxbrlname);
            nfileMap.put("cfilerealname",xbrlname);
            nfileMap.put("mdnumber",oldfileMap.get("xbrl文件哈希码"));
            nfileMap.put("chxtype",oldfileMap.get("xbrl文件哈希码类型"));
            nfileMap.put("fileurl",oldfileMap.get("xbrl文件访问地址"));
            nfileMap.put("datainterval", interval);

            if (StringUtils.isBlank(carriertype))
            {
                nfileMap.put("carriertype", DaApiProFileUtil.isImageFile(xbrlname));
            }
        }

        filelist.add(nfileMap);
    }


    public static Map converBillHead(Map headMap, HashMap<String, String> billMap, Map cvmap, String billheadid, List<Map> billheadlist)
    {
        Map nHeadMap  = new HashMap<>();
        for (String key : billMap.keySet())
        {
            if("cdeptguid_name".equals(billMap.get(key)))
            {
                nHeadMap.put("cdeptguid", headMap.get(key));
                nHeadMap.put(billMap.get(key), headMap.get(key));

            }
            else
            {
                if ("报销人id".equalsIgnoreCase(key))
                {
                    nHeadMap.put(billMap.get(key), headMap.get("报销人"));

                } else if ("还款人id".equalsIgnoreCase(key))
                {
                    nHeadMap.put(billMap.get(key), headMap.get("还款人"));

                }else if ("申请人id".equalsIgnoreCase(key))
                {
                    nHeadMap.put(billMap.get(key), headMap.get("申请人"));

                }else if ("借款人id".equalsIgnoreCase(key))
                {
                    nHeadMap.put(billMap.get(key), headMap.get("借款人"));

                }
                else
                {
                    nHeadMap.put(billMap.get(key), headMap.get(key));
                }
            }
        }
        nHeadMap.put("cguid", billheadid);
        nHeadMap.putAll(cvmap);
        billheadlist.add(nHeadMap);
        return nHeadMap;
    }

    public static void converBillBody(Map bodymap, HashMap<String, String> billMap, String billheadid, List<Map> billbodylist, Map cvmap) {
        Map nBodyMap  = new HashMap<>();
        for (String key : billMap.keySet())
        {
            nBodyMap.put(billMap.get(key),bodymap.get(key));
        }
        nBodyMap.put("cheadid",billheadid);
        nBodyMap.putAll(cvmap);
        nBodyMap.put("cguid", Guid.g());
        billbodylist.add(nBodyMap);
    }


    public static void valueValiBillDate(HashMap<String, String> voucherRequiredMap, HashMap<String, String> billHeadRequiredMap, HashMap<String, String> billBodyRequiredMap, HashMap<String, String> billHeadLengthMap, HashMap<String, String> billBodyLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        //校验外层凭证信息
        valueVoucherValidate(voucherRequiredMap, billHeadLengthMap, billdetial, stringBuffer);
        //校验主表信息
        valuBillHeadValidate(billHeadRequiredMap, billHeadLengthMap, billdetial, stringBuffer,billBodyRequiredMap,billBodyLengthMap);
    }


    private static void valuBillHeadValidate(HashMap<String, String> billHeadRequiredMap, HashMap<String, String> billHeadLengthMap, Map billdetial, StringBuffer stringBuffer, HashMap<String, String> billBodyRequiredMap, HashMap<String, String> billBodyLengthMap)
    {
        Object headlist = billdetial.get("headlist");
        if (isObjectNotEmpty(headlist) && headlist instanceof Map)
        {
            Map headMap = (Map)headlist;
            StringBuffer heads = new StringBuffer();
            valueVoucherValidate(billHeadRequiredMap, billHeadLengthMap, headMap, heads);
            if (heads.length() > 0)
            {
                stringBuffer.append("单据主表验证信息【").append(heads).append("】");
            }
            Object bodylist = headMap.get("bodylist");
            if (isObjectNotEmpty(bodylist) && bodylist instanceof List)
            {
                List<Map> newbodylistlist = (List)bodylist;
                StringBuffer bodys = new StringBuffer();
                for (Map bodyMap : newbodylistlist)
                {
                    valueVoucherValidate(billBodyRequiredMap, billBodyLengthMap, bodyMap, bodys);
                }
                if (bodys.length() > 0)
                {
                    stringBuffer.append("单据子表表验证信息【").append(bodys).append("】");
                }
            }
            else
            {
                stringBuffer.append("获取单据子表信息为空");
            }
        }
        else
        {
            stringBuffer.append("获取单据信息为空");
        }
    }

    private static void valueVoucherValidate(HashMap<String, String> voucherRequiredMap, HashMap<String, String> billHeadLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        for (String key : voucherRequiredMap.keySet())
        {//必填项
            Object value = billdetial.get(key);
            if(isObjectNotEmpty(value))
            {
                valueLengthValidate(key,value,billHeadLengthMap,stringBuffer);
            }
            else
            {//必填报错
                stringBuffer.append(key+"必填;");
            }
        }

        for (Object key : billdetial.keySet())
        {//必填项
            Object value = voucherRequiredMap.get(key);
            if(isObjectEmpty(value))
            {
                valueLengthValidate(key.toString(),billdetial.get(key),billHeadLengthMap,stringBuffer);
            }
        }
        //文件名校验
        Object banklist = billdetial.get("bankreceiptlist");
        if (banklist != null && banklist instanceof List)
        {
            List<Map> oldbanklist= (ArrayList<Map>)banklist;
            for (Map bankmap : oldbanklist)
            {
                Object filesobj = bankmap.get("filelist");
                if (filesobj != null && filesobj instanceof List)
                {
                    List<Map> oldfilelist= (ArrayList<Map>)filesobj;

                    for (Map filemap : oldfilelist)
                    {
                        CheckFileName(filemap, stringBuffer);
                    }
                }
            }
        }
        Object invoicedetiallist = billdetial.get("invoicelist");
        if (invoicedetiallist != null && invoicedetiallist instanceof List)
        {
            List<Map> oinvoicelist= (ArrayList<Map>)invoicedetiallist;

            for (Map invoicemap : oinvoicelist)
            {
                Object filesobj = invoicemap.get("filelist");
                if (filesobj != null && filesobj instanceof List)
                {
                    List<Map> oldfilelist= (ArrayList<Map>)filesobj;

                    for (Map filemap : oldfilelist)
                    {
                        CheckFileNameT(filemap, stringBuffer);

                    }
                }
            }
        }
        Object filesobj = billdetial.get("filelist");
        if (filesobj != null && filesobj instanceof List)
        {
            List<Map> oldfilelist= (ArrayList<Map>)filesobj;

            for (Map filemap : oldfilelist)
            {
                CheckFileName(filemap, stringBuffer);

            }
        }
    }
    private static void CheckFileNameT(Map filemap, StringBuffer sb)
    {
        String cfilerealname = CollectionUtil.getStringFromMap(filemap, "文件实际名称");
        String filename = CollectionUtil.getStringFromMap(filemap, "存储文件名");
        if (StringUtils.isBlank(cfilerealname))
        {
            cfilerealname = filename;
        }

        if (!ArrayUtil.isValidFileName(cfilerealname))
        {
            sb.append("文件实际名称").append(cfilerealname).append("缺少文件名后缀或文件格式错误!");
        }
        if (!ArrayUtil.isValidFileName(filename))
        {
            sb.append("存储文件名").append(filename).append("缺少文件名后缀或文件格式错误!");
        }
    }
    private static void CheckFileName(Map filemap, StringBuffer sb)
    {
        String cfilerealname = StringUtils.isNotBlank(CollectionUtil.getStringFromMap(filemap, "文件实际名称"))? CollectionUtil.getStringFromMap(filemap, "文件实际名称") : CollectionUtil.getStringFromMap(filemap, "文件实际名称");
        String filename = StringUtils.isNotBlank(CollectionUtil.getStringFromMap(filemap, "文件对象存储文件名")) ? CollectionUtil.getStringFromMap(filemap, "文件对象存储文件名") : CollectionUtil.getStringFromMap(filemap, "存储文件名");

        if (StringUtils.isBlank(cfilerealname))
        {
            cfilerealname = filename;
        }


        if (!ArrayUtil.isValidFileName(cfilerealname))
        {
            sb.append("文件实际名称").append(cfilerealname).append("缺少文件名后缀或文件格式错误!");
        }
        if (!ArrayUtil.isValidFileName(filename))
        {
            sb.append("文件对象存储文件名").append(filename).append("缺少文件名后缀或文件格式错误!");
        }
    }

    private static Boolean isObjectNotEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isNotEmpty(s);
    }

    private static Boolean isObjectEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isEmpty(s);
    }
    private static void valueLengthValidate(String key, Object value, HashMap<String, String> LengthMap, StringBuffer stringBuffer){
        //数值类型不参数长度判断
        if (!(value instanceof Number) && !(value instanceof List)&& !(value instanceof Map))
        {
            Integer length = 0;
            if (key.contains("id"))
            {
                length = CollectionUtil.getIntFromMap(LengthMap, "id");
            }else if (key.contains("事由") || key.contains("摘要"))
            {
                length = CollectionUtil.getIntFromMap(LengthMap, "事由");
            }else
            {
                length =  CollectionUtil.getIntFromMap(LengthMap, "其它");
            }

            if(value.toString().length()>length){//长度报错
                stringBuffer.append(key+"大于最大长度【" + length + "】;");
            }
        }

    }

    public static Map getDetialMap(Map details, HashMap<String, String> billPublicMap, Map logmap)
    {
        Map cvmap = new HashMap<>();
        for (String key : billPublicMap.keySet())
        {
            if (key == "分表字段")
            {
                String sybilldate = StringUtils.isNotBlank(CollectionUtil.getStringFromMap(details, "上游单据日期")) ? CollectionUtil.getStringFromMap(details, "上游单据日期") : CollectionUtil.getStringFromMap(details, "主单据日期");
                if (StringUtils.isNotBlank(sybilldate) && sybilldate.length() > 4)
                {
                    String year = "da_files_" + sybilldate.substring(0, 4);
                    cvmap.put(billPublicMap.get(key),year);
                }else
                {
                    sybilldate = CollectionUtil.getStringFromMap(details, "单据日期");
                    if (StringUtils.isBlank(sybilldate))
                    {
                        sybilldate = getSysDate();
                    }
                    String year = "da_files_" + sybilldate.substring(0, 4);
                    cvmap.put(billPublicMap.get(key),year);
                }

            }else if("会计年月".equals(key))
            {
                String iyear = CollectionUtil.getStringFromMap(details, "会计年");
                String imonth = CollectionUtil.getStringFromMap(details, "会计月");
                String pzdate = CollectionUtil.getStringFromMap(details, "凭证日期");

                String tdate = CollectionUtil.getStringFromMap(details, "开票日期");
                if (StringUtils.isNotEmpty(tdate) && tdate.length() > 6)
                {
                    String[] ts = tdate.split("-");
                    if(ts.length == 3)
                    {
                        cvmap.put("tyear", ts[0]);
                        cvmap.put("tmonth", ts[1]);
                    }
                }

                if (StringUtils.isNotEmpty(iyear) && StringUtils.isNotEmpty(imonth))
                {
                    cvmap.put(billPublicMap.get(key), iyear + "-" + imonth);
                }else
                {
                    if (StringUtils.isNotEmpty(pzdate))
                    {
                        String[] split = pzdate.split("-");
                        if(split.length == 3)
                        {
                            iyear = split[0];
                            imonth = split[1];
                            cvmap.put(billPublicMap.get("会计年"), iyear);
                            cvmap.put(billPublicMap.get("会计月"), imonth);
                            cvmap.put(billPublicMap.get(key), iyear + "-" + imonth);
                        }
                    }
                }
            }
            else
            {
                if (isObjectNotEmpty(details.get(key)))
                {
                    cvmap.put(billPublicMap.get(key),details.get(key));
                }
            }
        }
        String billdate = CollectionUtil.getStringFromMap(details, "单据日期");
        String billInterval = ArrayUtil.getDataInterval(billdate);
        Object headlist = details.get("表头");

        if (headlist != null && headlist instanceof Map && StringUtils.isBlank(billdate))
        {
            Map headMap = (Map)headlist;
            //获取主单据日期
            billdate = CollectionUtil.getStringFromMap(headMap, "单据日期");
            billInterval = ArrayUtil.getDataInterval(billdate);
        }
        //单据资料增加资料区间
        cvmap.put("datainterval", billInterval);
        cvmap.putAll(makeBatchDefalultValue(logmap));
        return cvmap;
    }

    public static Map getBillCommon(Map details, HashMap<String, String> billPublicMap)
    {
        Map cvmap = new HashMap<>();
        for (String key : billPublicMap.keySet())
        {
            if (key == "分表字段")
            {
                String sybilldate = StringUtils.isNotBlank(CollectionUtil.getStringFromMap(details, "上游单据日期")) ? CollectionUtil.getStringFromMap(details, "上游单据日期") : CollectionUtil.getStringFromMap(details, "主单据日期");
                if (StringUtils.isNotBlank(sybilldate) && sybilldate.length() > 4)
                {
                    String year = "da_files_" + sybilldate.substring(0, 4);
                    cvmap.put(billPublicMap.get(key),year);
                }else
                {
                    sybilldate = CollectionUtil.getStringFromMap(details, "单据日期");
                    if (StringUtils.isBlank(sybilldate))
                    {
                        sybilldate = getSysDate();
                    }
                    String year = "da_files_" + sybilldate.substring(0, 4);
                    cvmap.put(billPublicMap.get(key),year);
                }

            }else if("会计年月".equals(key))
            {
                String iyear = CollectionUtil.getStringFromMap(details, "会计年");
                String imonth = CollectionUtil.getStringFromMap(details, "会计月");
                String pzdate = CollectionUtil.getStringFromMap(details, "凭证日期");

                String tdate = CollectionUtil.getStringFromMap(details, "开票日期");
                if (StringUtils.isNotEmpty(tdate) && tdate.length() > 6)
                {
                    String[] ts = tdate.split("-");
                    if(ts.length == 3)
                    {
                        cvmap.put("tyear", ts[0]);
                        cvmap.put("tmonth", ts[1]);
                    }
                }

                if (StringUtils.isNotEmpty(iyear) && StringUtils.isNotEmpty(imonth))
                {
                    cvmap.put(billPublicMap.get(key), iyear + "-" + imonth);
                }else
                {
                    if (StringUtils.isNotEmpty(pzdate))
                    {
                        String[] split = pzdate.split("-");
                        if(split.length == 3)
                        {
                            iyear = split[0];
                            imonth = split[1];
                            cvmap.put(billPublicMap.get("会计年"), iyear);
                            cvmap.put(billPublicMap.get("会计月"), imonth);
                            cvmap.put(billPublicMap.get(key), iyear + "-" + imonth);
                        }
                    }
                }
            }
            else
            {
                if (isObjectNotEmpty(details.get(key)))
                {
                    cvmap.put(billPublicMap.get(key),details.get(key));
                }
            }
        }
        return cvmap;
    }
    private static String getSysDate(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM");
        String time =s.format(date);
        return time;
    }
    private static Map makeBatchDefalultValue(Map batchmap)
    {
        Map map = new HashMap<>();
        String corgnid           = CollectionUtil.getStringFromMap(batchmap,"corgnid");
        String corgnid_name      = CollectionUtil.getStringFromMap(batchmap,"corgnid_name");
        String cadminorgnid      = CollectionUtil.getStringFromMap(batchmap,"cadminorgnid");
        String cadminorgnid_name = CollectionUtil.getStringFromMap(batchmap,"cadminorgnid_name");
        String ccreatorid        = CollectionUtil.getStringFromMap(batchmap,"ccreatorid");
        String ccreatorid_name   = CollectionUtil.getStringFromMap(batchmap,"ccreatorid_name");
        String ccreatedate       = CollectionStringUtil.getSysDateTime();
        String csourcesysname   = CollectionUtil.getStringFromMap(batchmap,"csourcesysname");
        String cserialnumber   = CollectionUtil.getStringFromMap(batchmap,"cserialnumber");

        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate", ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);

        map.put("ccollectorid",ccreatorid);
        map.put("ccollectorid_name",ccreatorid_name);
        map.put("csourcesysname",csourcesysname);
        map.put("cserialnumber",cserialnumber);
        return map;
    }

    public static void convertree(Map headMap, Map cvmap, List<Map> treelist, HashMap<String, String> treePublicMap, HashMap<String, String> treePrivateMap, String ctype, String invoiceheadid, String bankid, String url, String ishead)
    {
        Map tMap = new HashMap<>();

        for (String key : treePublicMap.keySet())
        {
            if (key == "cparent")
            {
                String cparent = CollectionUtil.getStringFromMap(cvmap, key);
                if (StringUtils.isBlank(cparent))
                {
                    tMap.put(key, "000000");
                }else
                {
                    tMap.put(key, cvmap.get(treePublicMap.get(key)));
                }
            }
            else
            {
                tMap.put(key, cvmap.get(treePublicMap.get(key)));
            }
        }

        for (String key : treePrivateMap.keySet())
        {
            if (!"1".equalsIgnoreCase(ctype)  && ! "cparent".contains(key))
            {
                if ("cmeansname".equalsIgnoreCase(key))
                {
                    tMap.put(key, treePrivateMap.get(key));
                }else
                {
                    tMap.put(key, headMap.get(treePrivateMap.get(key)));
                }
            }else if ("2".equalsIgnoreCase(ctype))
            {
                tMap.put(key, headMap.get(treePrivateMap.get(key)));
            }
            else if ("3".equalsIgnoreCase(ctype))
            {
                tMap.put(key, headMap.get(treePrivateMap.get(key)));
            }
        }

        if ("2".equalsIgnoreCase(ctype))
        {
            tMap.put("cmeansid", invoiceheadid);
            String cmeansname = CollectionUtil.getStringFromMap(headMap, "发票类型");
            if (StringUtils.isNotBlank(cmeansname))
            {
                tMap.put("cmeansname", cmeansname);
            }
        }
        else if ("3".equalsIgnoreCase(ctype))
        {
            tMap.put("cmeansid", bankid);
        }
        String cfilesTableName = CollectionUtil.getStringFromMap(cvmap, "cfiles_table_name");
        tMap.put("cfiletablename", cfilesTableName);

        tMap.put("ctype", ctype);
        tMap.put("cguid",Guid.g());
        tMap.put("pageurl",url);
        tMap.put("ishead",ishead);
        tMap.putAll(makeBatchDefalultValue(cvmap));
        treelist.add(tMap);
    }

    public static void converExttree2(Map headMap, Map cvmap, List<Map> treelist, HashMap<String, String> treePublicMap, HashMap<String, String> treePrivateMap, String ctype, String invoiceheadid, String bankid, String url, Map sourceBatch)
    {
        Map tMap = new HashMap<>();

        for (String key : treePublicMap.keySet())
        {
            if (key == "cparent")
            {
                String cparent = CollectionUtil.getStringFromMap(cvmap, key);
                if (StringUtils.isBlank(cparent))
                {
                    tMap.put(key, "000000");
                }else
                {
                    tMap.put(key, cvmap.get(treePublicMap.get(key)));
                }
            }
            else
            {
                tMap.put(key, cvmap.get(treePublicMap.get(key)));
            }
        }

        for (String key : treePrivateMap.keySet())
        {
            if (!"1".equalsIgnoreCase(ctype)  && ! "cparent".contains(key))
            {
                if ("cmeansname".equalsIgnoreCase(key))
                {
                    tMap.put(key, treePrivateMap.get(key));
                }else
                {
                    tMap.put(key, headMap.get(treePrivateMap.get(key)));
                }
            }else if ("2".equalsIgnoreCase(ctype))
            {
                tMap.put(key, headMap.get(treePrivateMap.get(key)));
            }
            else if ("3".equalsIgnoreCase(ctype))
            {
                tMap.put(key, headMap.get(treePrivateMap.get(key)));
            }
        }

        if (("2".equalsIgnoreCase(ctype)))
        {
            tMap.put("cmeansid", invoiceheadid);
        }
        else if (("3".equalsIgnoreCase(ctype)))
        {
            tMap.put("cmeansid", bankid);
        }
        String cfilesTableName = CollectionUtil.getStringFromMap(cvmap, "cfiles_table_name");
        tMap.put("cfiletablename", cfilesTableName);

        tMap.put("ctype", ctype);
        tMap.put("cguid",Guid.g());
        tMap.put("pageurl",url);
        makeExtDefalultValue(tMap, sourceBatch);
        treelist.add(tMap);
    }
    public static void convertree2(Map headMap, Map cvmap, List<Map> treelist, HashMap<String, String> treePublicMap, HashMap<String, String> treePrivateMap, String ctype, String url)
    {
        Map tMap = new HashMap<>();

        for (String key : treePublicMap.keySet())
        {
            if (key == "cparent")
            {
                String cparent = CollectionUtil.getStringFromMap(cvmap, "cvoucherid");
                if (StringUtils.isBlank(cparent))
                {
                    tMap.put(key, "000000");
                }else
                {
                    tMap.put(key, cvmap.get(treePublicMap.get(key)));
                }
            }
            else
            {
                tMap.put(key, cvmap.get(key));
            }
        }

        for (String key : treePrivateMap.keySet())
        {
            if (!"2".equalsIgnoreCase(ctype)  && ! "cparent".contains(key))
            {
                if ("cmeansname".equalsIgnoreCase(key))
                {
                    tMap.put(key, treePrivateMap.get(key));
                }else
                {
                    tMap.put(key, headMap.get(treePrivateMap.get(key)));
                }
            }
        }
        String cfilesTableName = CollectionUtil.getStringFromMap(cvmap, "cfiles_table_name");

        Map<String, Map> billtype = BeanHelper.getBean(BillVoucherCollectDataService.class).billtype;
        Map billmap = billtype.get(headMap.get("页面id"));
        String billtypeid = CollectionUtil.getStringFromMap(billmap, "cguid");
        String billname = CollectionUtil.getStringFromMap(billmap, "billname");
        //tMap.put("cdabilltypeid",billtypeid);
        tMap.put("cmeansname",billname);

        tMap.put("cfiletablename", cfilesTableName);
        tMap.put("ctype", ctype);
        tMap.put("cguid",Guid.g());
        tMap.put("pageurl",url);
        tMap.putAll(makeBatchDefalultValue(cvmap));
        treelist.add(tMap);
    }

    public static void converCommDetial(Map headMap, HashMap<String, String> publicCommonMap, Map cvmap, List<Map> daheadlist, String sourceBatchCguid, String isexistfile, Map fileistruemap, boolean isRelated, int sum)
    {
        Map dMap = new HashMap<>();
        for (String key : publicCommonMap.keySet())
        {
            if("cdeptguid_name".equals(publicCommonMap.get(key)))
            {
                dMap.put("cdeptguid", headMap.get(key));
                dMap.put(publicCommonMap.get(key), headMap.get(key));

            }else
            {
                if ("报销人id".equalsIgnoreCase(key))
                {
                    dMap.put(publicCommonMap.get(key), headMap.get("报销人"));

                }else if ("还款人id".equalsIgnoreCase(key))
                {
                    dMap.put(publicCommonMap.get(key), headMap.get("还款人"));

                }else if ("申请人id".equalsIgnoreCase(key))
                {
                    dMap.put(publicCommonMap.get(key), headMap.get("申请人"));

                }else if ("借款人id".equalsIgnoreCase(key))
                {
                    dMap.put(publicCommonMap.get(key), headMap.get("借款人"));

                }else
                {
                    dMap.put(publicCommonMap.get(key), headMap.get(key));
                }
            }
        }
        dMap.putAll(fileistruemap);
        dMap.putAll(cvmap);
        dMap.put("cguid",Guid.g());
        dMap.put("csourcebatchguid",sourceBatchCguid);
        dMap.put("collstatus", "1");
        dMap.put("datasource",CollectionUtil.getStringFromMap(cvmap,"csourcesysname"));
        dMap.put("collstatusname", "收集成功");
        Map<String, Map> billtype = BeanHelper.getBean(BillVoucherCollectDataService.class).billtype;
        Map billmap = billtype.get(headMap.get("页面id"));
        String billtypeid = CollectionUtil.getStringFromMap(billmap, "cguid");
        String billname = CollectionUtil.getStringFromMap(billmap, "billname");
        dMap.put("cdabilltypeid",billtypeid);
        dMap.put("cdabilltypename",billname);
        makestatusValue(dMap,isRelated);
        /*//组卷状态
        dMap.put("centityfilestatus", "0");
        dMap.put("centityfilestatus_name", "收集成功");
        //归档状态
        dMap.put("ceastatus", "0");
        dMap.put("ceastatus_name", "收集成功");
        //四性检测状态
        dMap.put("isuaistatus", "0");
        dMap.put("csuaistatus", "未检测");
        //手工关联状态
        dMap.put("imanualrelstatus", "0");
        dMap.put("cmanualrelstatus", "未关联");*/
        dMap.put("ctemplateid","da_data_collection_bills");

        dMap.put("isexistfile", isexistfile);
        String sybillid = CollectionUtil.getStringFromMap(dMap, "sybillid");
        String billid = CollectionUtil.getStringFromMap(dMap, "billid");

        if (sybillid.equalsIgnoreCase(billid))
        {
            dMap.put("ishead","1");
        }
        else
        {
            dMap.put("ishead","0");
        }

        dMap.putAll(makeBatchDefalultValue(cvmap));
        dMap.put("downloadfilenum", sum);
        daheadlist.add(dMap);
    }

    private static Map makestatusValue(Map dMap, boolean isRelated)
    {
        //组卷状态
        dMap.put("centityfilestatus", "0");
        dMap.put("centityfilestatus_name", "收集成功");
        //归档状态
        dMap.put("ceastatus", "0");
        dMap.put("ceastatus_name", "收集成功");
    /*    //四性检测状态
        dMap.put("isuaistatus", "0");
        dMap.put("csuaistatus", "未检测");*/
        //手工关联状态

        if (isRelated)
        {
            dMap.put("crelstatus", "1");
            dMap.put("crelstatus_name", "已关联");
            dMap.put("biscollectwithvou","1");
        }else
        {
            dMap.put("crelstatus", "0");
            dMap.put("crelstatus_name", "未关联");
            dMap.put("biscollectwithvou","0");

        }
        dMap.put("imanualrelstatus", "0");
        dMap.put("cmanualrelstatus", "未关联");

        dMap.put("isuaistatus","1");
        dMap.put("csuaistatus","已检测");

        //票据归档状态
        dMap.put("cbilleastatus", "0");
        dMap.put("cbilleastatus_name", "收集成功");


        return dMap;
    }

    public static Map converlog(Map headMap, HashMap<String, String> publicCommonMap, Map cvmap, String extcode, String msg, String serialnumber, String daheadcguid)
    {
        Map dMap = new HashMap<>();
        for (String key : publicCommonMap.keySet())
        {
            if("cdeptguid_name".equals(publicCommonMap.get(key)))
            {
                dMap.put("cdeptguid", headMap.get(key));
                dMap.put(publicCommonMap.get(key), headMap.get(key));
            }else
            {
                if ("报销人id".equalsIgnoreCase(key))
                {
                    dMap.put(publicCommonMap.get(key), headMap.get("报销人"));

                }else
                {
                    dMap.put(publicCommonMap.get(key), headMap.get(key));
                }
            }
        }
        dMap.putAll(cvmap);
        dMap.put("cguid",Guid.g());

        dMap.put("datasource",CollectionUtil.getStringFromMap(cvmap,"csourcesysname"));
        dMap.put("csyncexceptionmsg",msg);
        dMap.put("ctargetId", daheadcguid);

        Map<String, Map> billtype = BeanHelper.getBean(BillVoucherCollectDataService.class).billtype;
        Map billmap = billtype.get(headMap.get("页面id"));
        String billtypeid = CollectionUtil.getStringFromMap(billmap, "cguid");
        String billname = CollectionUtil.getStringFromMap(billmap, "billname");
        dMap.put("cdabilltypeid",billtypeid);
        dMap.put("cdabilltypename",billname);
        dMap.putAll(makeBatchDefalultValue(cvmap));
        return dMap;
    }

    public static void valueValiBankDate(HashMap<String, String> bankReceiptRequiredMap, HashMap<String, String> bankReceiptLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        Object headlist = billdetial.get("bankreceiptlist");
        if (isObjectNotEmpty(headlist) && headlist instanceof List)
        {
            List<Map> bankMap = (ArrayList)headlist;
            for (Map map : bankMap)
            {
                valueVoucherValidate(bankReceiptRequiredMap, bankReceiptLengthMap, map, stringBuffer);
            }
        }
    }

    public static void valueValiExtBankDate(HashMap<String, String> bankReceiptRequiredMap, HashMap<String, String> bankReceiptLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        Object headlist = enComParametersUtil.getCommonObjectValue(billdetial, "银行回执详情", "bankdetail");
        if (isObjectNotEmpty(headlist) && headlist instanceof List)
        {
            List<Map> bankMap = (ArrayList)headlist;
            for (Map map : bankMap)
            {
                valueVoucherValidate(bankReceiptRequiredMap, bankReceiptLengthMap, map, stringBuffer);
            }
        }
    }
    public static String converBankDetial(Map bankmap, HashMap<String, String> bankReceiptMap, Map cvmap, String blankid, List<Map> bankreceiptlist, String cempguidname, List<Map> bankAndVoucherRellist, boolean isRelated)
    {
        StringBuffer banksb = new StringBuffer();
        Map nHeadMap  = new HashMap<>();
        for (String key : bankReceiptMap.keySet())
        {
            Object o = bankmap.get(key);
            nHeadMap.put(bankReceiptMap.get(key),o);
            if (isObjectNotEmpty(o))
            {
                banksb.append(o).append(" ");
            }
        }
        nHeadMap.put("cguid", blankid);
        nHeadMap.put("cempguid", cempguidname);
        nHeadMap.put("ctemplateid", "da_data_collect_bankreceipt");
        nHeadMap.put("datasource",CollectionUtil.getStringFromMap(cvmap,"csourcesysname"));

        makestatusValue(nHeadMap, isRelated);
        nHeadMap.putAll(cvmap);
        bankreceiptlist.add(nHeadMap);

        databankAndVoucher(cvmap,bankAndVoucherRellist, nHeadMap);
        return banksb.toString();
    }
    public static String converExtBankDetial(Map bankmap, HashMap<String, String> bankReceiptMap, Map cvmap, String blankid, List<Map> bankreceiptlist, String cempguidname, Map sourceBatch, boolean isRelated, List<Map> bankAndVoucherRellist)
    {
        StringBuffer stringBuffer = new StringBuffer();

        Map nHeadMap  = new HashMap<>();
        for (String key : bankReceiptMap.keySet())
        {
            if (isObjectNotEmpty(bankmap.get(key)))
            {
                stringBuffer.append(bankmap.get(key)).append(" ");
            }
            nHeadMap.put(bankReceiptMap.get(key),bankmap.get(key));
        }
        nHeadMap.put("cguid", blankid);
        nHeadMap.put("cempguid", cempguidname);
        nHeadMap.put("ctemplateid", "da_data_collect_bankreceipt");
        nHeadMap.put("datasource",sourceBatch.get("csourcesysname"));
        if (isRelated)
        {
            nHeadMap.put("biscollectwithvou","1");
        }
        makestatusValue(nHeadMap, isRelated);
        makeExtDefalultValue(nHeadMap, sourceBatch);
        nHeadMap.putAll(cvmap);
        bankreceiptlist.add(nHeadMap);

        databankAndVoucher(cvmap,bankAndVoucherRellist, nHeadMap);

        return stringBuffer.toString();
    }


    public static Map makeExtDefalultValue(Map map, Map sourceBatch)
    {
        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");
        String corgnid_name = CollectionUtil.getStringFromMap(sourceBatch, "corgnid_name");
        String cadminorgnid = CollectionUtil.getStringFromMap(sourceBatch, "cadminorgnid");
        String cadminorgnid_name = CollectionUtil.getStringFromMap(sourceBatch, "cadminorgnid_name");
        String ccreatedate = CollectionStringUtil.getSysDateTime();
        String ccreatorid = CollectionUtil.getStringFromMap(sourceBatch, "ccreatorid");
        String ccreatorid_name = CollectionUtil.getStringFromMap(sourceBatch, "ccreatorid_name");
        String cserialnumber = CollectionUtil.getStringFromMap(sourceBatch, "cserialnumber");

        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate",ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);
        map.put("cserialnumber",cserialnumber);

        map.put("ccollectorid",ccreatorid);
        map.put("ccollectorid_name",ccreatorid_name);
        return map;
    }

    public static void valueValiInvoiceDate(HashMap<String, String> invoiceHeadRequiredMap, HashMap<String, String> invoiceHeadLengthMap, HashMap<String, String> invoiceBodyRequiredMap, HashMap<String, String> invoiceBodyLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        Object headlist = billdetial.get("invoicelist");
        if (isObjectNotEmpty(headlist) && headlist instanceof List)
        {
            List<Map> invoicelist = (List<Map>) headlist;
            for (Map headMap : invoicelist) {
                valueVoucherValidate(invoiceHeadRequiredMap, invoiceHeadLengthMap, headMap, stringBuffer);
                Object bodylist = headMap.get("invoicedetiallist");
                if (isObjectNotEmpty(bodylist) && bodylist instanceof List)
                {
                    List<Map> newbodylistlist = (List)bodylist;
                    for (Map bodyMap : newbodylistlist)
                    {
                        valueVoucherValidate(invoiceBodyRequiredMap, invoiceBodyLengthMap, bodyMap, stringBuffer);
                    }
                }
            }


        }
    }

    public static void valueValiExtInvoiceDate(HashMap<String, String> invoiceHeadRequiredMap, HashMap<String, String> invoiceHeadLengthMap, HashMap<String, String> invoiceBodyRequiredMap, HashMap<String, String> invoiceBodyLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        Object headlist = enComParametersUtil.getCommonObjectValue(billdetial, "票据详情","ticketlist");
        if (isObjectNotEmpty(headlist) && headlist instanceof List)
        {
            List<Map> invoicelist = (List<Map>) headlist;
            for (Map headMap : invoicelist) {
                valueVoucherValidate(invoiceHeadRequiredMap, invoiceHeadLengthMap, headMap, stringBuffer);
                Object bodylist = enComParametersUtil.getCommonObjectValue(headMap, "票据详情子表", "ticketbodylist");
                if (isObjectNotEmpty(bodylist) && bodylist instanceof List)
                {
                    List<Map> newbodylistlist = (List)bodylist;
                    for (Map bodyMap : newbodylistlist)
                    {
                        valueVoucherValidate(invoiceBodyRequiredMap, invoiceBodyLengthMap, bodyMap, stringBuffer);
                    }
                }
            }


        }
    }
    public static String converInvoiceHead(Map invoicemap, HashMap<String, String> invoiceHeadMap, Map cvmap, String invoiceheadid, List<Map> invoicelist, String cempguidname, Map headMap, List<Map> ticketAndVoucherRellist, boolean isRelated)
    {
        Map nHeadMap  = new HashMap<>();
        makestatusValue(nHeadMap, isRelated);
        nHeadMap.putAll(cvmap);
        StringBuffer stringBuffer = new StringBuffer();
        for (String key : invoiceHeadMap.keySet())
        {
            if (isObjectNotEmpty(invoicemap.get(key)))
            {
                stringBuffer.append(invoicemap.get(key)).append(" ");
            }
            if ("价税合计".equals(key)) {
                String cclass = CollectionUtil.getStringFromMap(invoicemap,"发票大类");
                if (CCLASSES.contains(cclass)) {
                    nHeadMap.put(invoiceHeadMap.get(key), invoicemap.get("总金额"));
                    continue;
                }
            }
            if (isObjectNotEmpty(invoiceHeadMap.get(key)))
            {
                nHeadMap.put(invoiceHeadMap.get(key),invoicemap.get(key));
            }
        }
        nHeadMap.put("cguid", invoiceheadid);
        nHeadMap.put("ctemplateid","da_data_collection_ticket");
        nHeadMap.put("cempguid",cempguidname);
        nHeadMap.put("datasource",CollectionUtil.getStringFromMap(cvmap,"csourcesysname"));
        nHeadMap.put("cbillcode",headMap.get("单据编号"));
        nHeadMap.put("cbillguid",headMap.get("单据id"));

        nHeadMap.put("biscollectwithvou","1");
        //跟随凭证的票据不记录文件数移除字段
        nHeadMap.remove("ifileqty");
        invoicelist.add(nHeadMap);
        String tdate = CollectionUtil.getStringFromMap(invoicemap, "开票日期");
        if (StringUtils.isNotEmpty(tdate) && tdate.length() > 6) {
            String[] ts = tdate.split("-");
            if (ts.length == 3) {
                nHeadMap.put("tyear", ts[0]);
                nHeadMap.put("tmonth", ts[1]);
            }
        }
        dataTicketAndVoucher(cvmap,ticketAndVoucherRellist,nHeadMap);
        return stringBuffer.toString();
    }

    public static String converExtInvoiceHead(Map invoicemap, HashMap<String, String> invoiceHeadMap, Map cvmap, String invoiceheadid, List<Map> invoicelist, String cempguidname, Map headMap, Map sourceBatch, boolean isRelated, List<Map> ticketAndVoucherRellist, List<Map> datailBsinessList)
    {
        Map nHeadMap  = new HashMap<>();

        nHeadMap.putAll(cvmap);
        //跟随凭证的票据不记录文件数移除字段
        nHeadMap.remove("ifileqty");
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();

        StringBuffer stringBuffer = new StringBuffer();
        for (String key : invoiceHeadMap.keySet())
        {
            if (isObjectNotEmpty(invoicemap.get(key)))
            {
                stringBuffer.append(invoicemap.get(key)).append(" ");
            }

            if ("价税合计".equals(key) || "itotal".equals(key)) {
                String cclass = enComParametersUtil.getCommonStringValue(invoicemap,"发票大类","cclass");
                if (CCLASSES.contains(cclass)) {
                    Object iamt = enComParametersUtil.getCommonObjectValue(invoicemap,"总金额","iamt");
                    nHeadMap.put(invoiceHeadMap.get(key), iamt);
                    continue;
                }
            }
            nHeadMap.put(invoiceHeadMap.get(key),invoicemap.get(key));

        }
        String tdate = enComParametersUtil.getCommonStringValue(invoicemap, "开票日期", "ddate");
        if (StringUtils.isNotEmpty(tdate) && tdate.length() > 6) {
            String[] ts = tdate.split("-");
            if (ts.length == 3) {
                nHeadMap.put("tyear", ts[0]);
                nHeadMap.put("tmonth", ts[1]);
            }
        }

        nHeadMap.put("cguid", invoiceheadid);
        nHeadMap.put("ctemplateid","da_data_collection_ticket");
        nHeadMap.put("cempguid",cempguidname);
        nHeadMap.put("datasource",sourceBatch.get("csourcesysname"));

        nHeadMap.put("cbillcode",enComParametersUtil.getCommonStringValue(headMap,"单据编号", "cbillcode"));

        makestatusValue(nHeadMap, isRelated);
        makeExtDefalultValue(nHeadMap,sourceBatch);

        //放入票据权限字段
        //档案职员
        //档案对照乘车人
        nHeadMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(nHeadMap,"passenger"),CollectionUtil.getStringFromMap(nHeadMap,"passengercode"),"user", "cdapassengerid", "cdapassenger"));
        //档案对照复核人
        nHeadMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(nHeadMap,"passenger"),CollectionUtil.getStringFromMap(nHeadMap,"passengercode"),"user","cdareviewerid","cdareviewer"));
        //档案对照收款人
        nHeadMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(nHeadMap,"passenger"),CollectionUtil.getStringFromMap(nHeadMap,"passengercode"),"user","cpayeeid","cpayee"));

        invoicelist.add(nHeadMap);
        dataTicketAndVoucher(cvmap,ticketAndVoucherRellist,nHeadMap);
        return stringBuffer.toString();
    }

    public static String converInvoiceBody(Map bodymap, HashMap<String, String> invoiceBodyMap, String invoiceheadid, List<Map> invoicebodylist, Map cvmap)
    {
        StringBuffer stringBuffer = new StringBuffer();
        Map nBodyMap  = new HashMap<>();
        for (String key : invoiceBodyMap.keySet())
        {
            if (isObjectNotEmpty(bodymap.get(key)))
            {
                stringBuffer.append(bodymap.get(key)).append(" ");;
            }
            nBodyMap.put(invoiceBodyMap.get(key),bodymap.get(key));
        }
        nBodyMap.put("cguid", Guid.g());
        nBodyMap.put("cheadguid",invoiceheadid);
        nBodyMap.putAll(makeBatchDefalultValue(cvmap));

        invoicebodylist.add(nBodyMap);

        return stringBuffer.toString();
    }

    public static String converExtInvoiceBody(Map bodymap, HashMap<String, String> invoiceBodyMap, String invoiceheadid, List<Map> invoicebodylist, Map sourceBatch)
    {
        Map nBodyMap  = new HashMap<>();
        StringBuffer stringBuffer = new StringBuffer();
        for (String key : invoiceBodyMap.keySet())
        {
            if (isObjectNotEmpty(invoiceBodyMap.get(key)))
            {
                stringBuffer.append(bodymap.get(key)).append(" ");
            }
            nBodyMap.put(invoiceBodyMap.get(key),bodymap.get(key));
        }
        nBodyMap.put("cguid", Guid.g());
        nBodyMap.put("cheadguid",invoiceheadid);
        makeExtDefalultValue(nBodyMap, sourceBatch);
        invoicebodylist.add(nBodyMap);
        return stringBuffer.toString();
    }

    public static void extconverfile(Map oldfileMap, List<Map> filelist, HashMap<String, String> daFileMap, String blankid, String invoiceid, Map cvmap, String dafilepath, Map sourceBatch, String interval, String cfilecontents)
    {
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        String storagemethod = enComParametersUtil.getCommonStringValue(oldfileMap,"存储方式","storagemethod");
        String mdnumber = enComParametersUtil.getCommonStringValue(oldfileMap, "文件哈希码", "hxcode");
        String chxtype = enComParametersUtil.getCommonStringValue(oldfileMap, "文件哈希类型", "chxtype");
        Map nfileMap  = new HashMap<>();
        nfileMap.put("cfilecontents", cfilecontents);
        for (String key : daFileMap.keySet())
        {

            if ("文件类型".equalsIgnoreCase(key) || "cfiletype".equalsIgnoreCase(key))
            {
                //文件来源类型(0凭证的文件，1单据的文件，2发票的文件(ocr识别文件，非ocr识别文件),3银行回单,4模拟打印文件,5其他， 6会计账簿， 7会计报告，8其他会计资料
                String cfilesourcetype = enComParametersUtil.getCommonStringValue(oldfileMap, "文件来源类型", "cfilesourcetype");
                if(!StringUtils.isBlank(cfilesourcetype))
                {
                    nfileMap.put(daFileMap.get(key),oldfileMap.get(key));
                }
            }else if ("文件收集方式".equalsIgnoreCase(key) || "filecollmethod".equalsIgnoreCase(key))
            {
                Object sjfs = oldfileMap.get(key);
                if (sjfs instanceof Number)
                {
                    if (sjfs instanceof Integer)
                    {
                        nfileMap.put(daFileMap.get(key), sjfs);
                    }else
                    {
                        Double aDouble = (Double) oldfileMap.get(key);
                        int i = aDouble.intValue();
                        nfileMap.put(daFileMap.get(key),i);
                    }

                }else
                {
                    nfileMap.put(daFileMap.get(key),sjfs);
                }

            }else if ("filename".equalsIgnoreCase(key) || "存储文件名".equalsIgnoreCase(key))
            {
                String filename = CollectionUtil.getStringFromMap(oldfileMap, key);
                //只有url形式传输可以截取文件名，其它形式不可以。。。
                if("url".equalsIgnoreCase(storagemethod)) {

                    if (StringUtils.isNotEmpty(mdnumber) && StringUtils.isNotEmpty(chxtype)) {
                        String[] split = filename.split("\\.");
                        String filetype = split[split.length - 1];
                        filename = chxtype + mdnumber + "." + filetype;
                    }
                }

                nfileMap.put(daFileMap.get(key),filename);

            }else if ("文件来源类型".equalsIgnoreCase(key) || "cfilesourcetype".equalsIgnoreCase(key))
            {
                if (StringUtils.isNotBlank(invoiceid))
                {
                    nfileMap.put(daFileMap.get(key),"2");
                }
                else if (StringUtils.isNotBlank(blankid))
                {
                    nfileMap.put(daFileMap.get(key),"3");
                }else
                {
                    nfileMap.put(daFileMap.get(key),"1");
                }
            }
            else if ("排序字段".equalsIgnoreCase(key) || "csortindex".equalsIgnoreCase(key))
            {
                if (isObjectEmpty(oldfileMap.get(key)))
                {
                    nfileMap.put(daFileMap.get(key),"0");
                }else
                {
                    nfileMap.put(daFileMap.get(key),oldfileMap.get(key));
                }
            }
            else
            {
                nfileMap.put(daFileMap.get(key),oldfileMap.get(key));

            }
        }
        if (StringUtils.isBlank(blankid) && StringUtils.isBlank(invoiceid))
        {
            nfileMap.put("cbusinesstype","1");
        }
        else if (StringUtils.isNotEmpty(invoiceid))
        {
            nfileMap.put("cbusinesstype","2");
        }
        else if (StringUtils.isNotEmpty(blankid))
        {
            nfileMap.put("cbusinesstype","3");
        }

        nfileMap.put("bankid", blankid);
        nfileMap.put("invoiceid", invoiceid);

        //只有local形式传输修改电子档案文件路径
        if ("local".equalsIgnoreCase(storagemethod)) {
            if(StringUtils.isNotEmpty(mdnumber) && StringUtils.isNotEmpty(chxtype)){
                dafilepath = dafilepath+"/"+chxtype+"/"+mdnumber;
            }
        }
        nfileMap.put("dafilepath", dafilepath);
/*        nfileMap.put("cstatus","9");
        nfileMap.put("cstatus_name","未收集,来自云费控数据");*/

        nfileMap.put("cstatus","0");
        nfileMap.put("cstatus_name","收集中");
        nfileMap.putAll(cvmap);
        nfileMap.put("cguid", Guid.g());

        String cfilerealname = CollectionUtil.getStringFromMap(nfileMap, "cfilerealname");
        String filename = CollectionUtil.getStringFromMap(nfileMap, "filename");

        if (StringUtils.isNotBlank(filename))
        {
            String carriertype = CollectionUtil.getStringFromMap(nfileMap, "carriertype");
            if (StringUtils.isBlank(carriertype))
            {
                nfileMap.put("carriertype", DaApiProFileUtil.isImageFile(filename));
            }
        }else if (StringUtils.isNotBlank(cfilerealname))
        {
            String carriertype = CollectionUtil.getStringFromMap(nfileMap, "carriertype");
            if (StringUtils.isBlank(carriertype))
            {
                nfileMap.put("carriertype", DaApiProFileUtil.isImageFile(cfilerealname));
            }
        }

        nfileMap.put("cserialnumber",sourceBatch.get("cserialnumber"));
        nfileMap.put("syssource",sourceBatch.get("csourcesysname"));

        nfileMap.put("datainterval", interval);

        String cfiletype = CollectionUtil.getStringFromMap(nfileMap, "cfiletype");
        if (StringUtils.isBlank(cfiletype))
        {
            nfileMap.put("cfiletype", "0");
        }
        filelist.add(nfileMap);
    }

    public static void converExtCommDetial(Map cvmap, List<Map> daheadlist, String sourceBatchCguid, String isexistfile, Map sourceBatch, boolean isRelated, List<Map> datailBsinessList)
    {
        Map dMap = new HashMap<>();
        dMap.putAll(cvmap);
        dMap.put("cguid",Guid.g());
        dMap.put("csourcebatchguid",sourceBatchCguid);
        dMap.put("collstatus", "1");
        dMap.put("datasource",CollectionUtil.getStringFromMap(sourceBatch,"csourcesysname"));
        dMap.put("collstatusname", "收集成功");
        makestatusValue(dMap, isRelated);
        /*//组卷状态
        dMap.put("centityfilestatus", "0");
        dMap.put("centityfilestatus_name", "收集成功");
        //归档状态
        dMap.put("ceastatus", "0");
        dMap.put("ceastatus_name", "收集成功");
        //四性检测状态
        dMap.put("isuaistatus", "0");
        dMap.put("csuaistatus", "未检测");
        //手工关联状态
        dMap.put("imanualrelstatus", "0");
        dMap.put("cmanualrelstatus", "未关联");*/
        dMap.put("ctemplateid","da_data_collection_bills");

        dMap.put("isexistfile", isexistfile);
        makeExtDefalultValue(dMap,sourceBatch);
        String cgroupnum = CollectionUtil.getStringFromMap(sourceBatch, "cgroupnum");
        if(StringUtils.isNotBlank(cgroupnum))
        {
            dMap.put("cgroupnum", cgroupnum);
        }

        //放入单据权限字段
        //档案职员
        dMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(dMap,"cempguid_name"),CollectionUtil.getStringFromMap(dMap,"cempgucode"),"user", "cdaempguid", "cdaempguid_name"));
        //档案制单人
        dMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(dMap,"ccreatorname"),CollectionUtil.getStringFromMap(dMap,"ccreatorcode"),"user", "cdacreatorid", "cdacreatorname"));
        //档案部门
        dMap.putAll(ArrayUtil.getQXmap(datailBsinessList, CollectionUtil.getStringFromMap(dMap,"cdeptguid_name"),CollectionUtil.getStringFromMap(dMap,"cdeptguidcode"),"dept", "cdadeptguid", "cdadeptguid_name"));
        daheadlist.add(dMap);
    }

    public static Map extbillJosn(Object headmap, Map cvmap, List<Map> billheadlist, Map sourceBatch)
    {
        Map billmap = new HashMap<>();
        String billjson = JsonUtil.toJSON(headmap);
        billmap.put("cbilljson", billjson);
        billmap.putAll(cvmap);
        makeExtDefalultValue(billmap,sourceBatch);
        billmap.put("cguid", Guid.g());
        billheadlist.add(billmap);

        return billmap;
    }

    public static void converexttree(Map cvmap, List<Map> treelist, HashMap<String, String> treePublicMap, HashMap<String, String> treePrivateMap, String ctype, String url, Map sourceBatch, String headid)
    {
        Map tMap = new HashMap<>();

        for (String key : treePublicMap.keySet())
        {
            if (key == "cparent")
            {
                String cparent = CollectionUtil.getStringFromMap(cvmap, "cvoucherid");
                if (StringUtils.isBlank(cparent))
                {
                    tMap.put(key, "000000");
                }else
                {
                    tMap.put(key, cvmap.get(treePublicMap.get(key)));
                }
            }
            else
            {
                tMap.put(key, cvmap.get(treePublicMap.get(key)));
            }
        }

        for (String key : treePrivateMap.keySet())
        {
            if (!"2".equalsIgnoreCase(ctype)  && ! "cparent".contains(key))
            {
                if ("cmeansname".equalsIgnoreCase(key))
                {
                    tMap.put(key, treePrivateMap.get(key));
                }else
                {
                    tMap.put(key, cvmap.get(treePrivateMap.get(key)));
                }
            }
        }
        String cfilesTableName = CollectionUtil.getStringFromMap(cvmap, "cfiles_table_name");
        String billname = CollectionUtil.getStringFromMap(cvmap, "billname");
        //tMap.put("cdabilltypeid",billtypeid);
        tMap.put("cmeansname",billname);

        tMap.put("cfiletablename", cfilesTableName);
        tMap.put("ctype", ctype);
        tMap.put("cguid",Guid.g());
        tMap.put("pageurl",url);
        makeExtDefalultValue(tMap,sourceBatch);
        tMap.put("extbilldetialid",headid);
        tMap.put("extcode",cvmap.get("extcode"));
        tMap.put("billtablename",cvmap.get("exttablename"));
        treelist.add(tMap);
    }

    public static void converbillAndVoucher(Map cvmap, List<Map> billAndVoucherRellist, Map sourceBatch)
    {
        Map bMap = new HashMap<>();
        bMap.put("cbillid", cvmap.get("billid"));
        bMap.put("cvoucherid", cvmap.get("cvoucherid"));
        bMap.put("cvoucode", cvmap.get("cvoucherno"));
        bMap.put("cbillcode", cvmap.get("cbillcode"));
        bMap.put("cguid", Guid.g());
        makeExtDefalultValue(bMap,sourceBatch);
        billAndVoucherRellist.add(bMap);

    }

    public static void databillAndVoucher(Map cvmap, List<Map> billAndVoucherRellist, Map billdetial)
    {
        Map bMap = new HashMap<>();
        Object headlist = billdetial.get("headlist");
        if (headlist != null && headlist instanceof Map)
        {
            Map headMap = (Map)headlist;

            bMap.put("cbillid", headMap.get("单据id"));
            bMap.put("cbillcode", headMap.get("单据编号"));
        }else
        {
            bMap.put("cbillid", cvmap.get("billid"));
            bMap.put("cbillcode", cvmap.get("cbillcode"));

        }

        bMap.put("cvoucherid", cvmap.get("cvoucherid"));
        bMap.put("cvoucode", cvmap.get("cvoucherno"));
        bMap.put("cguid",Guid.g());
        bMap.putAll(makeBatchDefalultValue(cvmap));
        billAndVoucherRellist.add(bMap);
    }

    public static void valueValiBillDateAndBodylist(HashMap<String, String> voucherRequiredMap, HashMap<String, String> billHeadRequiredMap, HashMap<String, String> billBodyRequiredMap, HashMap<String, String> billBodyRequiredMap2, HashMap<String, String> billHeadLengthMap, HashMap<String, String> billBodyLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        //校验外层凭证信息
        valueVoucherValidate(voucherRequiredMap, billHeadLengthMap, billdetial, stringBuffer);
        //校验单据主表信息
        valuBillHeadValidate2(billHeadRequiredMap, billHeadLengthMap, billdetial, stringBuffer);
        //检验单据子表信息
        valuBillBodyValidate(billBodyRequiredMap, billBodyLengthMap, billdetial, stringBuffer, "detailedbody");

        valuBillBodyValidate(billBodyRequiredMap2, billBodyLengthMap, billdetial, stringBuffer, "expensebody");

    }

    private static void valuBillBodyValidate(HashMap<String, String> billBodyRequiredMap, HashMap<String, String> billBodyLengthMap, Map billdetial, StringBuffer stringBuffer, String  fieldName )
    {
        Object headlist = billdetial.get("headlist");
        if (isObjectNotEmpty(headlist) && headlist instanceof Map)
        {
            Map headMap = (Map)headlist;
            StringBuffer heads = new StringBuffer();
            Object bodylist = headMap.get(fieldName);
            if (isObjectNotEmpty(bodylist) && bodylist instanceof List)
            {
                List<Map> newbodylistlist = (List)bodylist;
                StringBuffer bodys = new StringBuffer();
                for (Map bodyMap : newbodylistlist)
                {
                    valueVoucherValidate(billBodyRequiredMap, billBodyLengthMap, bodyMap, bodys);
                }
                if (bodys.length() > 0)
                {
                    stringBuffer.append("单据子表表验证信息【").append(bodys).append("】");
                }
            }
            else
            {
                stringBuffer.append("获取单据子表信息为空");
            }
        }
        else
        {
            stringBuffer.append("获取单据信息为空");
        }
    }

    private static void valuBillHeadValidate2(HashMap<String, String> billHeadRequiredMap, HashMap<String, String> billHeadLengthMap, Map billdetial, StringBuffer stringBuffer)
    {
        Object headlist = billdetial.get("headlist");
        if (isObjectNotEmpty(headlist) && headlist instanceof Map)
        {
            Map headMap = (Map)headlist;
            StringBuffer heads = new StringBuffer();
            valueVoucherValidate(billHeadRequiredMap, billHeadLengthMap, headMap, heads);
            if (heads.length() > 0)
            {
                stringBuffer.append("单据主表验证信息【").append(heads).append("】");
            }
        }
        else
        {
            stringBuffer.append("获取单据信息为空");
        }
    }

    public static String SXCheck(Map param)
    {
        String dsid = CollectionUtil.getStringFromMap(param, "dsid");
        if(StringUtils.isBlank(dsid))
        {
            DbService dbService = new ApsContextDb().getDb(dsid);
            param.put("dsid", dbService.getDsID());
        }
        Message ms = new Message("da.suai.precheck");
        Map rtn = (Map)ms.publish(param);
        if (CollectionUtil.isBlankMap(rtn))
        {
            return "调用四性检测失败,数据无法入库,请稍后重新尝试收集";
        }else
        {
            Object suairesult = rtn.get("suairesult");
            if (isObjectEmpty(suairesult))
            {
                return "调用四性检测失败,获取四性检测结果失败";
            }else
            {
                if (suairesult instanceof List)
                {
                    List<Map> suailist= (List<Map>)suairesult;
                    StringBuffer stringBuffer = new StringBuffer();
                    for (Map sxmap : suailist)
                    {
                        boolean pass = CollectionUtil.getBoolean(sxmap, "pass", false);
                        if (!pass)
                        {
                            if (stringBuffer.length() > 0)
                            {
                                stringBuffer.append(";");
                            }
                            String msg = CollectionUtil.getStringFromMap(sxmap, "msg");
                            stringBuffer.append(msg);
                        }

                    }
                    return stringBuffer.toString();
                }else
                {
                    return "调用四性检测成功,无法解析报文,请联系管理员【" + suairesult.toString() + "】";
                }
            }
        }
    }

    public static Map getSXMap(Map datamap, Map batchmap, String caccountclass, List<Map> filelist, String dsid)
    {
        Map sxmap = new HashMap<>();
        String cbillcode = CollectionUtil.getStringFromMap(datamap, "cbillcode");
        String billid = CollectionUtil.getStringFromMap(datamap, "oldcguid");
        sxmap.put("caccountclass", caccountclass);
        sxmap.put("dsid", dsid);
        sxmap.put("currentOrgnId", CollectionUtil.getStringFromMap(batchmap, "corgnid"));
        sxmap.put("currentOrgnName", CollectionUtil.getStringFromMap(batchmap, "corgnid_name"));
        sxmap.put("currentAdminOrgnId", CollectionUtil.getStringFromMap(batchmap, "cadminorgnid"));
        sxmap.put("currentAdminOrgnName", CollectionUtil.getStringFromMap(batchmap, "cadminorgnid_name"));
        sxmap.put("currentUserId", CollectionUtil.getStringFromMap(batchmap, "ccollector"));
        sxmap.put("currentUserName", CollectionUtil.getStringFromMap(batchmap, "ccollector_name"));

        List filel = new ArrayList<>();
        Map filemap = new HashMap<>();
        filemap.put("billid", billid);
        filemap.put("cbillcode", cbillcode);

        filemap.put("ifileqty", datamap.get("ifileqty"));

        filemap.put("fileList", filelist);
        filel.add(filemap);
        sxmap.put("list", filel);
        return sxmap;
    }


    /**
     * 更新凭证关联关系
     * @param cguid
     */
    public static void updateSUAIStatusMs(String cguid)
    {
        if (StringUtils.isNotEmpty(cguid))
        {
            List<String> list = new ArrayList();
            list.add(cguid);
            publishSUAIStatusMs(list);
        }
    }

    public static void updateIntegrityStatusUpdateMs(Map map)
    {
        Message ms = new Message("da.bbs.IntegrityStatusUpdateMs");
        List<Map> objects = new ArrayList<>();
        objects.add(map);
        Object rtn = ms.publish(objects);
    }

    private static void publishSUAIStatusMs(List<String> list){
        String ctablename = "da_api_fk_file_collection";
        Message ms = new Message("da.suai.updaterelstatusrrecollectms");
        Map par=new HashMap();
        par.put("ctablename", ctablename);
        par.put("cvoucherids", list);
        Object rtn = ms.publish(par);
    }

    public static Map getDetialEnMap(Map details, HashMap<String, String> billPublicMap, Map logmap)
    {
        Map cvmap = new HashMap<>();
        for (String key : billPublicMap.keySet())
        {
            if (key == "分表字段")
            {
                String sybilldate = CollectionUtil.getStringFromMap(details, "sybilldate");
                if (StringUtils.isNotBlank(sybilldate) && sybilldate.length() > 4)
                {
                    String year = "da_files_" + sybilldate.substring(0, 4);
                    cvmap.put(billPublicMap.get(key),year);
                }else
                {
                    sybilldate = CollectionUtil.getStringFromMap(details, "ddate");
                    if (StringUtils.isBlank(sybilldate))
                    {
                        sybilldate = getSysDate();
                    }
                    String year = "da_files_" + sybilldate.substring(0, 4);
                    cvmap.put(billPublicMap.get(key),year);
                }

            }else if("iyearandmonth".equals(key))
            {
                String iyear = CollectionUtil.getStringFromMap(details, "iyear");
                String imonth = CollectionUtil.getStringFromMap(details, "imonth");
                String pzdate = CollectionUtil.getStringFromMap(details, "cvoucherdate");

                String tdate = CollectionUtil.getStringFromMap(details, "ddate");
                if (StringUtils.isNotEmpty(tdate) && tdate.length() > 6)
                {
                    String[] ts = tdate.split("-");
                    if(ts.length == 3)
                    {
                        cvmap.put("tyear", ts[0]);
                        cvmap.put("tmonth", ts[1]);
                    }
                }

                if (StringUtils.isNotEmpty(iyear) && StringUtils.isNotEmpty(imonth))
                {
                    cvmap.put(billPublicMap.get(key), iyear + "-" + imonth);
                }else
                {
                    if (StringUtils.isNotEmpty(pzdate))
                    {
                        String[] split = pzdate.split("-");
                        if(split.length == 3)
                        {
                            iyear = split[0];
                            imonth = split[1];
                            cvmap.put(billPublicMap.get("iyear"), iyear);
                            cvmap.put(billPublicMap.get("imonth"), imonth);
                            cvmap.put(billPublicMap.get(key), iyear + "-" + imonth);
                        }
                    }
                }
            }
            else
            {
                if (isObjectNotEmpty(details.get(key)))
                {
                    cvmap.put(billPublicMap.get(key),details.get(key));
                }
            }
        }
        String billdate = CollectionUtil.getStringFromMap(details, "ddate");
        String billInterval = ArrayUtil.getDataInterval(billdate);
        Object headlist = details.get("head");

        if (headlist != null && headlist instanceof Map && StringUtils.isBlank(billdate))
        {
            Map headMap = (Map)headlist;
            //获取主单据日期
            billdate = CollectionUtil.getStringFromMap(headMap, "ddate");
            billInterval = ArrayUtil.getDataInterval(billdate);
        }
        //单据资料增加资料区间
        cvmap.put("datainterval", billInterval);
        cvmap.putAll(makeBatchDefalultValue(logmap));
        return cvmap;
    }


}
