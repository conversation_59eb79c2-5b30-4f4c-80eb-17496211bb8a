package com.aisino.da.fc.ms.bankreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.util.ImportUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/09/14/08:43
 */
@Ms(value = "da_fc_bankstatement_hs", desp = "徽商银行流水导入保存前")
public class BankstatementImportHS implements Ims {

    private static Log log = LogFactory.getLog(BankstatementImportHS.class);

    @Override
    public Object doMessage(Object... objects) {
        try {
            int startIndex = 1;//起始行
            Map<String,Object> templateMap = (Map) objects[1];
            String startIndexStr = templateMap.get("istartline")==null?"": templateMap.get("istartline").toString();
            if(StringUtil.isNotEmpty(startIndexStr)){
                startIndex = Integer.parseInt(startIndexStr);
            }
            Map<String,Object> map = (Map) objects[0];
            List<Map> importList = new ArrayList();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                importList = map.get(key)==null?new ArrayList():(List)map.get(key);
                break;
            }
            if(CollectionUtil.isNotEmpty(importList)&&importList.size()>=startIndex+1){
                ImportUtil.replaceBlank(importList);
                Map<Integer,String> headMap = importList.get(startIndex-1);//列名数据
                int headSize = headMap.size();
                int srjeIndex = 2;//收入金额
                int zcjeIndex = 3;//支出金额
                for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                    int key = entry.getKey();
                    String value = headMap.get(key);
                    if(StringUtil.isNotEmpty(value)&&value.contains("收入金额")){
                        srjeIndex = key;
                    }
                    if(StringUtil.isNotEmpty(value)&&value.contains("支出金额")){
                        zcjeIndex = key;
                    }
                }
                headMap.put(headSize,"交易金额");
                headMap.put(headSize+1,"收支类别");
                for(int i = startIndex;i<importList.size();i++){
                    Map<Integer,Object> bodyMap = importList.get(i);
                    String srjeStr = bodyMap.get(srjeIndex)==null?"0":bodyMap.get(srjeIndex).toString();
                    String zcjeStr = bodyMap.get(zcjeIndex)==null?"0":bodyMap.get(zcjeIndex).toString();
                    srjeStr = srjeStr.replaceAll(",","");
                    zcjeStr = zcjeStr.replaceAll(",","");
                    if(StringUtil.isEmpty(srjeStr)){
                        srjeStr = "0";
                    }if(StringUtil.isEmpty(zcjeStr)){
                        zcjeStr = "0";
                    }
                    double srje = 0;
                    //判断金额类型
                    if(ImportUtil.isNumeric(srjeStr)) {
                        srje = Double.parseDouble(srjeStr);
                    }
                    if(srje !=0){
                        //取‘收入金额’或‘支出金额’不为空的数据
                        bodyMap.put(headSize, srjeStr);
                        bodyMap.put(headSize+1, "收入");
                    }else{
                        bodyMap.put(headSize, zcjeStr);
                        bodyMap.put(headSize+1, "支出");
                    }
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return objects[0];
    }
}
