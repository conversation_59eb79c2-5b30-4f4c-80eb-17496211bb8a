package com.aisino.da.fc.ms.bankreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.util.ImportUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/09/11/15:43
 */
@Ms(value = "da_fc_building_temp", desp = "建设银行流水导入保存前")
public class BankstatementImportCBC implements Ims {

    private static Log log = LogFactory.getLog(BankstatementImportCBC.class);

    @Override
    public Object doMessage(Object... objects) {
        try {
            int startIndex = 1;//起始行
            Map<String,Object> templateMap = (Map) objects[1];
            String startIndexStr = templateMap.get("istartline")==null?"": templateMap.get("istartline").toString();
            if(StringUtil.isNotEmpty(startIndexStr)){
                startIndex = Integer.parseInt(startIndexStr);
            }
            Map<String,Object> map = (Map) objects[0];
            List<Map> importList = new ArrayList();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                importList = map.get(key)==null?new ArrayList():(List)map.get(key);
                break;
            }
            if(CollectionUtil.isNotEmpty(importList)&&importList.size()>=startIndex+1){
                ImportUtil.replaceBlank(importList);
                Map<Integer,String> headMap = importList.get(startIndex-1);//列名数据
                int headSize = headMap.size();
                int jffseIndex = 3;//借方发生额（支取）
                int dffseIndex = 4;//贷方发生额（收入）
                for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                    int key = entry.getKey();
                    String value = headMap.get(key);
                    if("借方发生额（支取）".equals(value)){
                        jffseIndex = key;
                    }
                    if("贷方发生额（收入）".equals(value)){
                        dffseIndex = key;
                    }
                }
                headMap.put(headSize,"交易金额");
                headMap.put(headSize+1,"收支类别");
                for(int i = startIndex;i<importList.size();i++){
                    Map<Integer,Object> bodyMap = importList.get(i);
                    String jffseStr = bodyMap.get(jffseIndex)==null?"0":bodyMap.get(jffseIndex).toString();
                    String dffseStr = bodyMap.get(dffseIndex)==null?"0":bodyMap.get(dffseIndex).toString();
                    jffseStr = jffseStr.replaceAll(",","");
                    dffseStr = dffseStr.replaceAll(",","");
                    double jffse = 0;
                    //判断金额类型
                    if(ImportUtil.isNumeric(jffseStr)) {
                        jffse = Double.parseDouble(jffseStr);
                    }
                    if(jffse!=0){
                        //取‘借方发生额（支取）’或‘贷方发生额（收入）’不为0的数据
                        //交易金额取借方发生额（支取）时，为支取;交易金额取贷方发生额（收入）时，为收入
                        bodyMap.put(headSize,jffseStr);
                        bodyMap.put(headSize+1,"支出");
                    }else{
                        bodyMap.put(headSize,dffseStr);
                        bodyMap.put(headSize+1,"收入");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return objects[0];
    }
}
