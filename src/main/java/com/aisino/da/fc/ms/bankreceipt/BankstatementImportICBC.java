package com.aisino.da.fc.ms.bankreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.util.ImportUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/09/11/11:43
 */
@Ms(value = "da_fc_bankstatement_icbc", desp = "工商银行流水导入保存前")
public class BankstatementImportICBC implements Ims {

    private static Log log = LogFactory.getLog(BankstatementImportICBC.class);

    @Override
    public Object doMessage(Object... objects) {
        try {
            int startIndex = 2;//起始行
            Map<String,Object> templateMap = (Map) objects[1];
            String startIndexStr = templateMap.get("istartline")==null?"": templateMap.get("istartline").toString();
            if(StringUtil.isNotEmpty(startIndexStr)){
                startIndex = Integer.parseInt(startIndexStr);
            }
            Map<String,Object> map = (Map) objects[0];
            List<Map> importList = new ArrayList();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                importList = map.get(key)==null?new ArrayList():(List)map.get(key);
                break;
            }
            if(CollectionUtil.isNotEmpty(importList)&&importList.size()>=startIndex+1){
                ImportUtil.replaceBlank(importList);
                Map<Integer,String> headMap = importList.get(startIndex-1);//列名数据
                int headSize = headMap.size();
                int zcjeIndex = 5;//转出金额
                int zrjeIndex = 6;//转入金额
                for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                    int key = entry.getKey();
                    String value = headMap.get(key);
                    if(StringUtil.isNotEmpty(value)&&value.contains("转出金额")){
                        zcjeIndex = key;
                    }
                    if(StringUtil.isNotEmpty(value)&&value.contains("转入金额")){
                        zrjeIndex = key;
                    }
                }
                headMap.put(headSize,"交易金额");
                for(int i = startIndex;i<importList.size();i++){
                    Map<Integer,Object> bodyMap = importList.get(i);
                    String zcjeStr = bodyMap.get(zcjeIndex)==null?"0":bodyMap.get(zcjeIndex).toString();
                    String zrjeStr = bodyMap.get(zrjeIndex)==null?"0":bodyMap.get(zrjeIndex).toString();
                    zcjeStr = zcjeStr.replaceAll(",","").replaceAll("[\t\n\r]", "");
                    zrjeStr = zrjeStr.replaceAll(",","").replaceAll("[\t\n\r]", "");
                    if(StringUtil.isEmpty(zcjeStr)){
                        zcjeStr = "0";
                    }if(StringUtil.isEmpty(zrjeStr)){
                        zrjeStr = "0";
                    }
                    double zcje = 0;
                    //判断金额类型
                    if(ImportUtil.isNumeric(zcjeStr)&&ImportUtil.isNumeric(zrjeStr)) {
                        zcje = Double.parseDouble(zcjeStr);
                    }
                    if(zcje!=0){
                        //取转出金额或转入金额不为空的数据
                        bodyMap.put(headSize,zcjeStr);
                    }else{
                        bodyMap.put(headSize,zrjeStr);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return objects[0];
    }
}
