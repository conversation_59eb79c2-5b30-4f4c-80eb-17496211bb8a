package com.aisino.da.fc.ms.filecollect.add;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.bd.util.BigDecimalUtil;
import com.aisino.aps.common.exception.ApsPreciseException;
import com.aisino.aps.db.metadata.utils.DbMetaUtil;
import com.aisino.aps.db.metadata.vo.DbTableType;
import com.aisino.da.bbs.action.integrity.IntegrityRuleCheckAction;
import com.aisino.da.fc.dao.filecollect.CollectDataDaoInterface;
import com.aisino.da.fc.util.StaticCodeClass;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * FinBaseIMTemplate
 *
 * <AUTHOR>
 * @version 1.0, 2024/6/7
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public abstract class FinBaseIMTemplate {
    @Inject
    public ApsContextDb db;
    @Inject
    public CollectDataDaoInterface collectDataDaoInterface;
    @Inject
    private IntegrityRuleCheckAction integrityRuleCheckAction;
    public abstract void putSomeListColChildrenVal(Map map, StringBuffer rtnMsg);
    public void valueLengthVal(Map map, String table, List<ApsPreciseException> apsPreciseExceptions,
                                    HashMap<Object, Object> tableColumnMap,HashMap<String, String> allFieldsMap){
        for (String key : allFieldsMap.keySet()) {
            valueLengthValidate(key,map,table, apsPreciseExceptions,tableColumnMap,allFieldsMap);
        }
    }
    private void valueLengthValidate(String key, Map map, String table, List<ApsPreciseException> apsPreciseExceptions,
                                     HashMap<Object, Object> tableColumnMap,HashMap<String, String> allFieldsMap){
        if(map.containsKey(key)){
            String checkInfo = allFieldsMap.get(key);
            String value = CollectionUtil.getStringFromMap(map,key);
            if(value!=null&&!"int".equals(checkInfo)&&!"21,9".equals(checkInfo)){//数字 金额不检长度
                if(StringUtils.isNotBlank(checkInfo)){
                    Integer length = Integer.valueOf(checkInfo);
                    int lengthValue = value.toString().length();
                    int sub = lengthValue - length;
                    if(sub>0){//长度报错
                        addApsPreciseException(apsPreciseExceptions,map,table,key,tableColumnMap.get(key)+"超出处理范围");
                    }
                }else {
                    //fieldsLengthErrorMsg.append(key+"报文接口定义中长度为空;");
                }
            }
        }
    }
    public void addApsPreciseException(List<ApsPreciseException> apsPreciseExceptions,Map map,String table,String key,String msg) {
        if(apsPreciseExceptions!=null){
            String v = CollectionUtil.getStringFromMap(map,key);
            String rowid = CollectionUtil.getStringFromMap(map,"rowid");
            if("da_fc_md_voucher".equals(table)){
                try{
                    rowid = rowid.split(",")[0];
                }catch (Exception e){
                }
            }
            ApsPreciseException apsPreciseException = new ApsPreciseException(table, key, v, msg, "", rowid, null);
            apsPreciseExceptions.add(apsPreciseException);
        }
    }
    public HashMap<String, String> makeAllFieldsMap() {
        HashMap<String, String> allFieldsMap = new HashMap<>();
        //长度和类型
        allFieldsMap.put("csourceorganname","200");
        allFieldsMap.put("csourceorganid","50");
        allFieldsMap.put("cvoucherid","50");
        allFieldsMap.put("cvoucherdate","23");
        allFieldsMap.put("cperioddate","35");
        allFieldsMap.put("cvoucode","int");
        allFieldsMap.put("cvoucreatorname","200");
        allFieldsMap.put("creviewer","200");
        allFieldsMap.put("cpostdate","100");
        allFieldsMap.put("cauditorname","200");
        allFieldsMap.put("cpostername","200");
        allFieldsMap.put("cvoutypename","200");
        allFieldsMap.put("iaffix","int");
        allFieldsMap.put("icredittotal","21,9");
        allFieldsMap.put("idebittotal","21,9");
        allFieldsMap.put("cvoucherid","50");
        allFieldsMap.put("cvoucherline","50");
        allFieldsMap.put("csummary","65535");
        allFieldsMap.put("cacctstr","500");
        allFieldsMap.put("iqty","int");
        allFieldsMap.put("cuomname","200");
        allFieldsMap.put("iunitprice","21,9");
        allFieldsMap.put("iamt_f","21,9");
        allFieldsMap.put("ccurname","200");
        allFieldsMap.put("iexchangerate","21,9");
        allFieldsMap.put("idebitamt","21,9");
        allFieldsMap.put("icreditamt","21,9");
        allFieldsMap.put("cdescribe1","255");
        allFieldsMap.put("cdescribe2","255");
        allFieldsMap.put("cdescribe3","255");
        allFieldsMap.put("cvouassisitstr","900");
        return allFieldsMap;
    }
    private String getPageurl(String pageIdStr, String name) {
        return "page/commonvchr/" + pageIdStr+"%s?origin=1&singleTab=1&cstate=view&name=" + name;
    }
    public Map makeVouAndBillRelaMap(Map listMap, String metaDataGuid) {
        /**预览表*/
        HashMap<Object, Object> vouAndBillRelaMap = new HashMap<>();
        String pageurl = getPageurl("da_fin_md_voucher/da_fin_md_voucher/","记账凭证");
        makeDefalultValue(vouAndBillRelaMap);
        String cvoucherid = CollectionUtil.getStringFromMap(listMap, "cvoucherid");
        String clistguid = CollectionUtil.getStringFromMap(listMap, "cguid");
        vouAndBillRelaMap.put("cvoucherid",cvoucherid);
        vouAndBillRelaMap.put("ctype","0");
        vouAndBillRelaMap.put("cmeansid",cvoucherid);
        vouAndBillRelaMap.put("cserialnumber",listMap.get("cserialnumber"));
        vouAndBillRelaMap.put("cmeanscode",listMap.get("cvoucode"));
        vouAndBillRelaMap.put("cmeansname",listMap.get("cvoutypename"));
        vouAndBillRelaMap.put("cfiletablename",listMap.get("cfiles_table_name"));
        vouAndBillRelaMap.put("clistguid",clistguid);
        vouAndBillRelaMap.put("cparent","000000");
        vouAndBillRelaMap.put("pageurl",String.format(pageurl,metaDataGuid));
        return vouAndBillRelaMap;
    }
    public void setSumAMT(Map map,Map sumMap){
        String idebitamtSumStr = CollectionUtil.getStringFromMap(sumMap, "idebittotal");
        String icreditamtSumStr = CollectionUtil.getStringFromMap(sumMap, "icredittotal");
        BigDecimal idebitamtSum = BigDecimalUtil.getBigDecimal(idebitamtSumStr);
        BigDecimal icreditamtSum = BigDecimalUtil.getBigDecimal(icreditamtSumStr);
        String idebitamt = CollectionUtil.getStringFromMap(map, "idebitamt");
        String icreditamt = CollectionUtil.getStringFromMap(map, "icreditamt");
        idebitamtSum = idebitamtSum.add(BigDecimalUtil.getBigDecimal(idebitamt));
        icreditamtSum = icreditamtSum.add(BigDecimalUtil.getBigDecimal(icreditamt));
        sumMap.put("idebittotal",idebitamtSum);
        sumMap.put("icredittotal",icreditamtSum);
    }
    public void checkSumAMT(Map sumMap){
        String idebitamtSumStr = CollectionUtil.getStringFromMap(sumMap, "idebittotal");
        String icreditamtSumStr = CollectionUtil.getStringFromMap(sumMap, "icredittotal");
        BigDecimal idebitamtSum = BigDecimalUtil.getBigDecimal(idebitamtSumStr);
        BigDecimal icreditamtSum = BigDecimalUtil.getBigDecimal(icreditamtSumStr);
        if(idebitamtSum.compareTo(icreditamtSum)!=0){
            throw new BusinessException("借贷金额合计不相等");
        }
    }
    public Boolean vouUniquenessVal(String businessPrimaryKeyStrManualVoucher,Map listMap){
        String[] split = businessPrimaryKeyStrManualVoucher.split(",");
        String[] par = new String[split.length];
        for (int i = 0; i < split.length; i++) {
            String v = CollectionUtil.getStringFromMap(listMap, split[i]);
            par[i] = v;
        }
        return db.checkExists("da_fc_voucherinfo",businessPrimaryKeyStrManualVoucher,par);
    }
    public Boolean vouUniquenessValUseGuid(String businessPrimaryKeyStrManualVoucher,Map listMap,String clistguid){

        String[] split = businessPrimaryKeyStrManualVoucher.split(",");
        Params params = new Params();
        for (String s : split) {
            String v = CollectionUtil.getStringFromMap(listMap, s);
            params.put(s,v);
        }
        Map map = collectDataDaoInterface.queryVoucherGuid(params);
        String cguid = CollectionUtil.getStringFromMap(map, "cguid");
        if(cguid!=null){
            return clistguid.equals(cguid);
        }else {
            return true;
        }
    }
    public Boolean vouUniquenessVal1(String businessPrimaryKeyStrManualVoucher,String[] splitV){
        String[] par = new String[splitV.length];
        for (int i = 0; i < splitV.length; i++) {
            String v = splitV[i];
            if(StringUtils.isNotBlank(v)){
                par[i] = v;
            }
        }
        return db.checkExists("da_fc_voucherinfo",businessPrimaryKeyStrManualVoucher,par);
    }
    public HashMap<Object, Object> makeListMap(Map daFcMdVoucherMainMap){
        //列表数据
        String cvoucode = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cvoucode");//元数据主表-凭证编号
        String cvoucodestr = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cvoucodestr");//元数据主表-凭证字号
        HashMap<Object, Object> listMap = new HashMap<>();
        listMap.putAll(daFcMdVoucherMainMap);
        listMap.put("ivoucode",cvoucode);
        listMap.put("cvoucode",cvoucodestr);
        listMap.put("cpageid","da_fc_manualvoucherinfo_list");
        listMap.put("ctemplateid","da_fc_manualvoucherinfo_list_template");
        listMap.put("cserialnumber","shoulu");
        return listMap;
    }
    public StringBuffer mappingRelationshipVal(Map row,Map metaDataDetailMap){
        //权限控制
        //元数据主表 审核人 复核人参数冗余到列表，便于转换id（mappingRelationshipVal）
        String creviewer = CollectionUtil.getStringFromMap(metaDataDetailMap, "creviewer");
        String cauditorname = CollectionUtil.getStringFromMap(metaDataDetailMap, "cauditorname");
        row.put("creviewer",creviewer);
        row.put("cauditorname",cauditorname);
        //转换 审核人 复核人 记账人 制单人
        Map<String, List<Map>> detailBusinessByType = collectDataDaoInterface.getDetailBusiness();
        StringBuffer rtnMsg = new StringBuffer();
        //制单人名称
        mappingRelationshipValPart(row,detailBusinessByType,rtnMsg,StaticCodeClass.voucreator,"cvoucreatorname","cdavoucreatorid","cdavoucreatorname");
        //审核人名称
        mappingRelationshipValPart(row,detailBusinessByType,rtnMsg,StaticCodeClass.auditor,"cauditorname","cdaauditorid","cdaauditorname");
        //复核人名称
        mappingRelationshipValPart(row,detailBusinessByType,rtnMsg,StaticCodeClass.reviewer,"creviewer","cdareviewerid","cdareviewername");
        //记账人名称
        mappingRelationshipValPart(row,detailBusinessByType,rtnMsg,StaticCodeClass.poster,"cpostername","cdaposterid","cdapostername");

        return rtnMsg;
    }
    public void mappingRelationshipValPart(Map row, Map<String, List<Map>> detailBusinessByType, StringBuffer rtnMsg,String groupByKey,String colName,String idKey,String nameKey){
        if(CollectionUtil.isBlankMap(detailBusinessByType)){
            rtnMsg.append("对照表为空;");
            return;
        }
        String colNameValue = CollectionUtil.getStringFromMap(row, colName);
        if(StringUtils.isNotBlank(colNameValue)){
            //制单人
            List<Map> list = detailBusinessByType.get(groupByKey);
            if(CollectionUtil.isNotEmpty(list)){
                for (Map map : list) {
                    String cservicetypename = CollectionUtil.getStringFromMap(map, "cservicetypename");//对照表名称
                    if(colNameValue.equals(cservicetypename)){
                        String cuserid = CollectionUtil.getStringFromMap(map, "cuserid");//选择的职员
                        String cuserid_name = CollectionUtil.getStringFromMap(map, "cuserid_name");//选择的职员
                        row.put(idKey,cuserid);
                        row.put(nameKey,cuserid_name);
                        break;
                    }
                }
            }else{
                rtnMsg.append("对照表为空;");
            }
        }
    }
    public StringBuffer putSomeListColVal(Map map){
        StringBuffer rtnMsg = new StringBuffer();
        putSomeListColChildrenVal(map,rtnMsg);
        String cdetailclassname = "记账凭证";//明细分类
        map.put("creportname",cdetailclassname);
        map.put("cdetailclassname",cdetailclassname);
        map.put("cdetailclassguid_name",cdetailclassname);
        List<Map> detailClassByMainCodeList = collectDataDaoInterface.queryDetailClassByMainCode(StaticCodeClass.voucherRoot);
        for (Map detailClassMap : detailClassByMainCodeList) {
            String cname = detailClassMap.get("cname").toString();
            Object cguid = detailClassMap.get("cguid");
            Object ccode = detailClassMap.get("ccode");
            if(cname.equals(cdetailclassname)){
                Object istatus = CollectionUtil.getStringFromMap(detailClassMap,"istatus");
                if("0".equals(istatus)){//未启用
                    rtnMsg.append("明细分类："+cdetailclassname+"未启用;");
                    break;
                }
                map.put("cdetailclassguid",cguid);
                map.put("cdetailclasscode",ccode);
                break;
            }
        }
        String corgnid = SessionHelper.getCurrentOrgnId();
        String corgnid_name = SessionHelper.getCurrentOrgnName();
        String ccollector = SessionHelper.getCurrentUserId();
        Map employee = getEmployee(ccollector);
        map.put("ccollecttime", getSysTime());//收集时间（精确到秒）
        map.put("ccollectdate", getSysDate());//收集日期 (查询条件 年月日)
        map.put("ceastatus", "收集成功");
        map.put("ieastatus", "0");
        map.put("centityfilestatus_name", "收集成功");
        map.put("centityfilestatus", 0);
        map.put("csuaistatus", "已检测");
        map.put("isuaistatus", 1);
        map.put("cmanualrelstatus", "未关联");
        map.put("imanualrelstatus", 0);
        map.put("cgenpdfstatus", 0);
        map.put("csourceorganid", corgnid);
        map.put("csourceorganname", corgnid_name);
        map.put("ccollectorid", employee.get("cguid"));
        map.put("ccollectoruseid", ccollector);
        map.put("ccollectorid_name",employee.get("cname"));
        map.put("cvoucherid", Guid.g());
        map.put("cdatasource","localvou");
        map.put("csourcecode","localvou");
        map.put("csourcesysname","手工收集");
        map.put("csourcename","手工收集");
        //分表字段cfiles_table_name
        String cvoucherdate = CollectionUtil.getStringFromMap(map, "cvoucherdate");
        if(cvoucherdate!=null){
            String[] split = cvoucherdate.split("-");
            String s = split[0];
            if(s.length()==4){
                map.put("cfiles_table_name","da_files_"+ s);
            }
        }
        return rtnMsg;
    }
    public Map getEmployee(String cguid)
    {
        return db.queryMap("select b.cguid,b.cname from aos_rms_user a\n" +
                "inner join cm_employee b on a.cEmp=b.cguid\n" +
                "where  a.cguid = ?", cguid);
    }
    public String getSysTime(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time =s.format(date);
        return time;
    }
    public String  getSysDate(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd");
        String time =s.format(date);
        return time;
    }
    public void   setMainToDetail(Map main,Map detail){
        for (Object colStr : main.keySet()) {
            String stringFromMap = CollectionUtil.getStringFromMap(detail,colStr.toString());
            if(StringUtil.isEmpty(stringFromMap)){
                Object value = main.get(colStr);
                detail.put(colStr,value);
            }
        }
    }
    public String makeDefalultValue(Map map){
        String corgnid = SessionHelper.getCurrentOrgnId();
        String corgnid_name = SessionHelper.getCurrentOrgnName();
        String cadminorgnid = SessionHelper.getCurrentAdminOrgnId();
        String cadminorgnid_name = SessionHelper.getCurrentAdminOrgnName();
        String ccreatedate = getSysTime();
        String ccreatorid = SessionHelper.getCurrentUserId();
        String ccreatorid_name = SessionHelper.getCurrentUserRealName();
        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate",ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);
        String g = Guid.g();
        map.put("cguid", g);
        return g;
    }
    //校验数据是否为数字类型
    public static boolean isNumeric(String str){
        Pattern pattern = Pattern.compile("^-?[1-9]+[0-9]*([/.][0-9]+)?|^-?0[/.]?[0-9]*");
        return pattern.matcher(str).matches();
    }
    public static boolean isPositiveNumeric(String str){
        Pattern pattern = Pattern.compile("^[1-9]+[0-9]*([/.][0-9]+)?|^0[/.]?[0-9]*");
        return pattern.matcher(str).matches();
    }

    /**
     * 正整数
     * @param str
     * @return
     */
    public static boolean isPositiveInteger(String str){
        Pattern pattern = Pattern.compile("\\b[1-9][0-9]*\\b");
        return pattern.matcher(str).matches();
    }
    public void integrityMatchingVoucher(List<Map> voucherList,List<Map> voucherMetaList,List<Map> voucherSonMetaList){
        //完整性
        try{
            integrityRuleCheckAction.integrityMatchingVoucher(voucherList,voucherMetaList,voucherSonMetaList,SessionHelper.getCurrentOrgnId(),null);
        }catch (Exception e){
        }
    }
    public void createDaFlieTable(Map listMap){
        try{
            String year = CollectionUtil.getStringFromMap(listMap, "cvoucherdate").split("-")[0];
            getDaFileTableName(year);
        }catch (Exception e){
        }
    }
    private String getDaFileTableName(String year)
    {
        DbService dbService = new ApsContextDb().getDb();

        String table_name = "";
        if (year.contains("da_files_"))
        {
            table_name =  year;
        }else
        {
            table_name = "da_files_"+ year;

        }

        boolean b = DbMetaUtil.isTableExist(dbService, table_name, DbTableType.TABLE);
        if (b)
        {
            return table_name;
        }
        String sql = dbService.getSql("da_api_create_file.createtable");
        String tsql = sql.replace("table_name", table_name);
        dbService.update(tsql);
        return table_name;
    }
}
