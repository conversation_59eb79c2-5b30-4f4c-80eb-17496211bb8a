package com.aisino.da.fc.ms.filecollect.add;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.bd.util.BigDecimalUtil;
import com.aisino.da.fc.dao.filecollect.CollectDataDaoInterface;
import com.aisino.da.fc.util.StaticCodeClass;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * ManualVoucherAddSaveBeforeMS
 *
 * <AUTHOR>
 * @version 1.0, 2024/5/28
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Ms(value = "da.fc.ManualVoucherAddSaveBeforeMS", desp = "手工凭证新增ms前")
public class ManualVoucherAddSaveBeforeMS extends FinBaseIMTemplate implements Ims {
    @Inject
    public ApsContextDb db;
    @Inject
    public CollectDataDaoInterface collectDataDaoInterface;
    @Override
    public Object doMessage(Object... objects) {
        /**
         * todo 校验列表是否唯一,后端必填项校验?
         */
        Params params = (Params) objects[0];
        Map savedata = params.getMap("savedata");
        Map<String, List> newData = (Map<String, List>) savedata.get("_new");
        Map<String, List> updateData = (Map<String, List>) savedata.get("_update");
        //获取主子表明细&&区分新增还是保存
        List newDaFcMdVoucher = newData.get("da_fc_md_voucher");
        List updateDaFcMdVoucher = updateData.get("da_fc_md_voucher");
        Boolean isNew = true;
        Map daFcMdVoucher;
        if(CollectionUtil.isNotEmpty(newDaFcMdVoucher)){
            daFcMdVoucher = (Map)newDaFcMdVoucher.get(0);
            isNew = true;
        }else{
            daFcMdVoucher = (Map)updateDaFcMdVoucher.get(0);
            isNew = false;
        }
        //获取列表
        List<Map> newDaFcMdVoucherZ = newData.get("da_fc_md_voucherz");
        List updateDaFcMdVoucherZ = updateData.get("da_fc_md_voucherz");
        List<Map> daFcMdVoucherz;
        if(isNew){
            daFcMdVoucherz = newDaFcMdVoucherZ;
        }else {//修改保存，新增明细补上clistguid
            daFcMdVoucherz = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(newDaFcMdVoucherZ)){
                String clistguid = CollectionUtil.getStringFromMap(daFcMdVoucher, "clistguid");//clistguid
                for (Map map : newDaFcMdVoucherZ) {
                    map.put("clistguid",clistguid);
                }
                daFcMdVoucherz.addAll(newDaFcMdVoucherZ);
            }
            if(CollectionUtil.isNotEmpty(updateDaFcMdVoucherZ)){
                daFcMdVoucherz.addAll(updateDaFcMdVoucherZ);
            }
        }
        String iaffix  = CollectionUtil.getStringFromMap(daFcMdVoucher, "iaffix");
        if("0".equals(iaffix)||iaffix==null){
        }else {
            Pattern pattern = Pattern.compile("^[1-9]\\d*$");
            boolean matches = pattern.matcher(iaffix).matches();
            if(!matches){
                throw new BusinessException("【附单数】，校验未通过，请检查！");
            }
        }
        if(isNew){//新增数据
            if(CollectionUtil.isEmpty(daFcMdVoucherz)){
                throw new BusinessException("明细行不能为空");
            }
            Map daFcMdVoucherMainMap = daFcMdVoucher;
            StringBuffer stringBuffer = putSomeListColVal(daFcMdVoucherMainMap);
            String cvoucherid = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cvoucherid");
            if(stringBuffer.length()!=0){
                throw new BusinessException(stringBuffer.toString());
            }
            Map listMap = makeListMap(daFcMdVoucherMainMap);
            String metaDataGuid = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cguid");
            //String clistguid = makeDefalultValue(listMap);
            String clistguid = metaDataGuid;
            //新增 唯一性校验
            String businessPrimaryKeyStrManualVoucher = StaticCodeClass.businessPrimaryKeyStrManualVoucher;
            if(vouUniquenessVal(businessPrimaryKeyStrManualVoucher,listMap)){
                throw new BusinessException("该记录已进行收集");
            }
            listMap.put("ext_clistguid",clistguid);
            daFcMdVoucherMainMap.put("clistguid",clistguid);
            //主表、列表金额合计参数:金额合计
            HashMap<Object, Object> sumMap = new HashMap<>();
            for (Map map : daFcMdVoucherz) {
                checkDetail(map);
                map.put("clistguid",clistguid);
                map.put("cvoucherid",cvoucherid);
                setSumAMT(map,sumMap);//处理借贷金额和合计借贷金额
                setMainToDetail(daFcMdVoucherMainMap,map);
            }
            checkSumAMT(sumMap);
            listMap.putAll(sumMap);
            daFcMdVoucherMainMap.putAll(sumMap);
            ArrayList<Map> voucherMetaList = new ArrayList<>();
            voucherMetaList.add(daFcMdVoucher);
            List<Map> voucherList = new ArrayList<>();
            voucherList.add(listMap);
            integrityMatchingVoucher(voucherList,voucherMetaList,daFcMdVoucherz);

            Map vouAndBillRelaMap = makeVouAndBillRelaMap(listMap,metaDataGuid);
            mappingRelationshipVal(listMap,daFcMdVoucherMainMap);
            db.insertT("da_fc_voucherinfo",listMap);//列表
            db.insertT("da_fview_vouandbillrela",vouAndBillRelaMap);//预览表
            createDaFlieTable(listMap);
        }else if(!CollectionUtil.isBlankMap(updateData)){//修改数据
            if(CollectionUtil.isEmpty(daFcMdVoucherz)){
                throw new BusinessException("明细行不能为空");
            }
            //更新列表
            Map daFcMdVoucherMainMap = daFcMdVoucher;
            String cvoucherid = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cvoucherid");
            StringBuffer stringBuffer = putSomeListColVal(daFcMdVoucherMainMap);
            daFcMdVoucherMainMap.put("cvoucherid",cvoucherid);
            if(stringBuffer.length()!=0){
                throw new BusinessException(stringBuffer.toString());
            }
            //列表数据
            String cvoucode = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cvoucode");//元数据主表-凭证编号
            String cvoucodestr = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cvoucodestr");//元数据主表-凭证字号
            String clistguid = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "clistguid");//clistguid
            HashMap<Object, Object> listMap = new HashMap<>();
            listMap.putAll(daFcMdVoucherMainMap);
            listMap.put("ivoucode",cvoucode);
            listMap.put("cvoucode",cvoucodestr);
            listMap.put("cpageid","da_fc_manualvoucherinfo_list");
            listMap.put("ctemplateid","da_fc_manualvoucherinfo_list_template");
            makeDefalultValue(listMap);
            listMap.put("ext_clistguid",clistguid);
            //主表、列表金额合计参数:金额合计
            HashMap<Object, Object> sumMap = new HashMap<>();
            for (Map map : daFcMdVoucherz) {
                checkDetail(map);
                map.put("clistguid",clistguid);
                map.put("cvoucherid",cvoucherid);
                map.put("cvoucode",cvoucode);
                map.put("cvoucodestr",cvoucodestr);
                setSumAMT(map,sumMap);//处理借贷金额和合计借贷金额
                setMainToDetail(daFcMdVoucherMainMap,map);
                String str ="cperioddate,iaffix,cyear,cvoutypecode,cvoutypename,cpostername,cauditorname,creviewer,cmonth,cvoucreatorname," +
                        "cbustypecode,cperioddatelist,cpostdate,cvoucherdate,cdetailclassguid_name,cmonth_name,cbustypename";
                String[] split = str.split(",");
                for (String col : split) {
                    Object value = daFcMdVoucherMainMap.get(col);
                    map.put(col,value);
                }
            }
            checkSumAMT(sumMap);
            listMap.putAll(sumMap);
            listMap.put("cguid",clistguid);
            listMap.put("clistguid",clistguid);
            daFcMdVoucherMainMap.putAll(sumMap);
            //修改 唯一性校验
            String businessPrimaryKeyStrManualVoucher = StaticCodeClass.businessPrimaryKeyStrManualVoucher;
            if(!vouUniquenessValUseGuid(businessPrimaryKeyStrManualVoucher,listMap,clistguid)){
                throw new BusinessException("该记录已进行收集");
            }
            ApsContextDb apsContextDb = new ApsContextDb();
            String dbID = apsContextDb.getDbID();
            DbService db =apsContextDb.getDb(dbID,true);
            db.checkToStartTransaction();
            Map map = db.queryMap("select * from da_fc_voucherinfo where cguid = ?", clistguid);
            apsContextDb.deleteT("da_fc_voucherinfo",listMap,"cguid");
            apsContextDb.deleteT("da_fview_vouandbillrela",listMap,"clistguid");
            String metaDataGuid = CollectionUtil.getStringFromMap(daFcMdVoucherMainMap, "cguid");
            Map vouAndBillRelaMap = makeVouAndBillRelaMap(listMap,metaDataGuid);
            ArrayList<Map> voucherMetaList = new ArrayList<>();
            voucherMetaList.add(daFcMdVoucher);
            List<Map> voucherList = new ArrayList<>();
            voucherList.add(listMap);
            integrityMatchingVoucher(voucherList,voucherMetaList,daFcMdVoucherz);
            if(CollectionUtil.isBlankMap(map)){
                map = listMap;
            }else {
                listMap.remove("cgenpdfstatus");
                map.putAll(listMap);
            }
            //mappingRelationshipVal(map,daFcMdVoucherMainMap);
            apsContextDb.insertT("da_fc_voucherinfo",map);
            apsContextDb.insertT("da_fview_vouandbillrela",vouAndBillRelaMap);//预览表
           /* db.execute("update da_fc_md_voucherz set cvoucode='"+cvoucode+"'" +
                    ",cvoucodestr = '"+cvoucodestr+"' where clistguid = '"+clistguid+"'");*/

            db.update("update da_fc_billandvoucherrel set cvoucode = '"+cvoucode+"' where clistguid = ?",clistguid);
            db.update("update da_fc_voucherandticketrel set cvoucode = '"+cvoucode+"' where clistguid = ?",clistguid);
            db.commit();
        }
        return null;
    }
    public void checkDetail(Map map) {
        String iamt_f = CollectionUtil.getStringFromMap(map, "iamt_f");
        if(iamt_f!=null&&!StringUtils.isBlank(iamt_f)){
            if(BigDecimalUtil.getBigDecimal(iamt_f).abs().compareTo(BigDecimal.ZERO)==0){//数值，不能为 0
                throw new BusinessException("原币金额，数值，不能为 0");
            }
        }
        String idebitamt = CollectionUtil.getStringFromMap(map, "idebitamt");
        String icreditamt = CollectionUtil.getStringFromMap(map, "icreditamt");
        // 校验失败：都不为0、都为空、都有值
        if(StringUtils.isBlank(idebitamt)&&StringUtils.isBlank(icreditamt)){
            throw new BusinessException("借方金额、贷方金额有且只有一项不能为空");
        }
        if("0".equals(idebitamt)||"0".equals(icreditamt)){
            throw new BusinessException("借方金额、贷方金额，不能为0");
        }
        if(idebitamt!=null&&icreditamt!=null){
            throw new BusinessException("借方金额、贷方金额有且只有一项不能为空");
        }
    }
    @Override
    public void putSomeListColChildrenVal(Map map, StringBuffer rtnMsg) {
        String cvoutypeguid = CollectionUtil.getStringFromMap(map, "cvoutypeguid");
        String cbustypeguid = CollectionUtil.getStringFromMap(map, "cbustypeguid");
        Map busTypeInfoMap = collectDataDaoInterface.queryBusTypeInfoByGUID(cbustypeguid);
        Map voucherTypeMap = collectDataDaoInterface.queryVoucherByGUID(cvoutypeguid);
        //凭证类型
        if(!CollectionUtil.isBlankMap(voucherTypeMap)){
            String cname = CollectionUtil.getStringFromMap(voucherTypeMap, "cname");
            String ccode = CollectionUtil.getStringFromMap(voucherTypeMap, "ccode");
            String cvoucode = CollectionUtil.getStringFromMap(map, "cvoucode");
            map.put("cvoutypecode",ccode);
            map.put("cvoutypename",cname);
            map.put("cvoucodestr",ccode+"-"+cvoucode);
        }else{
            rtnMsg.append("凭证类型预置数据为空;");
        }
        //业务类型
        if(!CollectionUtil.isBlankMap(busTypeInfoMap)){
            Object ccode = busTypeInfoMap.get("ccode");
            Object cname = busTypeInfoMap.get("cname");
            map.put("cbustypename", cname);
            map.put("cbustypecode", ccode);
        }else {
            rtnMsg.append("业务类型预置数据为空;");
        }
        //String cmonth = map.get("cmonth").toString();
        int month = BigDecimalUtil.getBigDecimal(map.get("cmonth")).intValue();
        String cmonth = String.valueOf(month);
        map.put("cmonth",cmonth);
        map.put("cmonth_name",cmonth+"月");
        String cyear = map.get("cyear").toString();
        String cmonthStr = String.format("%02d", Integer.valueOf(month));
        map.put("cperioddate", cyear+"-"+cmonthStr);//会计期间（查询2023-02）
        map.put("cperioddatelist", cyear+""+cmonthStr);//会计期间(列表202302)
    }
}
