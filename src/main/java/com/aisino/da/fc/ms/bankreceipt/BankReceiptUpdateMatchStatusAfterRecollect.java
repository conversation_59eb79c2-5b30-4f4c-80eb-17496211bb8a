package com.aisino.da.fc.ms.bankreceipt;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;

@Ms(value = "da_fc_updatematchstatus_after_recollect" , desp = "覆盖收集更新匹配状态")
public class BankReceiptUpdateMatchStatusAfterRecollect implements Ims{

	private final Logger log = LoggerFactory.getLogger(BankReceiptUpdateMatchStatusAfterRecollect.class);
	@SuppressWarnings("unchecked")
	@Override
	public Object doMessage(Object... obj) {
		log.info("调用da_fc_updatematchstatus_after_recollect服务");
		Map rtn = new HashMap();
		if(obj==null) {
			log.info("da_fc_updatematchstatus_after_recollect参数为空。");
			rtn.put("success", "0");
			rtn.put("msg", "参数为空!");
			return rtn;
		}
		Map params = (Map)obj[0];
		String cserialnumber = params.get("cserialnumber")==null?"":params.get("cserialnumber").toString();
		if(StringUtil.isEmpty(cserialnumber)) {
			log.info("da_fc_updatematchstatus_after_recollect批次号为空。");
			rtn.put("success", "0");
			rtn.put("msg", "参数错误!");
			return rtn;
		}
		DbService dbService;
		String dsid = params.get("dsid")==null?"":params.get("dsid").toString();
		if(StringUtil.isEmpty(dsid)) {
			dbService =  new ApsContextDb().getDb();
		}else {
			ApsContextDb db = new ApsContextDb(dsid);
			dbService = db.getDb(dsid,true);
		}
		try {
			//根据批次号查询凭证信息 按组织 + 凭证日期 + 凭证类型+ 凭证号
			Params p = new Params();
			p.put("cserialnumber", cserialnumber);
			List<Map> voucherlist = dbService.queryMapListById("da_fc_bankreceipt_match.getVoucherBySerialnumber", p);
			if(CollectionUtil.isEmpty(voucherlist)) {
				log.debug("da_fc_updatematchstatus_after_recollect批次号:"+cserialnumber+"查询的凭证信息为空。");
				rtn.put("success", "0");
				rtn.put("msg", "批次号:"+cserialnumber+"没有查询到凭证!");
				return rtn;
			}
			List<String>  cvoucherkeystrList = CollectionUtil.getStrColFromListMap(voucherlist, "cvoucherkeystr");
			//凭证覆盖收集清除回单匹配记录
			for(String cvoucherkeystr:cvoucherkeystrList) {
				dbService.checkToStartTransaction();
				Params par = new Params();
				par.put("cvoucherkeystr", cvoucherkeystr);
				dbService.updateById("da_fc_bankreceipt_match.cancelBankstatementMatchVouByVoucherkey", par);
				dbService.updateById("da_fc_bankreceipt_match.cancelBankreceiptMatchVouByVoucherkey", par);
				dbService.updateById("da_fc_bankreceipt_match.cancelBankAndVouByVoucherkey", par);
				dbService.commit();
			}
		}catch(Exception e) {
			dbService.rollback();
			log.info("da_fc_updatematchstatus_after_recollect异常："+e.getMessage());
			rtn.put("success", "0");
			rtn.put("msg", e.getMessage());
			return rtn;
		}
		return null;
	}

}
