package com.aisino.da.fc.ms.filecollect.delete.ms;

import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.action.collect.FileCollectDeleteBaseAction;
import com.aisino.da.fc.bean.CollectParamsBean;
import com.aisino.da.fc.util.CallSourceSysByGuidMsUtil;

import java.util.List;
import java.util.Map;

/**
 * AccountReportDetailDeleteAfterMs
 * 列表行删除后，没有删除其他子表数据，此类处理。包含日志、元数据、列表表
 * <AUTHOR>
 * @version 1.0, 2023/4/23
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Ms(value = "da.fc.AccountReportDetailDeleteAfterMs", desp = "报告删除后")
public class AccountReportDetailDeleteAfterMs extends FileCollectDeleteBaseAction implements Ims {

    @Override
    public Object doMessage(Object... objects) {
        Params params = (Params)objects[0];
        doMessage(params,"da_fc_accountreport","creportname");
        List<Map> list = params.getListMap("da_fc_accountreport");
        CallSourceSysByGuidMsUtil.deleteCallSourceSysByGuid(list);
        return null;
    }

    @Override
    public void makeFinDeleteParamsBean(CollectParamsBean collectParamsBean) {
        makeAccountReportFinDeleteParamsBean(collectParamsBean);
    }
}
