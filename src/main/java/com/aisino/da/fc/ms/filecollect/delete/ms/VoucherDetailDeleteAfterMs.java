package com.aisino.da.fc.ms.filecollect.delete.ms;

import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.action.collect.FileCollectDeleteBaseAction;
import com.aisino.da.fc.bean.CollectParamsBean;
import com.aisino.da.fc.util.CallSourceSysByGuidMsUtil;
import com.aisino.da.fc.util.VouModifyFileCallSUAIUtils;

import java.util.List;
import java.util.Map;

/**
 * VoucherDetailDeleteAfterMs
 * 列表行删除后，没有删除其他子表数据，此类处理。包含日志、元数据、凭证的关系表
 * <AUTHOR>
 * @version 1.0, 2023/4/10
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Ms(value = "da.fc.VoucherDetailDeleteAfterMs", desp = "凭证删除后")
public class VoucherDetailDeleteAfterMs extends FileCollectDeleteBaseAction implements Ims {
    @Override
    public Object doMessage(Object... objects) {
        Params params = (Params)objects[0];
        doMessage(params,"da_fc_voucherinfo","cvoucode");
        List<Map> list = params.getListMap("da_fc_voucherinfo");


        VouModifyFileCallSUAIUtils.updateSUAIStatusMs(list);
        CallSourceSysByGuidMsUtil.deleteCallSourceSysByGuid(list);
        cancelbankReceiptRel(list);
        return null;
    }
    @Override
    public void makeFinDeleteParamsBean(CollectParamsBean collectParamsBean){
        makeVoucherFinDeleteParamsBean(collectParamsBean);
    }
}
