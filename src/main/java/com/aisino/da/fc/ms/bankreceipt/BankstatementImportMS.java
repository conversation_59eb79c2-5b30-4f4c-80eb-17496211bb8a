package com.aisino.da.fc.ms.bankreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.util.ImportUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/09/13/10:43
 */
@Ms(value = "da_fc_bankstatement_ms", desp = "民生银行流水导入保存前")
public class BankstatementImportMS implements Ims {

    private static Log log = LogFactory.getLog(BankstatementImportMS.class);

    @Override
    public Object doMessage(Object... objects) {
        try {
            int startIndex = 17;//起始行
            Map<String,Object> templateMap = (Map) objects[1];
            String startIndexStr = templateMap.get("istartline")==null?"": templateMap.get("istartline").toString();
            if(StringUtil.isNotEmpty(startIndexStr)){
                startIndex = Integer.parseInt(startIndexStr);
            }
            Map<String,Object> map = (Map) objects[0];
            List<Map> importList = new ArrayList();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                importList = map.get(key)==null?new ArrayList():(List)map.get(key);
                break;
            }
            if(CollectionUtil.isNotEmpty(importList)&&importList.size()>=startIndex+1){
                ImportUtil.replaceBlank(importList);//清除单元格里得空格，换行符
                ImportUtil.replaceBlankLine(importList);//清除文件中的空行
                String bfyh = "";//本方银行
                String bfzh = "";//本方账号
                String bfhm = "";//本方户名
                String bz = "";//币种
                String bfyhCell = importList.get(2).get(1)==null?"":importList.get(2).get(1).toString();//本方银行:取 $B$3中的数据
                if(StringUtil.isNotEmpty(bfyhCell)){
                    bfyh = bfyhCell;
                }
                String bfzhCell = importList.get(1).get(1)==null?"":importList.get(1).get(1).toString();//本方账号:取 $B$2中的数据
                if(StringUtil.isNotEmpty(bfzhCell)){
                    bfzh = bfzhCell;
                }
                String bfhmCell = importList.get(0).get(1)==null?"":importList.get(0).get(1).toString();//本方户名：取 $B$1中的数据
                if(StringUtil.isNotEmpty(bfhmCell)){
                    bfhm = bfhmCell;
                }
                String bzCell = importList.get(3).get(1)==null?"":importList.get(3).get(1).toString();//币种：取 $B$4中的数据
                if(StringUtil.isNotEmpty(bzCell)){
                    bz = bzCell;
                }
                Map<Integer,String> headMap = importList.get(startIndex-1);//列名数据
                int headSize = headMap.size();
                int jysjIndex = 0;//交易时间
                int jffseIndex = 2;//借方发生额
                int dffseIndex = 3;//贷方发生额
                for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                    int key = entry.getKey();
                    String value = headMap.get(key);
                    if(StringUtil.isNotEmpty(value)&&"交易时间".equals(value)){
                        jysjIndex = key;
                    }
                    if(StringUtil.isNotEmpty(value)&&"贷方发生额".equals(value)){
                        dffseIndex = key;
                    }
                    if(StringUtil.isNotEmpty(value)&&"借方发生额".equals(value)){
                        jffseIndex = key;
                    }
                }
                headMap.put(headSize,"本方银行");
                headMap.put(headSize+1,"本方账号");
                headMap.put(headSize+2,"本方户名");
                headMap.put(headSize+3,"币种");
                headMap.put(headSize+4,"交易金额");
                headMap.put(headSize+5,"收支类别");
                for(int i = startIndex;i<importList.size();i++){
                    Map<Integer,Object> bodyMap = importList.get(i);
                    bodyMap.put(headSize,bfyh);
                    bodyMap.put(headSize+1, bfzh);
                    bodyMap.put(headSize+2, bfhm);
                    bodyMap.put(headSize+3,bz);
                    String dffseStr = bodyMap.get(dffseIndex)==null?"0":bodyMap.get(dffseIndex).toString();
                    String jffseStr = bodyMap.get(jffseIndex)==null?"0":bodyMap.get(jffseIndex).toString();
                    dffseStr = dffseStr.replaceAll(",","");
                    jffseStr = jffseStr.replaceAll(",","");
                    if(StringUtil.isEmpty(dffseStr)){
                        dffseStr = "0";
                    }if(StringUtil.isEmpty(jffseStr)){
                        jffseStr = "0";
                    }
                    double dffse = 0;
                    //判断金额类型
                    if(ImportUtil.isNumeric(dffseStr)) {
                        dffse = Double.parseDouble(dffseStr);
                    }
                    if(dffse !=0){
                        //取‘借方发生额’或‘贷方发生额’不为空的数据
                        //交易金额取借方发生额时，为支出； 交易金额取贷方发生额时，为收入；
                        bodyMap.put(headSize+4, dffseStr);
                        bodyMap.put(headSize+5,"收入");
                    }else{
                        bodyMap.put(headSize+4, jffseStr);
                        bodyMap.put(headSize+5,"支出");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return objects[0];
    }
}
