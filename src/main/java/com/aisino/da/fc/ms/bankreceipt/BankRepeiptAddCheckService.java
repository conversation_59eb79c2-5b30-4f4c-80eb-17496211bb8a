package com.aisino.da.fc.ms.bankreceipt;

import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CastUtil;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.exception.ApsBatchPreciseException;
import com.aisino.aps.common.exception.ApsException;
import com.aisino.aps.common.exception.ApsPreciseException;
import com.aisino.da.fc.util.ImportUtil;

import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 银行流水导入保存前的校验
 * @Date  2024-02-24 14:24
**/
@Ms(value = "da_fc_bankstatementCheckService", desp = "银行流水导入保存前的校验")
public class BankRepeiptAddCheckService implements Ims {

    @Override
    public Object doMessage(Object... objects) {
        ApsContextDb db =  new ApsContextDb();
        if (null == objects) {
            throw new ApsException("传入参数为空！");
        }
        ApsBatchPreciseException be = new ApsBatchPreciseException("异常信息");
        Params params = (Params) objects[0];
        Map<String, Object> fieldMap = params.getFieldMap();
        String cOrgnId =  SessionHelper.getCurrentOrgnId();
        Set<Map> billCheckSet = new HashSet<Map>();
        if(!fieldMap.get("batchsavedata").toString().isEmpty()){
            List batchsavedata = (List) fieldMap.get("batchsavedata");
            List batchreturn = (List) fieldMap.get("batchreturn");
            for(int i=0;i<batchreturn.size();i++){
                Map detail1= (Map) batchsavedata.get(i);
                Map data11= (Map) detail1.get("data");
                Map data111= (Map) data11.get("da_fc_bankstatement");
                String rowid1 = CastUtil.castString(data111.get("rowid"));
                int rowth = Integer.parseInt(rowid1)+1;
                String cbankaccount_name = CollectionUtil.getStringFromMap(data111,"cbankaccount_name");//本方账户
                if(StringUtil.isNotEmpty(cbankaccount_name)){
                    cbankaccount_name = cbankaccount_name.replaceAll("\\s+", "");
                }
                String bankserial = CollectionUtil.getStringFromMap(data111,"bankserial");//交易流水号
                if(StringUtil.isNotEmpty(bankserial)){
                    String itransactionamount = CollectionUtil.getStringFromMap(data111,"itransactionamount");//交易金额
                    Map temMap = new HashMap();
                    if(StringUtil.isNotEmpty(itransactionamount)){
                        itransactionamount = itransactionamount.replaceAll(",","");
                    }
                    if(ImportUtil.isNumeric(itransactionamount)){
                        DecimalFormat decimalFormat = new DecimalFormat("0.00");
                        itransactionamount = decimalFormat.format(Double.parseDouble(itransactionamount));
                        temMap.put("itransactionamount", Double.parseDouble(itransactionamount));
                    }
                    List<ApsPreciseException> apsPreciseExceptions1 = new ArrayList<ApsPreciseException>();
                    temMap.put("cbankaccount_name", cbankaccount_name);
                    temMap.put("bankserial", bankserial);
                    if(!billCheckSet.add(temMap)){
                        if (StringUtil.isNotEmpty(bankserial)) {
                            if(rowth == batchreturn.size()){
                                //msgBuff.append("文件中交易流水号为" + bankserial + "所在行与其他行银行账号+交易流水号重复,请修改;");
                                apsPreciseExceptions1.add(new ApsPreciseException("da_fc_bankstatement", "bankserial", bankserial, "导入失败，交易流水号为" + bankserial + "所在行与交易流水号重复,请修改;","",rowid1,null));
                                be.addApsCoreException(apsPreciseExceptions1);
                            }
                        }
                    }
                }
            }
        }
        if(!fieldMap.get("savedata").toString().isEmpty()){
            Map savedata = (Map) fieldMap.get("savedata");
            Map data= (Map) savedata.get("_new");
            List datalist= (List) data.get("da_fc_bankstatement");
            for(int i=0;i<datalist.size();i++){
                List<ApsPreciseException> apsPreciseExceptions = new ArrayList<ApsPreciseException>();
                Map data1 = (Map) datalist.get(i);
                String rowid = CastUtil.castString(data1.get("rowid"));
                data1.put("datasource","手工");
                data1.put("centityfilestatus","0");
                data1.put("centityfilestatus_name","收集成功");
                data1.put("ceastatus","0");
                data1.put("ceastatus_name","收集成功");
                String cbankaccount_name = CollectionUtil.getStringFromMap(data1,"cbankaccount_name");//本方账户
                if(StringUtil.isNotEmpty(cbankaccount_name)){
                    cbankaccount_name = cbankaccount_name.replaceAll("\\s+", "");
                    data1.put("cbankaccount_name",cbankaccount_name);
                }
                else{
                    apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "cbankaccount_name", cbankaccount_name, "【本方账号】是必录项，不能为空","",rowid,null));
                }
                String ccounteraccount = CollectionUtil.getStringFromMap(data1,"ccounteraccount");//对方账号
                if(StringUtil.isNotEmpty(ccounteraccount)){
                    ccounteraccount = ccounteraccount.replaceAll("\\s+", "");
                    data1.put("ccounteraccount",ccounteraccount);
                }
                String ctransactiondate = CollectionUtil.getStringFromMap(data1,"ctransactiondate");//交易时间
                ctransactiondate = ImportUtil.transactiondateFormat(ctransactiondate);//常见的日期格式转换
                String itransactionamount = CollectionUtil.getStringFromMap(data1,"itransactionamount");//交易金额
                String caccountname = CollectionUtil.getStringFromMap(data1,"caccountname");//本方户名
                String bankserial = CollectionUtil.getStringFromMap(data1,"bankserial");//交易流水号
                if (StringUtil.isNotEmpty(itransactionamount)){
                    itransactionamount = itransactionamount.replaceAll(",","");
                    if(!ImportUtil.isNumeric(itransactionamount)) {
                        apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "itransactionamount", itransactionamount, "交易金额请输入数字！","",rowid,null));
                    }else{
                        if("0".equals(itransactionamount)){
                            apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "itransactionamount", itransactionamount, "交易金额不得等于0！","",rowid,null));
                        }else{
                            //不足两位则补0
                            DecimalFormat decimalFormat = new DecimalFormat("0.00");
                            itransactionamount = decimalFormat.format(Double.parseDouble(itransactionamount));
                            data1.put("itransactionamount",itransactionamount);
                        }
                    }
                }else{
                    apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "itransactionamount", itransactionamount, "【交易金额】是必录项，不能为空","",rowid,null));
                }
                if(StringUtil.isNotEmpty(bankserial)){
                    int recount = db.queryCount("select count(1) from da_fc_bankstatement where cbankaccount_name = ? and bankserial = ? and corgnid = ? and itransactionamount = ?",new Object[]{cbankaccount_name,bankserial,cOrgnId,itransactionamount});
                    if(recount>0){
                        apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "bankserial", bankserial, "文件中交易流水号为" + bankserial + "所在行与已导入数据的交易流水号重复,请修改;","",rowid,null));
                    }
                }
                if(StringUtil.isNotEmpty(ctransactiondate)) {
                    if (!ImportUtil.checkDateFormat(ctransactiondate)) {
                        apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "ctransactiondate", ctransactiondate, "交易时间格式错误；","",rowid,null));
                    }else{
                        data1.put("ctransactiondate",ctransactiondate);
                    }
                }else{
                    apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "ctransactiondate", ctransactiondate, "【交易时间】是必录项，不能为空","",rowid,null));
                }
                //若模板中本方户名不为空，则校验是否与登录组织名称一致，若一致导入成功，不一致，报错“本方户名有误”
                Map map = db.queryMap("select cName from aos_orgn where cGuid=?", cOrgnId);
                if(StringUtil.isNotEmpty(caccountname)){
                    String corgnname= CollectionUtil.getStringFromMap(map,"cName");
                    if(!corgnname.equals(caccountname)){
                        apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "caccountname", caccountname, "本方户名有误!","",rowid,null));
                    }
                }else{
                    data1.put("caccountname",CollectionUtil.getStringFromMap(map,"cName"));
                }
                //银行账号导入的时候需要校验是不是所选财务组织的银行账号，如果不是，导入失败，并标注原因“银行账号有误”
                int bankcount = db.queryCount("select count(fbc.cBankAccount) from fk_bd_compaccount fbc where fbc.cBankAccount=? and fbc.corgnid=? and fbc.istatus=1 and fbc.cStatus=1 ",new Object[]{cbankaccount_name,cOrgnId});
                if(bankcount==0){
                    apsPreciseExceptions.add(new ApsPreciseException("da_fc_bankstatement", "cbankaccount_name", bankcount, "银行账号已销户或已禁用!","",rowid,null));
                }else{
                    Map cabnkname = db.getOneRecord("select fbc.cGuid,fbc.cOpenBank_name from fk_bd_compaccount fbc where fbc.cBankAccount=? and fbc.corgnid=?",new Object[]{cbankaccount_name,cOrgnId});
                    String cbank= CollectionUtil.getStringFromMap(cabnkname,"cOpenBank_name");
                    data1.put("cbank",cbank);
                    data1.put("cbankaccount",cbankaccount_name);
                }
                be.addApsCoreException(apsPreciseExceptions);
            }
        }
        if (be.getApsPreciseExceptions().size() > 0) {
            throw be;
        }
        return true;
    }


}
