package com.aisino.da.fc.ms.filecollect.delete;

import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.fc.util.BillTicketDeleteCheckUtil;
import com.aisino.da.fc.util.MakeDataSqlUtil;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件名：BillCollectServiceImpl.class
 * 作者：刘淳
 * 描述：资料收集-单据资料-删除后操作MS
 * 修改人：
 * 修改时间：2023-02-16 16:54
 * 修改内容：
 **/
@Ms(value = "da_fc_bill_collect_after_delete", desp = "数据收集票据资料删除后操作")
public class BillCollectAfterDeleteService implements Ims {

    @Override
    public Object doMessage(Object... objects) {
        ApsContextDb apsContextDb = new ApsContextDb();
        Params params = (Params) objects[0];
        Map<String, Object> fieldMap = params.getFieldMap();
        String tablename = CollectionUtil.getStringFromMap(fieldMap, "tablename");
        List<Map> deleteDataList = (List<Map>) fieldMap.get(tablename);
        Map billData = deleteDataList.get(0);

////       删除关联表数据
//        BillTicketDeleteCheckUtil.deleteBillRel(billid, cvoucherid);
//       删除主资料关联的单据/票据/银行回单数据
        Map<String, Object> returnMap = BillTicketDeleteCheckUtil.checkAndDelete(apsContextDb, billData, false,true);
//        处理四性检测数据
        Message ms = new Message("da.suai.updaterelstatusms");
        Map par = new HashMap();
        par.put("ctablename", MakeDataSqlUtil.BILL_TABLE_NAME);
        par.put("cvoucherids", Lists.newArrayList(CollectionUtil.getStringFromMap(billData,"cvoucherid")));
        ms.publish(par);
        // 删除后更新凭证完整性状态
        List<Map> integrityList = new ArrayList<>();
        integrityList.add(billData);
        Message integrityMs = new Message("da.bbs.IntegrityStatusUpdateMs");
        integrityMs.publish(integrityList);
        if(returnMap.containsKey("esData")){
            // 推送ES信息
            List<Map<String, List<String>>> dataList = (List<Map<String, List<String>>>) returnMap.get("esData");
            if(CollectionUtil.isNotEmpty(dataList)){
                List<String> billList = new ArrayList<>();
                List<String> invoiceList = new ArrayList<>();
                List<String> bankesList = new ArrayList<>();
                for(Map<String, List<String>> dataMap : dataList){
                    List<String> billEsList = dataMap.get("billEsList");
                    billList.addAll(billEsList);
                    List<String> invoiceEsList = dataMap.get("invoiceEsList");
                    invoiceList.addAll(invoiceEsList);
                    List<String> bankesEsList = dataMap.get("bankesEsList");
                    bankesList.addAll(bankesEsList);
                }
                // 单据
                if(CollectionUtil.isNotEmpty(billList)){
                    BillTicketDeleteCheckUtil.sendEsMap(billList, "1");
                }
                // 票据
                if(CollectionUtil.isNotEmpty(invoiceList)){
                    BillTicketDeleteCheckUtil.sendEsMap(invoiceList, "2");
                }
                // 银行回单
                if(CollectionUtil.isNotEmpty(bankesList)){
                    BillTicketDeleteCheckUtil.sendEsMap(bankesList, "3");
                }
            }
        }

        return params;
    }
}
