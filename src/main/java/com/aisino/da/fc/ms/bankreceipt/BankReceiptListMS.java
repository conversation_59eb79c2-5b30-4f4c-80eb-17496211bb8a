package com.aisino.da.fc.ms.bankreceipt;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CastUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.common.util.PermeEmpUtil;
import com.aisino.da.fc.util.UserUtil;
import com.aisino.da.fc.util.design.PageSqlUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/19
 * @description :通过ms重写系统查询---银行回单
 */
@Ms(value = "da.fc.bankreceipt.list.condition.nomainproc", desp = "系统收集-银行回单-查询")
public class BankReceiptListMS implements Ims {
    @Override
    public Object doMessage(Object... args) {
        if (args == null) {
            throw new BusinessException("参数不能为空");
        }
        Params params = (Params) args[0];
        DbService dbService = new ApsContextDb().getDb();
        String sql = dbService.getSql("da_fc_bill_collection_receipt_sql.selectBankReceiptList");
        sql = checkPermission(sql, "aos_rms_user", "收集人", "t.ccollectorid", "#changedata12345#");

        Map fieldMap = new HashMap<>();
        List<Map> conditionList = params.getListMap("conditionList");
        if (conditionList != null && conditionList.size() != 0) {
            String pjSqlStr = "";
            for (Map item : conditionList) {
                fieldMap.put(item.get("field"), item.get("value"));
                if ("ctransactiondate".equals(item.get("field"))) {
                    fieldMap.put("ctransactiondate_start", item.get("value").toString().substring(0, 10));
                    fieldMap.put("ctransactiondate_end", item.get("value").toString().substring(11, 21));
                }
                if ("itransactionamount".equals(item.get("field"))) {
                    String valueStr = CastUtil.castString(item.get("value"));
                    if (valueStr.contains(",")) {
                        String[] valueList = valueStr.split(",");
                        if (!"".equals(CastUtil.castString(valueList[0]))) {
                            pjSqlStr = " AND t.itransactionamount >= " + valueList[0];
                        }
                        if (valueList.length > 1 && !"".equals(CastUtil.castString(valueList[1]))) {
                            pjSqlStr += " AND t.itransactionamount <= " + valueList[1];
                        }
                    } else {
                        if (!"".equals(valueStr)) {
                            pjSqlStr += " AND t.itransactionamount >= " + valueStr;
                        }
                    }
                }
            }
            sql = sql.replaceAll("#changedata9999#", pjSqlStr);
        }
        return PageSqlUtil.getPageResultMap(fieldMap, sql, params, dbService);
    }

    public String checkPermission(String sql, String tableName, String permeName, String SQLfield, String SQLStr) {
        // 查询有权限的职员 0-没有配置数据权限 1-配置了数据权限且有权限 2-配置了数据权限但没有权限
        Object emps = PermeEmpUtil.getPermeDatasIds(SessionHelper.getCurrentAdminOrgnId(), tableName, permeName);
        String reStr = "";
        if (emps != null) {
            if (emps instanceof List) {
                //有权限配置
                List<String> empIdList = (List<String>) emps;
                List<String> userids = UserUtil.selectUserByEmp(empIdList);
                StringBuilder sb = new StringBuilder(" and ").append(SQLfield);
                sb.append(" in('");
                for (String userid : userids) {
                    sb.append(userid).append("','");
                }
                sb.delete(sb.length() - 2, sb.length());
                sb.append(" ) ");
                reStr = sb.toString();
            } else {
                //存在数据权限，但是没配置
                reStr = " and 1=2 ";
            }
        }
        sql = sql.replaceAll(SQLStr, reStr);
        return sql;
    }


}
