package com.aisino.da.fc.ms.bankreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.util.ImportUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/09/13/10:43
 */
@Ms(value = "da_fc_bankstatement_zs", desp = "招商银行流水导入保存前")
public class BankstatementImportZS implements Ims {

    private static Log log = LogFactory.getLog(BankstatementImportZS.class);

    @Override
    public Object doMessage(Object... objects) {
        try {
            int startIndex = 8;//起始行
            Map<String,Object> templateMap = (Map) objects[1];
            String startIndexStr = templateMap.get("istartline")==null?"": templateMap.get("istartline").toString();
            if(StringUtil.isNotEmpty(startIndexStr)){
                startIndex = Integer.parseInt(startIndexStr);
            }
            Map<String,Object> map = (Map) objects[0];
            List<Map> importList = new ArrayList();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                importList = map.get(key)==null?new ArrayList():(List)map.get(key);
                break;
            }
            if(CollectionUtil.isNotEmpty(importList)&&importList.size()>=startIndex+1){
                ImportUtil.replaceBlank(importList);//清除单元格里得空格，换行符
                ImportUtil.replaceBlankLine(importList);//清除文件中的空行
                Map<Integer,String> headMap = importList.get(startIndex-1);//列名数据
                int headSize = headMap.size();
                int jfjeIndex = 7;//借方金额
                int dfjeIndex = 8;//贷方金额
                for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                    int key = entry.getKey();
                    String value = headMap.get(key);
                    if("借方金额".equals(value)){
                        jfjeIndex = key;
                    }
                    if("贷方金额".equals(value)){
                        dfjeIndex = key;
                    }
                }
                for(int i = startIndex;i<importList.size();i++){
                    Map<Integer,Object> bodyMap = importList.get(i);
                    String jfjeStr = bodyMap.get(jfjeIndex)==null?"0":bodyMap.get(jfjeIndex).toString();
                    String dfjeStr = bodyMap.get(dfjeIndex)==null?"0":bodyMap.get(dfjeIndex).toString();
                    jfjeStr = jfjeStr.replaceAll(",","");
                    dfjeStr = dfjeStr.replaceAll(",","");
                    double jfje = 0;
                    //判断金额类型
                    if(ImportUtil.isNumeric(jfjeStr)) {
                        jfje = Double.parseDouble(jfjeStr);
                    }
                    if(jfje!=0){
                        //取‘借方金额’或‘贷方金额’不为0的数据
                        //交易金额取借方金额时，为支取;交易金额取贷方金额时，为收入
                        bodyMap.put(headSize,jfjeStr);
                        bodyMap.put(headSize+1,"支出");
                    }else{
                        bodyMap.put(headSize,dfjeStr);
                        bodyMap.put(headSize+1,"收入");
                    }
                    headMap.put(headSize,"交易金额");
                    headMap.put(headSize+1,"收支类别");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return objects[0];
    }
}
