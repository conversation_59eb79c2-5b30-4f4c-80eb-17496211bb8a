package com.aisino.da.fc.ms.bankreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.fc.util.ImportUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/09/11/11:43
 */
@Ms(value = "da_fc_bankstatement_abc", desp = "农业银行流水导入保存前")
public class BankstatementImportABC implements Ims {

    private static Log log = LogFactory.getLog(BankstatementImportABC.class);

    @Override
    public Object doMessage(Object... objects) {
        try {
            int startIndex = 3;//起始行
            Map<String,Object> templateMap = (Map) objects[1];
            String startIndexStr = templateMap.get("istartline")==null?"": templateMap.get("istartline").toString();
            if(StringUtil.isNotEmpty(startIndexStr)){
                startIndex = Integer.parseInt(startIndexStr);
            }
            Map<String,Object> map = (Map) objects[0];
            List<Map> importList = new ArrayList();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                importList = map.get(key)==null?new ArrayList():(List)map.get(key);
                break;
            }
            int zsrbsNum = importList.size();//总收入笔数行号
            int importSize = importList.size();
            if(CollectionUtil.isNotEmpty(importList)&&importList.size()>=startIndex+1){
                //农行有两行表尾，表尾之后可能还有空行
                for(int i=0;i<importList.size();i++){
                    Map<Integer,String> temp = importList.get(i);
                    for (Map.Entry<Integer, String> entry : temp.entrySet()) {
                        int key = entry.getKey();
                        String value = temp.get(key);
                        if(StringUtil.isNotEmpty(value)&&"总收入笔数".equals(value)){
                            zsrbsNum = i;
                        }
                    }
                }
                //从总收入笔数那行之后都去掉
                for(int j=zsrbsNum;j<importSize;j++){
                    importList.remove(zsrbsNum);
                }
                Map<Integer,String> head0Map = importList.get(1);//表头数据
                String zh = "";//账号
                String hm = "";//户名
                String bz = "";//币种
                String zhCell = head0Map.get(0)==null?"":head0Map.get(0).toString();//账号
                if(StringUtil.isNotEmpty(zhCell)){
                    zh = zhCell.substring(zhCell.indexOf("账号")+3,zhCell.length());
                    zh = zh.replaceAll("[^0-9]", "");
                }
                String hmCell = head0Map.get(1)==null?"":head0Map.get(1).toString();//户名
                if(StringUtil.isNotEmpty(hmCell)){
                    hm = hmCell.substring(hmCell.indexOf("户名")+3,hmCell.length());
                }
                String bzCell = head0Map.get(2)==null?"":head0Map.get(2).toString();//币种
                if(StringUtil.isNotEmpty(bzCell)){
                    bz = bzCell.substring(bzCell.indexOf("币种")+3,bzCell.length());
                }
                Map<Integer,String> headMap = importList.get(startIndex-1);//列名数据
                int headSize = headMap.size();
                ImportUtil.replaceBlank(importList);
                int srjeIndex = 1;//收入金额
                int zcjeIndex = 2;//支出金额
                for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                    int key = entry.getKey();
                    String value = headMap.get(key);
                    if(StringUtil.isNotEmpty(value)&&"支出金额".contains(value)){
                        zcjeIndex = key;
                    }
                    if(StringUtil.isNotEmpty(value)&&"收入金额".contains(value)){
                        srjeIndex = key;
                    }
                }
                headMap.put(headSize,"本方账号");
                headMap.put(headSize+1,"本方户名");
                headMap.put(headSize+2,"币种");
                headMap.put(headSize+3,"交易金额");
                headMap.put(headSize+4,"收支类别");
                for(int i = startIndex;i<importList.size();i++){
                    Map<Integer,Object> bodyMap = importList.get(i);
                    bodyMap.put(headSize,zh);
                    bodyMap.put(headSize+1,hm);
                    bodyMap.put(headSize+2,bz);
                    String zcjeStr = bodyMap.get(zcjeIndex)==null?"0":bodyMap.get(zcjeIndex).toString();
                    String srjeStr = bodyMap.get(srjeIndex)==null?"0":bodyMap.get(srjeIndex).toString();
                    zcjeStr = zcjeStr.replaceAll(",","").replaceAll("[\t\n\r]", "");
                    srjeStr = srjeStr.replaceAll(",","").replaceAll("[\t\n\r]", "");
                    if(StringUtil.isEmpty(zcjeStr)){
                        zcjeStr = "0";
                    }if(StringUtil.isEmpty(srjeStr)){
                        srjeStr = "0";
                    }
                    double zcje = 0;
                    double srje = 0;
                    //判断金额类型
                    if(ImportUtil.isNumeric(zcjeStr)) {
                        zcje = Double.parseDouble(zcjeStr);
                    }
                    if(ImportUtil.isNumeric(srjeStr)) {
                        srje = Double.parseDouble(srjeStr);
                    }
                    if(srje !=0){
                        //取‘收入金额’或‘支出金额’不为空的数据
                        //交易金额取收入金额时，为收入； 交易金额取支出金额时，为支出；
                        bodyMap.put(headSize+3, srjeStr);
                        bodyMap.put(headSize+4,"收入");
                    }else{
                        bodyMap.put(headSize+3, zcjeStr);
                        bodyMap.put(headSize+4,"支出");
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return objects[0];
    }
}
