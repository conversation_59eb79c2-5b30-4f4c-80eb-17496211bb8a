package com.aisino.da.fc.ms.bankreceipt;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CastUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.fc.util.design.PageSqlUtil;
import com.aisino.da.fc.util.design.PermissionSqlUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/20
 * @description :通过ms重写系统查询---票据资料
 */
@Ms(value = "da.fc.invoice.list.condition.nomainproc", desp = "系统收集-票据资料-查询")
public class InvoiceQuerytListMS implements Ims {
    @Override
    public Object doMessage(Object... args) {
        if (args == null) {
            throw new BusinessException("参数不能为空");
        }
        Params params = (Params) args[0];
        DbService dbService = new ApsContextDb().getDb();
        String sql = dbService.getSql("da_fc_bill_collection_receipt_sql.selectInvoiceList");
        // 1 是历史数据之前有这个字段 字段有值，0 新增字段 历史数据没有值
        sql = PermissionSqlUtil.checkPermission(sql, "aos_rms_user", "收集人", "t.ccollectorid", "#changedccollectorid#", 1);
        sql = PermissionSqlUtil.checkPermission(sql, "cm_employee", "收款人", "t.cpayeeid", "#changedatacpayeeid#", 0);
        sql = PermissionSqlUtil.checkPermission(sql, "cm_employee", "复核人", "t.cdareviewerid", "#changedatacdareviewerid#", 0);

        Map<String, Object> fieldMap = new HashMap<>();
        List<Map> conditionList = params.getListMap("conditionList");
        if (conditionList != null && conditionList.size() != 0) {
            for (Map item : conditionList) {
                fieldMap.put(CastUtil.castString(item.get("field")), item.get("value"));
                if ("ddate".equals(CastUtil.castString(item.get("field")))) {
                    fieldMap.put("ddate_start", item.get("value").toString().substring(0, 10));
                    fieldMap.put("ddate_end", item.get("value").toString().substring(11, 21));
                }
                if ("itotal".equals(CastUtil.castString(item.get("field")))) {
                    String valueStr = CastUtil.castString(item.get("value"));
                    if (valueStr.contains(",")) {
                        String[] valueList = valueStr.split(",");
                        if (!"".equals(CastUtil.castString(valueList[0]))) {
                            fieldMap.put("itotal_start", valueList[0]);
                        }
                        if (valueList.length > 1 && !"".equals(CastUtil.castString(valueList[1]))) {
                            fieldMap.put("itotal_end", valueList[1]);
                        }
                    } else {
                        if (!"".equals(valueStr)) {
                            if ("ge".equals(CastUtil.castString(item.get("wigtype")))) {
                                fieldMap.put("itotal_start", valueStr);
                            } else {
                                fieldMap.put("itotal_end", valueStr);
                            }
                        }
                    }
                }
            }
        }
        return PageSqlUtil.getPageResultMap(fieldMap, sql, params, dbService);
    }


}
