package com.aisino.da.fc.ms.filecollect.delete.ms;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.fc.util.DeleteVoucherBeforeCheckBill;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * VoucherDetailDeleteBeforeMs
 * 凭证删除前校验
 * <AUTHOR>
 * @version 1.0, 2023/3/7
 * @see [相关类/方法]
 * @since 1.0
 */
@Ms(value = "da.fc.VoucherDetailDeleteBeforeMs", desp = "凭证删除前校验")
public class VoucherDetailDeleteBeforeMs implements Ims {
    @Inject
    public ApsContextDb db;
    @Override
    public Object doMessage(Object... objects) {
        Params params = (Params)objects[0];
        String cguid = params.getString("cguid");
        List<String> cguids = params.getList("cguids");
        if(cguid==null&&cguids!=null){
            cguid = cguids.get(0);
        }
        Map deleteMap = db.queryMap("select * from da_fc_voucherinfo where cGuid='" + cguid + "'");
        //Object ieastatus = deleteMap.get("ieastatus");//收集成功
        //Object centityfilestatus = deleteMap.get("centityfilestatus");//收集成功
        Object imanualrelstatus = deleteMap.get("imanualrelstatus")==null?"0":deleteMap.get("imanualrelstatus");//手工关联，没有这个状态的默认0
        if(!"0".equals(String.valueOf(imanualrelstatus))){
            throw new BusinessException("该条记录已进行手工关联，不允许删除");
        }
        //关联单据
        ArrayList<Map> deleteList = new ArrayList<>();
        deleteList.add(deleteMap);
        StringBuffer billMsg = new StringBuffer();
        List<Map> list = DeleteVoucherBeforeCheckBill.checkBill(deleteList, billMsg);
        if(billMsg.length()!=0||list.size()==0){//单据报错
            throw new BusinessException(billMsg.toString());
        }
        return null;
    }
}
