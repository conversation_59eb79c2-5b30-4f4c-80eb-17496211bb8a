package com.aisino.da.fc.config.dataretrieval;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DataRetrievalPageConfigUtil
 *
 * <AUTHOR>
 * @version 2.1, 2025/1/20
 */
public class DataRetrievalPageConfigUtil {
    static final String[] accountReportName = {"月度财务报告","季度财务报告","半年度财务报告","年度财务报告","其他报告"};
    private Boolean accountReportDataVal(String cdetailclasscode) {
        // 平台提供的后台实现方法获取需要添加权限数据的id
        Message msg1 = new Message("aps.dr.data.GetRuleDetailByEntityService");
        Map par1=new HashMap();
        // 数据权限实体表在平台表中的id
        DbService dbService = new ApsContextDb().getDb();
        Map map1 = dbService.queryMap("select cguid from aps_data_resource_table where ctablecode='da_bbs_file_class_sublist'");
        par1.put("centityguid", CollectionUtil.getStringFromMap(map1,"cguid"));
        // 电子档案应用id
        par1.put("capplicationid", "yx_da");
        // 管理组织id
        par1.put("corgnid", SessionHelper.getCurrentAdminOrgnId());
        Object value1 = msg1.publish(par1);
        if(value1!=null){//为null  不存在 规则设置。为空没有权限
            //list.add("JD");
            //list.add("SAP");
            // 为权限实体表的 name
            if(value1 instanceof List){
                List<String> codeList = (List<String>) value1;
                ArrayList<String> checkList = new ArrayList<>();
                for (String cguid : codeList) {
                    Map codeMap = dbService.queryMap("select ccode from da_bbs_file_class_sublist where cguid='"+cguid+"'");
                    checkList.add(CollectionUtil.getStringFromMap(codeMap,"ccode"));
                }
                if(checkList.contains(cdetailclasscode)){
                    return true;
                }else {
                    return false;
                }
            }else {
                return true;
            }
        }else{
            return true;
        }
    }
    public void makeAccountReport(ArrayList<Object> children,Map urlMap) {
        for (int i = 1; i < accountReportName.length+1; i++) {
            String cdetailclasscode = "BG0" + i;
            Boolean aBoolean = accountReportDataVal(cdetailclasscode);
            if(aBoolean){
                String str = accountReportName[i - 1];
                HashMap<Object, Object> children_map = new HashMap<>();
                children_map.put("cid", "01-03-0"+i);
                children_map.put("cdetailclasscode", cdetailclasscode);
                children_map.put("label", str);
                children_map.put("bleaf",  false);
                children_map.put("curl", urlMap.get(str));
                children_map.put("children", new ArrayList<>());
                children.add(children_map);
            }
        }
    }
}
