package com.aisino.da.fc.action.collect;

import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aps.common.pojo.vo.APSResponseVo;
import com.aisino.da.fc.bean.CollectParamsBean;
import com.aisino.da.fc.util.CallSourceSysByGuidMsUtil;

import java.util.ArrayList;
import java.util.Map;

/**
 * AccountBookBatchDelete
 *
 * <AUTHOR>
 * @version 1.0, 2023/4/12
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Action("da/fc/fin")
public class AccountBookBatchDelete  extends FileCollectDeleteBaseAction{
    @Request.Post("/accountbook/batchdelete")
    public APSResponseVo voucherBatchDelete(Params params) {
        Map creportname = batchDelete(params, "creportname");
        ArrayList<Map> successList = (ArrayList<Map>) creportname.get("successList");
        if(!successList.isEmpty()){
            CallSourceSysByGuidMsUtil.deleteCallSourceSysByGuid(successList);
        }
        return new APSResponseVo(creportname);
    }
    @Override
    public void makeFinDeleteParamsBean(CollectParamsBean collectParamsBean) {
        makeAccountBookFinDeleteParamsBean(collectParamsBean);
    }
}
