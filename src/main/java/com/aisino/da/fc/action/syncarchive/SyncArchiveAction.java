package com.aisino.da.fc.action.syncarchive;


import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.ContentType;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.exception.ApsException;
import com.aisino.aps.common.pojo.vo.APSResponseVo;
import com.aisino.aps.common.utils.OrgnUtils;
import com.aisino.da.fc.service.syncarchive.SyncArchiveService;
import com.aisino.da.fc.util.SyncArchiveUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName SyncArchiveAction
 * @Description 同步归档设置Action层
 * <AUTHOR>
 * @Date 2023/2/13 15:01
 */
@Action("da/fc/archive")
public class SyncArchiveAction {

    @Inject
    private SyncArchiveService syncArchiveService;

    @Inject
    private ApsContextDb db;

    /**
     * 加载左树节点
     * @param params
     * @return
     */
    @ContentType
    @Request.Post("loadlefttree")
    public APSResponseVo loadLeftTree(Params params){
        try {
            // 使用修改后的左树方法
            Map<String, Object> stringObjectMap = syncArchiveService.loadLeftTreeChange();
            return new APSResponseVo(stringObjectMap);
        } catch (Exception e){
            throw new ApsException(e);
        }
    }

    /**
     * 根据左树获取右表数据
     * @param params
     * @return
     */
    @ContentType
    @Request.Post("getnodelist")
    public APSResponseVo getNodeList(Params params){
        try {
            Map<String, Object> pageNodeList = syncArchiveService.getPageNodeList(params);
            return new APSResponseVo(pageNodeList);
        } catch (Exception e){
            throw new ApsException(e);
        }
    }

    /**
     * 保存同步归档设置
     * @param params
     * @return
     */
    @ContentType
    @Request.Post("savesyncarchive")
    public APSResponseVo saveSyncArchive(Params params){
        try {
            // 当前登录的管理组织id，根据管理组织id查询同步归档的所有主键
            String currentAdminOrgnId = SessionHelper.getCurrentAdminOrgnId();
            List<String> saveGuidList = syncArchiveService.selectArchiveIdByAdminOrgan(currentAdminOrgnId);
            // 修改和新增一起从页面带过来了，判断是新增还是修改，如果数据库中有则是修改，反之则是新增;下面是具体的实现
            List<Map> dataList = params.getListMap("modifyList");
            // 循环数据并分组数据，saveGuidList包含的主键id则是修改，反之则是新增
            Map<Boolean, List<Map>> dataMap = dataList.stream().collect(Collectors.partitioningBy(map -> saveGuidList.contains(map.get("cguid") + "")));
            List<Map> addList = dataMap.get(false);
            List<Map> modifyList = dataMap.get(true);
            boolean blfg = true;
            if(CollectionUtil.isNotEmpty(addList)){
                blfg = syncArchiveService.batchSaveSyncArchive(addList);
            }
            if(CollectionUtil.isNotEmpty(modifyList)){
                blfg = syncArchiveService.batchUpdateSyncArchive(modifyList);
            }
            Map<String,String> returnMap = new HashMap<>(2);
            // 修改集合
            if(blfg){
                returnMap.put("status", "1");
                returnMap.put("msg", "成功");
            }else {
                returnMap.put("status", "0");
                returnMap.put("msg", "失败");
            }
            return new APSResponseVo(returnMap);
        }catch (Exception e){
            if(e instanceof BusinessException){
                throw new BusinessException(e.getMessage());
            }
            throw new ApsException(e);
        }
    }

    /**
     * 保存打印模板数据
     * */
    @ContentType
    @Request.Post("saveprinttemplate")
    public APSResponseVo savePrintTemplate(Params params){
        try {
            Map<String, String> returnMap = syncArchiveService.savePrintTemplate(params);
            return new APSResponseVo(returnMap);
        }catch (Exception e){
            if(e instanceof BusinessException){
                throw new BusinessException(e.getMessage());
            }
            throw new ApsException(e);
        }
    }

    /**
    * 删除打印模板数据
    * */
    @ContentType
    @Request.Post("deleteprinttemplate")
    public APSResponseVo deletePrintTemplate(Params params){
        try {
            boolean b = syncArchiveService.deletePrintTemplate(params.getFieldMap());
            Map<String,String> returnMap = new HashMap<>(2);
            if(b){
                returnMap.put("status", "1");
                returnMap.put("msg", "成功");
            }else {
                returnMap.put("status", "0");
                returnMap.put("msg", "失败");
            }
            return new APSResponseVo(returnMap);
        }catch (Exception e){
            if(e instanceof BusinessException){
                throw new BusinessException(e.getMessage());
            }
            throw new ApsException(e);
        }
    }

    /**
     * 获取打印模板数据
     * @param params
     * @return
     */
    @ContentType
    @Request.Post("getprinttemplate")
    public APSResponseVo getPrintTempLate(Params params){
        try {
            List<Map> mapList = syncArchiveService.getPrintTempLateByHeadId(params.getString("cguid"));
            return new APSResponseVo(mapList);
        } catch (Exception e){
            throw new ApsException(e);
        }
    }

    /**
     * 判断登录的用户是不是管理组织
     */
    @ContentType
    @Request.Post("ismanagement")
    public APSResponseVo isManagement(Params params) {
        String orgnId = params.getString("orgnId");
        boolean adminOrgn = OrgnUtils.isAdminOrgn(orgnId);
        Map<String,Boolean> returnMap = new HashMap<>(2);
        returnMap.put("status",adminOrgn);
        return new APSResponseVo(returnMap);
    }

    /**
     * 获取当前管理组织的以及下级的所有的组织的个数
     */
    @ContentType
    @Request.Post("getadminorganandlower")
    public APSResponseVo getAdminOrganAndLower(Params params) {
        List<String> adminOrganAndSubGuids = SyncArchiveUtil.getAdminOrganAndSubGuids(db);
        return new APSResponseVo(adminOrganAndSubGuids.size());
    }


    /**
     * 获取右表归档资料的显示打印模板名称
     */
    @ContentType
    @Request.Post("gettableprintname")
    public APSResponseVo getTablePrintName(Params params) {
        String cguid = params.getString("cguid");
        String tablePrintName = syncArchiveService.getTablePrintName(cguid, SessionHelper.getCurrentOrgnId());
        Map<String, String> returnMap = new HashMap<>(1);
        returnMap.put("cprint_template_name", tablePrintName);
        return new APSResponseVo(returnMap);
    }
}
