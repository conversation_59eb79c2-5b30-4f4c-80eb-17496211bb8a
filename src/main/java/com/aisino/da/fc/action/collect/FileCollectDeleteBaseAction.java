package com.aisino.da.fc.action.collect;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.pojo.vo.APSResponseVo;
import com.aisino.da.fc.bean.CollectParamsBean;
import com.aisino.da.fc.service.collect.FinDeleteInterface;
import com.aisino.da.fc.util.BaseUtilsService;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * FileCollectDeleteBaseAction
 * 填装全局恒定删除参数
 * <AUTHOR>
 * @version 1.0, 2023/3/27
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public abstract class FileCollectDeleteBaseAction extends BaseUtilsService {
    @Inject
    public FinDeleteInterface finDeleteInterface;
    @Inject
    public ApsContextDb db;
    //public String businessPrimaryKeyStr = "cbusinessprimarykeystr";//判重的业务key
    /**
     *〈一句话功能简述〉组装删除需要的表名称信息
     *<P/>
     *〈功能详细描述〉
     * <AUTHOR>
     * @version 1.0, 2023/3/27
     * @param collectParamsBean
     * @return void
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     */
    public abstract void makeFinDeleteParamsBean(CollectParamsBean collectParamsBean);

    public Map batchDelete(Params params,String errorListMsgKeys){
        List<Map> deleteList = params.getListMap("row");
        List<Map> list = new ArrayList<>();
        for (Map deleteMap : deleteList) {
            String cdatasource = CollectionUtil.getStringFromMap(deleteMap, "cdatasource");
            if(StringUtils.isNotBlank(cdatasource)) {//不是预制数据 进行批量删除
                list.add(deleteMap);
            }
        }
        deleteList.clear();
        deleteList.addAll(list);
        CollectParamsBean collectParamsBean = new CollectParamsBean();
        makeFinDeleteParamsBean(collectParamsBean);
        Map<String, Object> deleteResult = finDeleteInterface.batchDelete(collectParamsBean,errorListMsgKeys,deleteList);
        /*String msg = CollectionUtil.getStringFromMap(deleteResult, "msg");
        if (!CollectionUtil.getBoolean(deleteResult, "isSuccess", true)) {
            return new APSResponseVo(msg);
        }*/
        //return new APSResponseVo(deleteResult);
        return deleteResult;
    }
    public APSResponseVo delete(Params params,String errorListMsgKeys){
        Map deleteMap = params.getMap("onerow");
        CollectParamsBean collectParamsBean = new CollectParamsBean();
        makeFinDeleteParamsBean(collectParamsBean);
        Map<String, Object> deleteResult = finDeleteInterface.delete(collectParamsBean,deleteMap,errorListMsgKeys);
        String msg = CollectionUtil.getStringFromMap(deleteResult, "msg");
        if (!CollectionUtil.getBoolean(deleteResult, "isSuccess", true)) {
            return new APSResponseVo(msg);
        }
        return new APSResponseVo().success().datagram(msg);
    }
    public Object doMessage(Params params,String listTableName,String errorListMsgKeys) {
        List<Map> list = params.getListMap(listTableName);
        Map deleteMap = list.get(0);
        CollectParamsBean collectParamsBean = new CollectParamsBean();
        makeFinDeleteParamsBean(collectParamsBean);
        Map<String, Object> deleteResult = finDeleteInterface.delete(collectParamsBean,deleteMap,errorListMsgKeys);
        String msg = CollectionUtil.getStringFromMap(deleteResult, "msg");
        if (!CollectionUtil.getBoolean(deleteResult, "isSuccess", true)) {
            throw new BusinessException(msg);
        }
        //补充删除收集日志（场景：同一个列表数据，第一次收集成功，第二次收集失败并且产生收集日志。此时行内删除明细）
        //makeBusinessPrimaryKeyStr(businessKeyStr, deleteMap);
        //db.deleteT(logTableName,deleteMap,StaticCodeClass.businessPrimaryKeyStr);
        return null;
    }
    /**
     *〈一句话功能简述〉删除凭证后调用ms
     *<P/>
     *〈功能详细描述〉 取消银行回单关联
     * <AUTHOR>
     * @version 1.0, 2024/1/23
     * @param voucherList
     * @return void
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     * @deprecated
     */
    public void cancelbankReceiptRel(List<Map> voucherList) {
        Message msg = new Message("da.common.ms.ResetVoucherIdMs");
        msg.publish(voucherList);
    }
}
