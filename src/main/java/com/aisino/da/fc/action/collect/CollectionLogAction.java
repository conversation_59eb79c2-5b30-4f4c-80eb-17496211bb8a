package com.aisino.da.fc.action.collect;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.ContentType;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CastUtil;
import com.aisino.aps.common.pojo.vo.APSResponseVo;
import com.aisino.da.common.util.BigDecimalUtil;
import com.aisino.da.fc.service.collect.CollectionLogService;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新增收集日志 ‘数据详情’、‘文件详情’ 查询方法
 */
@Action("da/fc/collectionlog")
public class CollectionLogAction
{
    @Inject
    private CollectionLogService collectionLogService;

    @ContentType
    @Request.Post("/getAllFile")
    public APSResponseVo getAllFile(Params params)
    {
        String cguid = params.getString("cguid");
        if (StringUtils.isEmpty(cguid))
        {
            return new APSResponseVo("查询文件详情时,获取行id为空,请重新选择并查看");
        }
        List<Map> fileList = collectionLogService.getFileList(cguid);

        Map reMap = new HashMap<>();
        reMap.put("data",fileList);
        reMap.put("code","0000");
        reMap.put("msg","查询成功");
        return new APSResponseVo(reMap);
    }

    @ContentType
    @Request.Post("/getSuccessFile")
    public APSResponseVo getSuccessFile(Params params)
    {
        String cguid = params.getString("cguid");
        if (StringUtils.isEmpty(cguid))
        {
            return new APSResponseVo("查询文件详情时,获取行id为空,请重新选择并查看");
        }
        Map successnum = collectionLogService.getSuccessFile(cguid);

        Map reMap = new HashMap<>();
        reMap.putAll(successnum);
        reMap.put("code","0000");
        reMap.put("msg","查询成功");
        return new APSResponseVo(reMap);
    }

    @ContentType
    @Request.Post("/getBatchBodyList")
    public APSResponseVo getBatchBodyList(Params params)
    {
        String cguid = params.getString("cguid", "");
        if (StringUtils.isEmpty(cguid))
        {
            return new APSResponseVo("查询数据详情时,获取行id为空,请重新选择并查看");
        }
        Map reMap = new HashMap<>();
        //暂时认为所有资料通用 若存在不通用各个资料自行处理
        List<Map> batchbodylist = collectionLogService.queryBatchBody(cguid);
        if (batchbodylist.isEmpty()){
            reMap.put("code","0001");
            reMap.put("msg","未获取到数据详情");
        }else {
            reMap.put("data",batchbodylist);
            reMap.put("code","0000");
            reMap.put("msg","查询成功");
        }
        return new APSResponseVo(reMap);
    }

    @ContentType
    @Request.Post("/selectFiles")
    public APSResponseVo selectFiles(Params params) {
        Map reMap = new HashMap<>();
        String cguid = params.getString("cguid");
        if (StringUtils.isEmpty(cguid)) {
            return new APSResponseVo("查询文件详情时,获取行id为空,请重新选择并查看");
        }
        List<Map> fileList = collectionLogService.getFileList(cguid);
        Map numMap = collectionLogService.getSuccessFile(cguid);
        double successnum = CastUtil.castDouble((numMap.get("successnum")));
        double countnum = CastUtil.castDouble((numMap.get("countnum")));

        //  若总数等于0 ，则有问题直接抛出默认数据
        reMap.put("totalNum", countnum);
        reMap.put("successNum", successnum);
        reMap.put("percent", BigDecimalUtil.calculatePercentage(successnum, countnum, 2));
        reMap.put("data", fileList);
        return new APSResponseVo(reMap);
    }



}
