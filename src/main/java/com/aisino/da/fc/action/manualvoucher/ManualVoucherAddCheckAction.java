package com.aisino.da.fc.action.manualvoucher;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.pojo.vo.APSResponseVo;

import java.util.HashMap;
import java.util.Map;

/**
 * ManualVoucherAddCheckAction
 *
 * <AUTHOR>
 * @version 1.0, 2024/5/30
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Action("da/fc/fin/manualvoucher")
public class ManualVoucherAddCheckAction {
    @Inject
    public ApsContextDb db;
    @Request.Post("/addcheck")
    public APSResponseVo addCheck(Params params) {
        String currentUserId = SessionHelper.getCurrentUserId();
        Map temp = getUserDetial(currentUserId);
        boolean isSuccessRtn = true;
        HashMap<Object, Object> map = new HashMap<>();
        if(temp==null||temp.get("cemp")==null){
            isSuccessRtn = false;
            map.put("msg", "登录用户未关联人员，禁止收集");
        }
        map.put("isSuccess", isSuccessRtn);
        return new APSResponseVo(map);
    }
    public Map getUserDetial(String currentUserId)
    {
        DbService dbService = new ApsContextDb().getDb();

        return dbService.queryMap("select cemp from aos_rms_user a where a.cGuid=?", currentUserId);
    }
}
