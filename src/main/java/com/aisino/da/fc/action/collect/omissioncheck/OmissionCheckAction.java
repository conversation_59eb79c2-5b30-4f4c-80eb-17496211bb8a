package com.aisino.da.fc.action.collect.omissioncheck;

import com.aisino.aosplus.core.ConfigHelper;
import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.pojo.vo.APSResponseVo;
import com.aisino.da.fc.service.bean.OmissionCheckBean;
import com.aisino.da.fc.service.omissioncheck.OmissionCheckTemplate;
import com.aisino.da.fc.service.omissioncheck.SomeOmissionCheckInterface;
import com.aisino.da.fc.service.omissioncheck.VoucherOmissionCheckInterface;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * OmissionCheckAction
 *
 * <AUTHOR>
 * @version 1.0, 2024/8/14
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Action("da/fc/fin")
public class OmissionCheckAction extends OmissionCheckTemplate {
    @Inject
    public ApsContextDb db;
    @Inject
    public VoucherOmissionCheckInterface voucherOmissionCheckImpl;
    @Inject
    public SomeOmissionCheckInterface someOmissionCheckImpl;
    @Request.Post("/voucher/voucheromissioncheckaction")
    public APSResponseVo voucherOmissionCheck(Params params) {
        /*StringBuffer msg = new StringBuffer();
        StringBuffer cfilecontents = new StringBuffer();
        Map map = db.queryMap("select filename,dafilepath,ccreatedate from da_files_2024 p order by  cCreateDate desc");
        FileInfoService.getFileInfoStr(map,msg,cfilecontents);*/

      /*  StringBuffer msg = new StringBuffer();
        StringBuffer cfilecontents = new StringBuffer();
        FileInfoService.getFileInfoStr("D:\\新建文件夹", msg, cfilecontents);*/
        StringBuffer where = new StringBuffer(" where ");
        //期间优先级
        OmissionCheckBean omissionCheckBean = new OmissionCheckBean();
        checkPreconditionsList(omissionCheckBean,where,"01");
        ArrayList<Object> messages = new ArrayList<>();
        messages.add("查询结果：");
        ArrayList<Map> insertList = new ArrayList<>();
        voucherOmissionCheckImpl.checkRulesAndVoucherOmissionVal(omissionCheckBean,where,params,messages,insertList);
        HashMap<String, Object> rtn = makeRtn(messages, insertList);
        return new APSResponseVo(rtn);
    }
    @Request.Post("/accountreport/omissioncheckaction")
    public APSResponseVo accountReportOmissionCheck(Params params) {
        String cyear = params.getString("cyear");
        String cmonth = params.getString("cmonth");
        if(StringUtils.isEmpty(cmonth)){
            throw new BusinessException("请输入会计月");
        }else {
            try{
                Integer month = Integer.valueOf(cmonth);
                cmonth = String.valueOf(month);
            }catch (Exception e){
                throw new BusinessException("请输入正确会计月，如01、1、13");
            }
            String regex = "^[1-9]\\d*$";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(cmonth);
            if (!matcher.matches()) {
                throw new BusinessException("请输入正确会计月，如01、1、13");
            }
        }
        String cperioddate;
        if(cmonth.length()==1){//1-9
            cperioddate = cyear+"-0"+cmonth;//会计期间（查询2023-01）
        }else {//01 12
            cperioddate =  cyear+"-"+cmonth;//会计期间（查询2023-12）
        }
        List<String> periodDateList = new ArrayList<>();
        periodDateList.add(cperioddate);
        HashMap<String, Object> rtn = someOmissionCheckImpl.check(params, periodDateList);
        return new APSResponseVo(rtn);
    }
    /**
     * 账簿 其他会计资料
     * @param params
     * @return
     */
    @Request.Post("/some/someomissioncheckaction")
    public APSResponseVo someOmissionCheck(Params params) {

        String cperiodDateStr = params.getString("cperioddate");


        String[] periodDateArray = cperiodDateStr.split(",");
        if(!cperiodDateStr.contains(",")){
            throw new BusinessException("会计期间格式不对:"+cperiodDateStr);
        }
        String[] startDateArr = periodDateArray[0].split("-");
        String[] endDateArr = periodDateArray[1].split("-");
        LocalDate startDate = LocalDate.of(Integer.valueOf(startDateArr[0]).intValue(),
                Integer.valueOf(startDateArr[1]).intValue(), 1);
        LocalDate endDate = LocalDate.of(Integer.valueOf(endDateArr[0]).intValue(),
                Integer.valueOf(endDateArr[1]).intValue(), 1);
        // 开始时间必须小于结束时间
        if (startDate.isAfter(endDate)) {
            throw new BusinessException("会计期间的开始日期必须小于结束日期:"+cperiodDateStr);
        }
        //配置库不生成数据
        String isInsert = ConfigHelper.getString("config.server");
        if(StringUtils.isNotBlank(isInsert)){
            return null;
        }
        List<String> periodDateList = new ArrayList<>();
        while (startDate.isBefore(endDate)) {//开始年月日到结束年月日
            String startPeriodDate = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            if(!periodDateList.contains(startPeriodDate)){//只保存年月信息
                periodDateList.add(startPeriodDate);
            }
            startDate = startDate.plusDays(1);
        }
        String endPeriodDate = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        if(!periodDateList.contains(endPeriodDate)){//只保存年月信息
            periodDateList.add(endPeriodDate);
        }
        HashMap<String, Object> rtn = someOmissionCheckImpl.check(params, periodDateList);
        return new APSResponseVo(rtn);
    }

}
