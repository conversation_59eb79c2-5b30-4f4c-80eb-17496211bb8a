package com.aisino.da.fc.action.layoutGeneration;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aps.common.pojo.vo.APSResponseVo;
import com.aisino.da.fc.service.layoutGeneration.LayoutGenerationCommonService;

import java.io.IOException;
import java.util.Map;

/*版式生成*/
@Action("da/fc/layoutGenCommonAction")
public class LayoutGenCommonAction {

    @Inject
    private LayoutGenerationCommonService layoutGenerationCommonService;

    @Request.Post("layoutGenCommon")
    public APSResponseVo layoutGenCommon(Params params) throws IOException {
        Map returnMap = layoutGenerationCommonService.genCommonPDF(params);
        return new APSResponseVo(APSResponseVo.SUCCESS_CODE,null,returnMap);
    }

    @Request.Post("layoutGenCommonBill")
    public APSResponseVo layoutGenCommonBill(Params params) throws IOException {
        Map returnMap = layoutGenerationCommonService.genCommonPDFBill(params);
        return new APSResponseVo(APSResponseVo.SUCCESS_CODE,null,returnMap);
    }
}
