package com.aisino.da.fc.action.bankstatement;


import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.pojo.vo.APSResponseVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/05/12/17:20
 */
@Action(value = "da/fc/bankstatement")
public class BankStatementAction {

    @Inject
    private ApsContextDb db;

    @Request.Post("batchdelete")
    public APSResponseVo batchDelete(Params params)  {

        List<Map> list = (List)params.get("rows");
        int total = params.getInt("total");
        int success = list.size();
        int fail = total - success;
        String msgInfo = "本次共删除%d条，成功%d条，失败%d条。";
        db.batchDeleteT("da_fc_bankstatement","cguid",list);
        HashMap<String, Object> rtn = new HashMap<>();
        rtn.put("total", total);
        rtn.put("success", success);
        rtn.put("fail", fail);
        ArrayList<Object> messages = new ArrayList<>();
        messages.add(String.format(msgInfo, total, success, fail));
        rtn.put("messages", messages);
        return new APSResponseVo(rtn);
    }
}
