package com.aisino.da.fc.crontab.bankocr;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Bean;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.quartz.utils.SynDbUtil;
import com.aisino.aps.tt.quartz.job.AJob;
import com.aisino.da.fc.service.bankreceipt.ocr.BankReceiptOcrService;
import com.aisino.da.fc.util.bankreceipts.ocrbank.BankConstants;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;
import java.util.Map;
/**
 * 银行回单识别后台定时任务
 *
 * <AUTHOR>
 * @date 2024/12/17
 */
@Bean
public class BankReceiptOcrASPJob extends AJob {
    private static final Logger logger = LoggerFactory.getLogger(BankReceiptOcrASPJob.class);
    @Inject
    private BankReceiptOcrService service;

    int count = 1;

    @Override
    public Object execute(SynDbUtil.DB db, Map allParams, Map<String, Object> params, String ctenantguid) {
        logger.debug("银行回单识别定时任务启动[BankReceiptOcrASPJob]启动---------------------- flag:{}   count:{}  date{}:", BankConstants.BANK_RECEIPT_FLAG, count, new Date());
        if ("1".equals(BankConstants.BANK_RECEIPT_FLAG)) {
            logger.debug("银行回单识别定时任务开始---------------------- ");
            if (count == 1) {
                count = 0;
                try {
                    while (true) {
                        logger.debug("银行回单识别定时任务启动[BankReceiptOcrASPJob]--------------------进循环-------------------");
                        // 查询下载日志表中是否存在未识别的文件
                        String dsId = db.getDbService().getDsID();
                        DbService dbService = new ApsContextDb().getDb(dsId, true);
                        dbService.checkToStartTransaction();
                        List<Map> list = service.getNotIdentityBankReceiptFiles(dbService);
                        dbService.commit();
                        if (CollectionUtils.isEmpty(list)) {
                            break;
                        }
                        // 去处理
                        service.getFilesAndOcrIdentify(list);

                    }
                    count = 1;
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    count = 1;
                }

            }

        }
        return null;
    }

    @Override
    public List<JobParams> forgeParams() {
        return null;
    }

    @Override
    public String jobName() {
        return null;
    }
}
