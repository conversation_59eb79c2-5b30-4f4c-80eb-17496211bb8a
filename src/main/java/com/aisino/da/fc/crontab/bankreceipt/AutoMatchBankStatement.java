package com.aisino.da.fc.crontab.bankreceipt;

import java.util.List;
import java.util.Map;

import com.aisino.aosplus.core.util.MapUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbHelper;
import com.aisino.aosplus.core.ioc.annotation.Bean;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aps.quartz.utils.SynDbUtil.DB;
import com.aisino.aps.tt.quartz.job.AJob;
import com.aisino.da.fc.service.bankreceipt.BankReceiptService;
import com.aisino.imm.util.DateUtils;

/**
 * <AUTHOR>
 * @Description 银行回单匹配定时任务
 * @Date  2025-02-24 14:35
**/
@Bean
public class AutoMatchBankStatement extends AJob{

	private static Log log = LogFactory.getLog(AutoMatchBankStatement.class);
	
	@Inject
	private BankReceiptService bankReceiptService;
	
	@Override
	public Object execute(DB db, Map allParams, Map<String, Object> params, String ctenantguid) {
		log.info("******回单与流水匹配定时任务AutoMatchBankStatement******");
		// 设置数据库对象
        String dsid = db.getDbService().getDsID();
        DbHelper.setCurrentDsID(db.getDbService().getDsID());
     // 获取所有的执行组织
        List<String> corginids = super.getCorginids();
        String str = corginids.get(0);
        if(StringUtil.isEmpty(str)) {
            throw new BusinessException("调度计划应用组织不能为空");
        }
        StringBuffer rtnmsg = new StringBuffer();
		boolean executeStatus = true;
		for(String orginid:corginids){
			Params par = new Params();
			String ccreatorid = "";
			String ccreatorid_name = "";
			Map settingMap = db.getDbService().queryMap("select * from da_param_setting where corgnid = ?",orginid);
			if(MapUtil.isNotEmpty(settingMap)){
				ccreatorid = settingMap.get("recipientid")==null?"":settingMap.get("recipientid").toString();
				ccreatorid_name = settingMap.get("recipient_name")==null?"":settingMap.get("recipient_name").toString();
			}
			String corgnname = db.getDbService().queryColumn("select cname from aos_orgn where cguid = ?",orginid);
			if(StringUtil.isEmpty(ccreatorid)){
				executeStatus = false;
				rtnmsg.append("组织：["+corgnname+"]失败，未设置定时任务执行人。");
				continue;
			}
			par.put("corgnid", orginid);
			par.put("dsid", dsid);
			par.put("ccreatorid", ccreatorid);
			par.put("ccreatorid_name", ccreatorid_name);
			Map rtn = bankReceiptService.bankreceiptMatchBankstatement(par);
			log.info("回单与流水匹配定时结果：["+corgnname+"]"+rtn);
			String success = rtn.get("success")==null?"":rtn.get("success").toString();
			String msg = rtn.get("msg")==null?"":rtn.get("msg").toString();
			if("0".equals(success)) {
				bankReceiptService.warningXxfs("回单与流水匹配定时结果："+msg, orginid,DateUtils.getTime(),dsid);
			}
			rtnmsg.append("组织：["+corgnname+"]成功，"+msg);
		}
		if(!executeStatus){
			throw new BusinessException(rtnmsg.toString());
		}
		return rtnmsg.toString();
	}

	@Override
	public List<JobParams> forgeParams() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String jobName() {
		// TODO Auto-generated method stub
		return "回单匹配流水";
	}

}
