package com.aisino.da.fc.crontab.bankorc;

import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.util.DateUtil;
import com.aisino.aosplus.plugin.job.BaseJob;
import com.aisino.aosplus.plugin.job.Job;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.fc.util.bankreceipts.ocrbank.BankConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.Date;
/**
 * 定时任务---删除临时表中无用的数据
 *
 * <AUTHOR>
 * @date 2024/12/17
 */
@Job("0 0 23 * * ?")
public class DeleteTempDataJob extends BaseJob {
    private static final Logger logger = LoggerFactory.getLogger(DeleteTempDataJob.class);
    private static final int NUM = 7;

    @Override
    public void execute() {
        if ("1".equals(BankConstants.TEMP_DATA_FLAG)) {
            logger.debug("ocr回单识别临时数据定期删除[DeleteTempDataJob]启动---------------------- date{}:", new Date());
            Date currentDate = new Date();
            System.out.println("当前日期: " + currentDate);
            // 使用Calendar对象进行日期操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            // 减去一天
            calendar.add(Calendar.DATE, -NUM);
            // 获取修改后的日期
            Date previousDate = calendar.getTime();
            System.out.println("减" + NUM + "天后的日期: " + previousDate);
            String deleteSql = "delete from da_fc_bank_receipt_file_temp where " +
                    "STR_TO_DATE(cCreateDate, '%Y-%m-%d %H:%i:%s') < STR_TO_DATE(?, '%Y-%m-%d %H:%i:%s')" ;
            String dates = DateUtil.format(previousDate, "yyyy-MM-dd HH:mm:ss");
            DbService db = new ApsContextDb().getDb();
            db.execute(deleteSql, dates);
            db.commit();
        }
    }
}
