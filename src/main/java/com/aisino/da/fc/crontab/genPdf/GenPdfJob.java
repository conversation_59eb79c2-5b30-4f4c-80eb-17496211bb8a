package com.aisino.da.fc.crontab.genPdf;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Bean;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.quartz.utils.SynDbUtil;
import com.aisino.aps.tt.quartz.job.AJob;
import com.aisino.da.core.util.ArrayUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
*@Description
*@Param
*@Return
*<AUTHOR>
*@Date 2025-03-06
*@Time 13:39
*/


@Bean
public class GenPdfJob extends AJob {
    private static Log log = LogFactory.getLog(GenPdfJob.class);


    @Override
    public Object execute(SynDbUtil.DB db, Map allParams, Map<String, Object> params, String ctenantguid) {
        log.info("******GenPdfJob调度计划开始******");
        log.debug("******GenPdfJob调度计划开始******");
        List<String> corginids = super.getCorginids();
        DbService dbService = db.getDbService();
        String dbId = dbService.getDsID();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        log.error("#####job组织参数:"+corginids);
        log.debug("#####job组织参数:"+corginids);
        if(!ArrayUtil.isEmpty(corginids)){
            Map parMap = new HashMap();
            parMap.put("cgenpdfstatus","0");
            String str = corginids.get(0);
            if(ArrayUtil.isBlank(str)) {
                throw new BusinessException("调度计划应用组织不能为空");
            }
            corginids.forEach(orginid -> {
                try {
                    if (!ArrayUtil.isBlank(orginid)) {
                        /*组织id*/
                        parMap.put("orginid", orginid);
                        // 当前执行时间
                        Date date = new Date();
                        String format = df.format(date);
                        parMap.put("currentDate", format);
                        parMap.put("dbid", dbId);
                        parMap.put("isAuto", "0");
                        log.error("#####开始调用版式生成:" + corginids);
                        GenPdfAuto.autoGenPdf(parMap);
                        log.error("#####结束调用版式生成:" + corginids);
                    }
                }catch (Exception e){
                    log.info("生成版式文件执行异常---->"+e.getMessage());
                    // 执行失败发送常规消息
                    log.info("生成版式文件发送执行失败消息--->开始发送");
                    ApsContextDb apsContextDb = new ApsContextDb();
                    apsContextDb.getDb(dbId);
                    Object serveMs = MessagesSent.genpdfMessageSent(apsContextDb, orginid,"da_fc_auto_genpdf_message","凭证自动生成版式文件定时任务执行失败");
                    if(serveMs != null){
                        log.info("生成版式文件定时任务执行失败---->发送消息成功");
                    }else{
                        log.info("生成版式文件任务执行失败---->发送消息失败");
                    }
                }
            });
        }else{
            throw new BusinessException("组织参数为空:"+corginids);
        }



        return null;
    }

    @Override
    public List<JobParams> forgeParams() {
        return null;
    }

    @Override
    public String jobName() {
        return "版式生成";
    }
}
