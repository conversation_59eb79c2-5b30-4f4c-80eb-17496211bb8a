package com.aisino.da.fc.crontab.genPdf;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.MapUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName MessagesSent
 * @Description 定时失败消息发送
 * <AUTHOR>
 * @Date 2024/1/19
 */
public class MessagesSent {
    private static final Log log = LogFactory.getLog(MessagesSent.class);

    /**
     * 版式生成发送消息
     * @param db
     * @param corguid
     * @param errorMsg
     * @return
     */
    public static Object genpdfMessageSent(ApsContextDb db,String corguid,String messageName,String errorMsg){
        // 获取根据消息类型配置的消息方案
        Map<String,Object> messageMap = db.queryMapById("da_fc_gen_pdf_mes_sql.getMessageInfo",messageName);
        if (MapUtil.isEmpty(messageMap)) {
            log.info("定时执行发送消息失败,未配置消息方案");
            throw new BusinessException("不存在有效的预警方案，请配置预警方案后再次发送！");
        }
        // 获取组织名称
        Map<String,Object> organMap = db.queryMapById("da_fc_gen_pdf_mes_sql.getOrganInfo",corguid);
        String orgnName = CollectionUtil.getStringFromMap(organMap, "cname");

        Map<String, Object> userMap = MessagesSent.getUserMap(db, corguid);
        String cuserid = CollectionUtil.getStringFromMap(userMap, "recipientid");
        // 构建参数
        List<Map> paramsList = new ArrayList<>(1);
        Map<String,Object> paramsMap = new HashMap<>(5);
        paramsList.add(paramsMap);
        paramsMap.put("corgnid", "");
        paramsMap.put("corgnname", "");
        paramsMap.put("cuserid", cuserid);
        paramsMap.put("cusername", "");
        paramsMap.put("content", MessagesSent.getDate() + ","+ orgnName +","+ errorMsg);
        paramsMap.put("cmsgsendid", cuserid);
        paramsMap.put("cmsgorgnid", corguid);
        /* 常规提醒调用发送消息方法
         * 必填项【warningPlanID 预警方案ID
         * 必填项【businessList 参数信息 List<Map>
         */
        Params par = new Params();
        par.put("action","sendNormalMsg");
        // 预警方案id
        par.put("warningPlanID", CollectionUtil.getStringFromMap(messageMap, "cguid"));
        //参数信息
        par.put("businessList",paramsList);
        // 数据源
        par.put("dbID",db.getDbService().getDsID());
        // 调用服务
        Message msg = new Message("aps.msg.normal.send");
        return msg.publish(par);
    }

    /**
     * 获取当前年月日
     */
    public static String getDate() {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    public static Map<String,Object> getUserMap(ApsContextDb db, String corgnid) {
        // 根据组织获取参数配置的消息接受人id
        Map<String,Object> userMap = db.queryMapById("da_fc_gen_pdf_mes_sql.getUserInfoByOrgnid", corgnid);
        String recipientid = CollectionUtil.getStringFromMap(userMap, "recipientid");
        // 如果当前用户未配置消息接收人的id，则设置为默认接收人信息
        if(StringUtil.isEmpty(recipientid)){
            userMap = db.queryMapById("da_fc_gen_pdf_mes_sql.getDefaultUserInfo");
        }
        return userMap;
    }
}