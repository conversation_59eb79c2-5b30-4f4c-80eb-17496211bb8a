package com.aisino.da.afk.dao.packbox.impl;


import com.aisino.aosplus.core.dao.sql.Eso;
import com.aisino.aosplus.core.mvc.annotation.Repository;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.afk.dao.packbox.PackboxDao;

import java.util.List;
import java.util.Map;

/**
* @title PackboxDaoImpl.java
* @description <实体装盒持久类>
* <AUTHOR>
*/
@Repository
public class PackboxDaoImpl implements PackboxDao {

    /**
     * @description <查询方法>
     * @param sqlId
     * @param params
     * @return java.util.List<java.util.Map>
     * <AUTHOR>
     */
    @Override
    public List<Map> queryListMapBySqlId(String sqlId, Params params) {
        ApsContextDb db = new ApsContextDb();
        return db.queryMapListById(sqlId,params);
    }

    /**
     * @description <修改方法>
     * @param sqlId
     * @param params
     * @return java.util.List<java.util.Map>
     * <AUTHOR>
     */
    @Override
    public int executeSqlById(String sqlId, Params params) {
        ApsContextDb db = new ApsContextDb();
        return db.getDb().updateById(sqlId,params);
    }

    /**
     * @description <批量修改方法>
     * @param esos
     * @return java.util.List<java.util.Map>
     * <AUTHOR>
     */
    @Override
    public void executeAllEso(List<Eso> esos) {
        ApsContextDb db = new ApsContextDb();
        db.getDb().executeAllEso(esos);
    }
}
