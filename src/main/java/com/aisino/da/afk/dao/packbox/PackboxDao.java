package com.aisino.da.afk.dao.packbox;

import com.aisino.aosplus.core.dao.sql.Eso;
import com.aisino.aosplus.core.ioc.annotation.Impl;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.da.afk.dao.packbox.impl.PackboxDaoImpl;

import java.util.List;
import java.util.Map;

@Impl(PackboxDaoImpl.class)
public interface PackboxDao {
    /**
     * 实体组卷表
     * */
    String TABLE_NAME_ENTITYFILES = "da_afk_entityfiles";

    /**
     * 查询符合条件的多条记录
     * @param sqlId
     * @param params
     * @return
     */
    List<Map> queryListMapBySqlId(String sqlId,Params params);

    int executeSqlById(String sqlId, Params params);

    /**
     * 批量执行eso
     * @param esos
     * @return
     */
    void executeAllEso(List<Eso> esos);
}
