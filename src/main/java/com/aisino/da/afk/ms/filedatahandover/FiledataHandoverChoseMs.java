package com.aisino.da.afk.ms.filedatahandover;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.sql.Eso;
import com.aisino.aosplus.core.dao.sql.SqlInfo;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.pojo.vo.APSResponseVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Ms(value = "da.afk.filedatahandoverchose.list.nomainproc", desp = "资料移交列表查询")
public class FiledataHandoverChoseMs implements Ims {

    @Inject
    private ApsContextDb apsContextDb;

    @Override
    public Object doMessage(Object... args) {
        if (args == null) {
            throw new BusinessException("参数不能为空");
        }
        Params params = (Params) args[0];
        Map fieldMap = new HashMap<>();
        if (params.getListMap("conditionList") != null && params.getListMap("conditionList").size() != 0){
            params.getListMap("conditionList").forEach(item -> {
                fieldMap.put(item.get("field"), item.get("value"));
            });
        }
        //MD返回来的月份不带0
        int month = CollectionUtil.getIntFromMap(fieldMap,"imonth");
        if(CollectionUtil.getStringFromMap(fieldMap,"iyear")!=null && !"".equals(fieldMap.get("iyear"))){
            if(CollectionUtil.getStringFromMap(fieldMap,"imonth")!=null && !"".equals(fieldMap.get("imonth"))){
                if(month>0 && month<10){
                    fieldMap.put("cfiledataperiod", fieldMap.get("iyear")+"-0"+month);
                }else {
                    fieldMap.put("cfiledataperiod", fieldMap.get("iyear")+"-"+month);
                }
            }else{
                fieldMap.put("cfiledataperiod", fieldMap.get("iyear")+"-");
            }
        }else {
            if(CollectionUtil.getStringFromMap(fieldMap,"imonth")!=null && !"".equals(fieldMap.get("imonth"))){
                if(month>0 && month<10){
                    fieldMap.put("cfiledataperiod", "-0"+month);
                }else {
                    fieldMap.put("cfiledataperiod", fieldMap.get("iyear")+"-"+month);
                }
            }
        }

        HashMap<Object, Object> hashMap = new HashMap<>();
        String cfiledatatype = CollectionUtil.getStringFromMap(fieldMap,"cfiledatatype");
        Map usingmap = apsContextDb.getOneRecord("SELECT usingdata FROM da_param_setting where corgnid=?",new Object[]{SessionHelper.getCurrentOrgnId()});
        String usingdata = CollectionUtil.getStringFromMap(usingmap,"usingdata");
        if(usingdata!=null && !"".equals(usingdata)){
            if(usingdata.indexOf(cfiledatatype)>-1){
                String sql;
                switch (cfiledatatype){
                    case "ZB":
                        sql = apsContextDb.getDb().getSql("da_afk_filedatahandoverchose.zblistsearch");
                        break;
                    case "BG":
                        sql = apsContextDb.getDb().getSql("da_afk_filedatahandoverchose.bglistsearch");
                        break;
                    case "QT":
                        sql = apsContextDb.getDb().getSql("da_afk_filedatahandoverchose.qtlistsearch");
                        break;
                    case "FSZL01":
                        sql = apsContextDb.getDb().getSql("da_afk_filedatahandoverchose.djlistsearch");
                        break;
                    case "FSZL02":
                        sql = apsContextDb.getDb().getSql("da_afk_filedatahandoverchose.pjlistsearch");
                        break;
                    case "FSZL03":
                        sql = apsContextDb.getDb().getSql("da_afk_filedatahandoverchose.yhhdlistsearch");
                        break;
                    default:
                        sql = apsContextDb.getDb().getSql("da_afk_filedatahandoverchose.pzlistsearch");
                }
                SqlInfo sqlInfo = new SqlInfo(sql);
                Eso eso = sqlInfo.getEso(fieldMap);
                Map mapList = apsContextDb.getDb().queryPageMapList(eso.getSql(), (int) params.get("pageno"),
                        (int) params.get("pagesize"), eso.getParams());
                List<Map> listdata = (List<Map>) mapList.get("records");
                if(listdata!=null && listdata.size()>0){
                    hashMap.put(params.get("entityid"), mapList.get("records"));
                    hashMap.put("Page", mapList.get("Page"));
                }
            }
        }

        return hashMap;
    }

}
