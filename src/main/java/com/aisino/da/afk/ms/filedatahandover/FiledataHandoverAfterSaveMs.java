package com.aisino.da.afk.ms.filedatahandover;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.shortlife.ShortLivePool;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.afk.dao.filedatahandover.FiledataHandoverDao;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

/**
 * <AUTHOR>
 * @desp 资料移交保存时缓存处理
 */
@Ms(value = "da.afk.filedatahandover.aftersave", desp = "资料移交保存后")
public class FiledataHandoverAfterSaveMs  implements Ims {

    @Inject
    private ApsContextDb apsContextDb;

    @Inject
    private FiledataHandoverDao filedataHandoverDao;


    @Override
    public Object doMessage(Object... args) {
        if (args == null) {
            throw new BusinessException("参数不能为空");
        }
        Vector<String> cacheValues = new Vector();//缓存值
        Params params = (Params) args[0];
        Map datamap = params.getMap("data");
        Map headermap = params.getMap("header");
        Map maininfomap =  (Map) datamap.get("a17311d7d4d6116066bc327d7b3862e9");
        Map childmap = (Map) maininfomap.get("children");
        List<Map> filedatalist = (List) childmap.get("538c3d93ca8d02612b4c57211a6538ab");

        //添加并发校验
        boolean stateflag =true;
        String urlstr = CollectionUtil.getStringFromMap(headermap,"url");
        String formstate = urlstr.substring(urlstr.indexOf("cstate")+7,urlstr.indexOf("&",urlstr.indexOf("cstate")));
        if("add".equals(formstate)){//新增时
            for(Map filedatamap : filedatalist){
                String filedatacguid = CollectionUtil.getStringFromMap(filedatamap,"cfileid");
                String filedatatype = CollectionUtil.getStringFromMap(filedatamap,"cfiledatatype");
                stateflag = filedataHandoverDao.checkfiledatastatus(filedatatype,filedatacguid,"addbefore");
                if(!stateflag) {//不是已收集状态，不允许保存
                    break;
                }
            }
            if(!stateflag){
                throw new BusinessException("勾选数据存在已经被操作，请刷新页面重试！");
            }
        }else{//修改时
            Map savemap = params.getMap("savedata");
            Map newmap = (Map) savemap.get("_new");
            List<Map> addlist = (List<Map>) newmap.get("da_afk_filedata_handoverline");
            if(addlist!=null && !addlist.isEmpty()){
                for(Map addmap : addlist){
                    String filedatacguid = CollectionUtil.getStringFromMap(addmap,"cfileid");
                    String filedatatype = CollectionUtil.getStringFromMap(addmap,"cfiledatatype");
                    stateflag = filedataHandoverDao.checkfiledatastatus(filedatatype,filedatacguid,"addbefore");
                    if(!stateflag) {//不是已收集状态，不允许保存
                        break;
                    }
                }
                if(!stateflag){
                    throw new BusinessException("勾选数据存在已经被操作，请刷新页面重试！");
                }
            }
        }

        boolean fail = false;  //true时缓存中正在装盒，装盒失败
        //并发校验
        for(Map cidmap : filedatalist){
            String cfileid = CollectionUtil.getStringFromMap(cidmap,"cfileid");//组卷表id
            if(StringUtil.isNotEmpty(getCacheValue("FiledataHanding"+cfileid))){
                //缓存中已存在
                fail = true;
            }else{
                cacheValues.add("FiledataHanding"+cfileid);
                setCacheValue("FiledataHanding"+cfileid,cfileid,1000L*60L);
            }
        }
        if(fail){
            delCacheValue(cacheValues);//清除缓存
            throw new BusinessException("勾选数据存在正在被操作，请刷新页面重试！");
        }else{
            //更新移交单状态
            params.put("chandoverstatus", "0");
            params.put("chandoverstatus_name", "待移交");
            params.put("cguid",maininfomap.get("cguid"));
            Boolean ctransform = filedataHandoverDao.updateTransform(params);
            if (!ctransform) {
                return "移交状态修改失败";
            }

            //更新资料收集表移交状态-移交中
            Map updatemap = new HashMap();
            updatemap.put("centityfilestatus","10");
            updatemap.put("centityfilestatus_name","移交中");
            for(Map map : filedatalist){
                //String rownum = CollectionUtil.getStringFromMap(map,"cfileid");
                String filedatatype = CollectionUtil.getStringFromMap(map,"cfiledatatype");
                String filedatacguid = CollectionUtil.getStringFromMap(map,"cfileid");
                updatemap.put("cguid",filedatacguid);
                filedataHandoverDao.updatefilecollectstatus(filedatatype, filedatacguid, updatemap);
            }
            delCacheValue(cacheValues);//清除缓存
        }

        return null;
    }
    /**
     * 取缓存值
     * @param key
     * @return
     */
    public static String getCacheValue(String key){
        Serializable s = ShortLivePool.getInstance().get(key);
        if(s == null){
            return null;
        }else{
            return s.toString();
        }
    }
    /**
     * 设置缓存
     * @param key
     * @param value
     * @param life
     */
    public static void setCacheValue(String key,String value,long life){
        ShortLivePool.getInstance().set("da_filedata_handing",key,value,life);
    }
    /**
     * 删除缓存
     * @param keys
     */
    public static void delCacheValue(Vector<String> keys){
        for(String key:keys){
            delCacheValue(key);
        }
    }
    /**
     * 删除缓存
     * @param key
     */
    public static void delCacheValue(String key){
        ShortLivePool.getInstance().remove(key);
    }

}
