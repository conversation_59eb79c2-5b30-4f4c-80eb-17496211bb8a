package com.aisino.da.afk.ms.filedatahandover;


import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.afk.dao.filedatahandover.FiledataHandoverDao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Ms(value = "da.afk.filedatahandover.afterdelete", desp = "资料移交删除后")

public class FiledataHandoverAfterDeleteMs implements Ims {

    @Inject
    private ApsContextDb apsContextDb;

    @Inject
    private FiledataHandoverDao filedataHandoverDao;

    @Override
    public Object doMessage(Object... args) {
        if (args == null) {
            throw new BusinessException("参数不能为空");
        }
        Params params = (Params) args[0];
        //更新资料收集表移交状态-收集成功
        String cguid = params.getString("cguid");
        List<Map> filedatalist = apsContextDb.queryMapList("SELECT cfiledatatype ,cfileid FROM da_afk_filedata_handoverline where cHeadId=? ", new Object[]{cguid});
        Map updatemap = new HashMap();
        updatemap.put("centityfilestatus","0");
        updatemap.put("centityfilestatus_name","收集成功");
        for(Map map : filedatalist){
            String filedatatype = CollectionUtil.getStringFromMap(map,"cfiledatatype");
            String filedatacguid = CollectionUtil.getStringFromMap(map,"cfileid");
            updatemap.put("cguid",filedatacguid);
            filedataHandoverDao.updatefilecollectstatus(filedatatype, filedatacguid, updatemap);
        }
        return null;
    }
}
