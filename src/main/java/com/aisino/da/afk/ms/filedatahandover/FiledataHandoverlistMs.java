package com.aisino.da.afk.ms.filedatahandover;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.sql.Eso;
import com.aisino.aosplus.core.dao.sql.SqlInfo;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;

import java.util.HashMap;
import java.util.Map;

@Ms(value = "da.afk.filedatahandover.list.nomainproc", desp = "资料移交列表查询")
public class FiledataHandoverlistMs implements Ims {

    @Inject
    private ApsContextDb apsContextDb;

    @Override
    public Object doMessage(Object... args) {
        if (args == null) {
            throw new BusinessException("参数不能为空");
        }
        Params params = (Params) args[0];
        if (params.getListMap("conditionList") != null && params.getListMap("conditionList").size() != 0){
            params.getListMap("conditionList").forEach(item -> {
                params.put(item.get("field").toString(), item.get("value"));
            });
        }
        if(params.getString("s_chandoverdept")!=null && !"".equals(params.getString("s_chandoverdept"))){
            Map handoverdeptmap = apsContextDb.getOneRecord("select cname from v_aps_department where cguid=?", params.get("s_chandoverdept"));
            params.put("handoverdeptname",handoverdeptmap.get("cname"));
        }
        if(params.getString("s_creceptiondept")!=null && !"".equals(params.getString("s_creceptiondept"))){
            Map handoverdeptmap = apsContextDb.getOneRecord("select cname from v_aps_department where cguid=?", params.get("s_creceptiondept"));
            params.put("receptiondeptname",handoverdeptmap.get("cname"));
        }
        Map<String, Object> rptlist = apsContextDb.queryPageMapListById("da_afk_filedatahandover.listsearch",params);
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put(params.get("entityid"), rptlist.get("records"));
        hashMap.put("Page", rptlist.get("Page"));
        return hashMap;
    }
}
