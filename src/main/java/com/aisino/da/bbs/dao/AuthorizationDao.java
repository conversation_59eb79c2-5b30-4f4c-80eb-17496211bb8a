package com.aisino.da.bbs.dao;

import com.aisino.aosplus.core.ioc.annotation.Impl;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.da.bbs.dao.impl.AuthorizationDaoImpl;

import java.util.List;
import java.util.Map;

@Impl(AuthorizationDaoImpl.class)
public interface AuthorizationDao {

    /**
     * 判断当前登录组织是不是根组织
     * @param params
     * @return
     */
    String isAble(Params params);


    /**
     * 查询列表数据
     * @param params
     * @return
     */
    List<Map> getSearchList(Params params);

    /**
     * 获取已授权和最大授权数
     * @param params
     * @return
     */
    String getAuthorizationNumber(Params params);


    /**
     * 进行授权处理并返回提示信息
     * @param params
     * @return
     */
    Map toAuthorization(Params params);


    /**
     * 取消授权
     * @param params
     * @return
     */
    String cancelAuthorization(Params params);
}
