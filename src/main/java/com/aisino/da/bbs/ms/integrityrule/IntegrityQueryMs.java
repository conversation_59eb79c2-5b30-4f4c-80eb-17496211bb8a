package com.aisino.da.bbs.ms.integrityrule;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName IntegrityQueryMs
 * @Description 完整性规则设置-查询
 * <AUTHOR>
 * @Date 2023/10/12 17:30
 */
@Ms(value = "da.bbs.IntegrityQueryMs" ,desp = "完整性规则设置-查询-列表服务后替换平台数据")
public class IntegrityQueryMs implements Ims {

    @Inject
    private ApsContextDb db;

    @Override
    public Object doMessage(Object... objects) {
        Params params = (Params)objects[0];
        int pageno = params.getInt("pageno");
        int pagesize = params.getInt("pagesize");
        // 获取实体id
        String entityid = params.getString("entityid");
        // 获取平台查询的数据
        Map<String,Object> result = params.getMap("result");
        String name = null;
        String code= null;
        List<Map> conditionList = params.getListMap("conditionList");
        if(CollectionUtil.isNotEmpty(conditionList)){
            for (Map map : conditionList) {
                String field = CollectionUtil.getStringFromMap(map, "field");
                String value = CollectionUtil.getStringFromMap(map, "value");
                if("ccode".equals(field)){
                    code = value;
                } else {
                    name = value;
                }
            }
        }
        Params par = new Params();
        par.put("corgnid",SessionHelper.getCurrentOrgnId());
        par.put("ccode",code);
        par.put("cname",name);
        List<Map> dataList = db.queryMapListById("da_bbs_integrity_rule.getIntegrityRuleListByCondition", par);
        Map<String, Object> pageMap = this.addPageFunction(dataList, pageno, pagesize);
        Map map = (Map) pageMap.get("Page");
        dataList = (List<Map>)pageMap.get("records");
        // 替换平台查询的数据
        result.put(entityid,dataList);
        result.put("Page",map);
        return params;
    }

    /**
     * 分页处理数据
     * @param dataList
     * @param pageIndex
     * @param pageSize
     * @return
     */
    private Map<String,Object> addPageFunction(List<Map> dataList, int pageIndex, int pageSize) {
        Map<String, Object> returnMap = new HashMap<>(2);
        // 存放分页记录
        Map<String, Integer> pageMap = new HashMap<>(4);
        // pageSize 每页大小
        pageMap.put("pageSize", pageSize);
        // pageNo  当前页
        pageMap.put("pageNo", pageIndex);
        // totalSize 总个数
        int totalSize = dataList.size();
        pageMap.put("totalSize", totalSize);
        // pageCount 总页数
        int pageCount = totalSize / pageSize + (totalSize % pageSize == 0 ? 0 : 1);
        pageMap.put("pageCount", pageCount);
        // 跳过多少数据也就是起始下标
        int fromIndex = (pageIndex - 1) * pageSize;
        // 通过stream流实现分页。
        dataList = dataList.stream().skip(fromIndex).limit(pageSize).collect(Collectors.toList());
        // 分页
        returnMap.put("Page", pageMap);
        // 数据
        returnMap.put("records", dataList);
        return returnMap;
    }
}
