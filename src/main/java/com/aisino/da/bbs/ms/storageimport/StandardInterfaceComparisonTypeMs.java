package com.aisino.da.bbs.ms.storageimport;

import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.aps.common.exception.ApsException;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName StandardInterfaceComparisonTypeMs.java
 * @Description 标准接口设置 对照类型
 * <AUTHOR>
 * @Date 2024年011月04日 1309:24:20
 * @Version 1.0.0
 */
@Ms(value = "da.bbs.standard.comparison.type", desp = "标准接口设置中对照类型ms")
public class StandardInterfaceComparisonTypeMs implements Ims {

    @Override
    public Object doMessage(Object... objects) {
        List<Map<String, String>> returnList = basicInfo();
        if (!(objects[0] instanceof Params)) {
            throw new ApsException("参数类型不正确");
        } else {
            Params params = (Params) objects[0];
            Map data = params.getMap("formData");
            List<String> list = (List<String>) data.get("source");

            for (String str : list) {
                // 01记账凭证 02单据资料 03 会计账簿 04会计报告 05其他会计资料 06银行回单07银行流水 08票据资料
                if ("01".equals(str)) {
                    getRelateInfo01(returnList);
                } else if ("02".equals(str)) {
                    getRelateInfo02(returnList);
                } else if ("08".equals(str)) {
                    getRelateInfo08(returnList);
                } else {
//                    getRelateInfo06(returnList);
                }
            }
        }
        returnList = returnList.stream().distinct().collect(Collectors.toList());
        return returnList;
    }


    /**
     * @return java.util.List<java.util.Map>
     * @description 基础信息
     * <AUTHOR>
     * @date 2024/11/4 13:11
     */
    private List<Map<String, String>> basicInfo() {
        Map<String, String> map = new HashMap<>(4);
        map.put("value", "1");
        map.put("label", "业务类型对照");
        Map<String, String> map2 = new HashMap<>(4);
        map2.put("value", "2");
        map2.put("label", "明细分类对照");
        Map<String, String> map3 = new HashMap<>(4);
        map3.put("value", "12");
        map3.put("label", "发票类型");
        List<Map<String, String>> returnList = new ArrayList<>();
        returnList.add(map);
        returnList.add(map2);
        returnList.add(map3);
        return returnList;
    }

    /**
     * @description 记账凭证返回的相关数据
     * <AUTHOR>
     * @date 2024/11/4 13:11
     */
    private void getRelateInfo01(List<Map<String, String>> returnList) {
//        3制单人、4审核人、5复核人、6记账人、9收集人
        Map<String, String> map = new HashMap<>(4);
        Map<String, String> map1 = new HashMap<>(4);
        Map<String, String> map2 = new HashMap<>(4);
        Map<String, String> map3 = new HashMap<>(4);
        Map<String, String> map4 = new HashMap<>(4);
        map.put("value", "3");
        map.put("label", "制单人");
        map1.put("value", "4");
        map1.put("label", "审核人");
        map2.put("value", "5");
        map2.put("label", "复核人");
        map3.put("value", "6");
        map3.put("label", "记账人");
        returnList.add(map);
        returnList.add(map1);
        returnList.add(map2);
        returnList.add(map3);
        returnList.add(map4);
    }

    /**
     * @description 单据资料返回的相关数据
     * <AUTHOR>
     * @date 2024/11/4 13:11
     */
    private void getRelateInfo02(List<Map<String, String>> returnList) {
//        3制单人、9收集人  7职员、8部门

        Map<String, String> map = new HashMap<>(4);
        Map<String, String> map2 = new HashMap<>(4);
        Map<String, String> map3 = new HashMap<>(4);
        map.put("value", "3");
        map.put("label", "制单人");
        map2.put("value", "7");
        map2.put("label", "职员");
        map3.put("value", "8");
        map3.put("label", "部门");
        returnList.add(map);
        returnList.add(map2);
        returnList.add(map3);
    }

    /**
     * @description 票据资料返回的相关数据
     * <AUTHOR>
     * @date 2024/11/4 13:11
     */
    private void getRelateInfo08(List<Map<String, String>> returnList) {
//        10乘车人/乘机人、5复核人、11收款人、9收集人

        Map<String, String> map = new HashMap<>(4);
        Map<String, String> map3 = new HashMap<>(4);
        Map<String, String> map4 = new HashMap<>(4);
        map.put("value", "5");
        map.put("label", "复核人");
        map3.put("value", "10");
        map3.put("label", "乘车人/乘机人");
        map4.put("value", "11");
        map4.put("label", "收款人");
        returnList.add(map);
        returnList.add(map3);
        returnList.add(map4);
    }

    /**
     * @description 返回的相关数据--银行回单和银行流水  会计账簿 会计资料 和其他会计资料
     * <AUTHOR>
     * @date 2024/11/4 13:11
     */
    private void getRelateInfo06(List<Map<String, String>> returnList) {
//        9收集人
        Map<String, String> map = new HashMap<>(4);
        map.put("value", "9");
        map.put("label", "收集人");
        returnList.add(map);
    }


}
