package com.aisino.da.bbs.ms.integrityrule;

import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Ims;
import com.aisino.aosplus.plugin.ms.annotation.Ms;
import com.aisino.da.bbs.entity.IntegrityConditionEnum;
import com.aisino.da.bbs.entity.IntegrityElementEnum;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName IntegrityConditionComboxMs
 * @Description 完整性规则设置-检查要素-条件下拉框服务
 * <AUTHOR>
 * @Date 2023/10/18 17:13
 */
@Ms(value = "da.bbs.IntegrityConditionComboxMs" ,desp = "完整性规则设置-检查要素-条件服务")
public class IntegrityConditionComboxMs implements Ims {

    @Override
    public Object doMessage(Object... objects) {
        Params params = (Params)objects[0];
        Map formData = params.getMap("formData");
        String celement = CollectionUtil.getStringFromMap(formData, "celement");
        String detect = CollectionUtil.getStringFromMap(formData, "detectitem");

        // todo
        //  包含要素 等于单据  条件为单据类型和文件格式
        //  包含要素 等于发票  条件为文件格式，发票类型,发票，明细，合计金额
        //  包含要素 等于回单  条件为文件格式
        // 返回自定义数据
        List<Map<String, Object>> conditionEnumList = IntegrityConditionEnum.getConditionEnumList();
        if(StringUtil.isNotEmpty(celement)) {
            conditionEnumList = conditionEnumList.stream().filter(map -> {
                if (celement.equals(IntegrityElementEnum.e1.getElement())) {
                    return CollectionUtil.getStringFromMap(map, "value").equals(IntegrityConditionEnum.c4.getValue()) ||
                            CollectionUtil.getStringFromMap(map, "value").equals(IntegrityConditionEnum.c2.getValue());
                } else if (celement.equals(IntegrityElementEnum.e2.getElement())) {
                    return !CollectionUtil.getStringFromMap(map, "value").equals(IntegrityConditionEnum.c4.getValue())&&
                            !CollectionUtil.getStringFromMap(map, "value").equals(IntegrityConditionEnum.c6.getValue());
                } else {
                    return CollectionUtil.getStringFromMap(map, "value").equals(IntegrityConditionEnum.c2.getValue())||
                            CollectionUtil.getStringFromMap(map, "value").equals(IntegrityConditionEnum.c6.getValue());
                }
            }).collect(Collectors.toList());
        }

        // todo 检测为文件只需要文件格式，为元数据，则不需要文件格式
        if(StringUtil.isNotEmpty(detect)){
            if("2".equals(detect)){
                conditionEnumList = conditionEnumList.stream().filter(map -> {
                    String value = CollectionUtil.getStringFromMap(map, "value");
                    return IntegrityConditionEnum.c2.getValue().equals(value);
                }).collect(Collectors.toList());
            }else {
                conditionEnumList = conditionEnumList.stream().filter(map -> {
                    String value = CollectionUtil.getStringFromMap(map, "value");
                    return !IntegrityConditionEnum.c2.getValue().equals(value);
                }).collect(Collectors.toList());
            }
        }
        return conditionEnumList;
    }
}
