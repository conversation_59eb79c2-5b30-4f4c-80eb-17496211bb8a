FROM 172.16.1.214:31104/aps/openjdk:8u322-jdk-bullseye-arthas as builder
WORKDIR application
LABEL "SPRING_BUILD_CACHE"="true"
ARG JAR_FILE=*.jar
COPY ${JAR_FILE} application.jar
RUN java -Djarmode=layertools -jar application.jar extract

FROM 172.16.1.214:31104/aps/openjdk:8u322-jdk-bullseye-arthas
WORKDIR application
COPY entrypoint.sh ./
COPY --from=builder application/dependencies/ ./
COPY --from=builder application/spring-boot-loader/ ./
COPY --from=builder application/aosplus/ ./
COPY --from=builder application/aps/ ./
COPY --from=builder application/app/ ./
COPY --from=builder application/snapshot-dependencies/ ./
COPY --from=builder application/application/ ./
ENTRYPOINT ["./entrypoint.sh"]